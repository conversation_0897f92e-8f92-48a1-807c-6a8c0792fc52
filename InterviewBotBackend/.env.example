# Application Settings
ENV=dev

APP_NAME=
DEBUG=
PORT=




DB_HOST=
DB_PORT=
DB_USER=
DB_PASSWORD=
DB_NAME=


# JWT Settings
JWT_SECRET_KEY=
JWT_ALGORITHM=
ACCESS_TOKEN_EXPIRE_MINUTES=

# Google OAuth Settings
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=

# Frontend Redirect URLs for OAuth
FRONTEND_SUCCESS_REDIRECT_URL=http://localhost:3000/dashboard
FRONTEND_ERROR_REDIRECT_URL=http://localhost:3000/login

#AWS S3
AWS_ACCESS_KEY_ID =
AWS_SECRET_ACCESS_KEY =
AWS_REGION =
S3_BUCKET_NAME =

# Admin Settings
ADMIN_EMAIL=
ADMIN_PASSWORD=

# LiveKit Settings
LIVEKIT_URL=
LIVEKIT_API_SECRET=
LIVEKIT_API_KEY=

MCP_SSE_URL=http://localhost:8000/sse

# Email Configuration
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM=
MAIL_PORT=587
MAIL_SERVER=
MAIL_FROM_NAME=EvalFast
MAIL_STARTTLS=true
MAIL_SSL_TLS=false
USE_CREDENTIALS=true
VALIDATE_CERTS=true

# OTP Configuration
OTP_EXPIRY_MINUTES=10
OTP_LENGTH=6


RAZORPAY_KEY_ID=
RAZORPAY_KEY_SECRET=
RAZORPAY_WEBHOOK_SECRET=