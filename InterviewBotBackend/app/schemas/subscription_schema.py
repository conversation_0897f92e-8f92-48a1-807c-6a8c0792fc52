from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum


class PlanType(str, Enum):
    individual = "individual"
    organization = "organization"


class SubscriptionPlanBase(BaseModel):
    name: str = Field(..., description="Name of the subscription plan")
    description: Optional[str] = Field(None, description="Description of the subscription plan")
    plan_type: PlanType = Field(..., description="Type of plan: individual or organization")

    # Common limits for both individual and organization
    interview_limit: int = Field(..., ge=0, description="Maximum number of interviews allowed")
    jobs_limit: int = Field(..., ge=0, description="Maximum number of jobs allowed")
    jd_limit: int = Field(..., ge=0, description="Maximum number of job descriptions allowed")

    # Individual-specific limits
    resume_limit: Optional[int] = Field(None, ge=0, description="Maximum number of resumes allowed (individual only)")

    # Organization-specific limits
    candidate_suitability_limit: Optional[int] = Field(None, ge=0, description="Maximum number of candidate suitability checks allowed (organization only)")

    price: Optional[Decimal] = Field(default=0.00, ge=0, description="Price of the subscription plan")
    is_active: bool = Field(default=True, description="Whether the plan is active")
    features: Optional[Dict[str, Any]] = Field(None, description="Additional features for the plan")


class SubscriptionPlanCreate(SubscriptionPlanBase):
    pass


class SubscriptionPlanUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Name of the subscription plan")
    description: Optional[str] = Field(None, description="Description of the subscription plan")
    plan_type: Optional[PlanType] = Field(None, description="Type of plan: individual or organization")

    # Common limits for both individual and organization
    interview_limit: Optional[int] = Field(None, ge=0, description="Maximum number of interviews allowed")
    jobs_limit: Optional[int] = Field(None, ge=0, description="Maximum number of jobs allowed")
    jd_limit: Optional[int] = Field(None, ge=0, description="Maximum number of job descriptions allowed")

    # Individual-specific limits
    resume_limit: Optional[int] = Field(None, ge=0, description="Maximum number of resumes allowed (individual only)")

    # Organization-specific limits
    candidate_suitability_limit: Optional[int] = Field(None, ge=0, description="Maximum number of candidate suitability checks allowed (organization only)")

    price: Optional[Decimal] = Field(None, ge=0, description="Price of the subscription plan")
    is_active: Optional[bool] = Field(None, description="Whether the plan is active")
    features: Optional[Dict[str, Any]] = Field(None, description="Additional features for the plan")


class SubscriptionPlanResponse(SubscriptionPlanBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserSubscriptionResponse(BaseModel):
    id: str
    user_id: str
    subscription_plan_id: str

    # Usage tracking
    interviews_used: int
    jobs_used: int
    jd_used: int
    resume_used: int

    # Remaining calculations
    interviews_remaining: int
    jobs_remaining: int
    jd_remaining: int
    resume_remaining: int

    activated_at: datetime
    expires_at: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    subscription_plan: SubscriptionPlanResponse

    class Config:
        from_attributes = True


class OrganizationSubscriptionResponse(BaseModel):
    id: str
    organization_id: str
    subscription_plan_id: str

    # Usage tracking
    interviews_used: int
    jobs_used: int
    jd_used: int
    candidate_suitability_used: int

    # Remaining calculations
    interviews_remaining: int
    jobs_remaining: int
    jd_remaining: int
    candidate_suitability_remaining: int

    activated_at: datetime
    expires_at: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    subscription_plan: SubscriptionPlanResponse

    class Config:
        from_attributes = True


class SubscriptionUsageResponse(BaseModel):
    # Interview usage
    interviews_used: int
    interviews_remaining: int
    interview_limit: int

    # Jobs usage
    jobs_used: int
    jobs_remaining: int
    jobs_limit: int

    # Job descriptions usage
    jd_used: int
    jd_remaining: int
    jd_limit: int

    # Individual-specific usage
    resume_used: Optional[int] = None
    resume_remaining: Optional[int] = None
    resume_limit: Optional[int] = None

    # Organization-specific usage
    candidate_suitability_used: Optional[int] = None
    candidate_suitability_remaining: Optional[int] = None
    candidate_suitability_limit: Optional[int] = None

    plan_name: str
    plan_type: PlanType

    # Limit checks
    is_interview_limit_reached: bool
    is_jobs_limit_reached: bool
    is_jd_limit_reached: bool
    is_resume_limit_reached: Optional[bool] = None
    is_candidate_suitability_limit_reached: Optional[bool] = None

    # Capability checks
    can_schedule_interview: bool
    can_apply_to_job: bool
    can_create_jd: bool
    can_create_resume: Optional[bool] = None
    can_perform_candidate_suitability: Optional[bool] = None

    # Backward compatibility
    is_limit_reached: bool


class SubscriptionStatsResponse(BaseModel):
    total_users: int
    total_organizations: int
    total_active_subscriptions: int
    total_plans: int
    usage_by_plan: Dict[str, Dict[str, Any]]


class UserSubscriptionCreateRequest(BaseModel):
    user_id: str
    subscription_plan_id: str


class OrganizationSubscriptionCreateRequest(BaseModel):
    organization_id: str
    subscription_plan_id: str


class BulkSubscriptionUpdateRequest(BaseModel):
    user_ids: list[str]
    subscription_plan_id: str


class BulkOrganizationSubscriptionUpdateRequest(BaseModel):
    organization_ids: list[str]
    subscription_plan_id: str