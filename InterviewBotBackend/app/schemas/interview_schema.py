from pydantic import BaseModel, EmailStr, validator, Field
from typing import List, Optional
from datetime import datetime, timezone, time


# Base Schema for shared attributes
class InterviewBase(BaseModel):
    candidate_name: Optional[str] = None
    candidate_email: Optional[EmailStr] = None
    skill_sets: List[str]
    job_role: str
    experience_level: int
    available_from: Optional[datetime] = Field(
        default=None, description="Start of availability window (must be in UTC)"
    )
    available_until: Optional[datetime] = Field(
        default=None, description="End of availability window (must be in UTC)"
    )
    interview_duration: int = Field(default=30, description="Duration of interview in minutes")
    resume_link: str
    jd_link: Optional[str] = None
    jd_id: Optional[str] = Field(
        default=None, description="ID of standalone job description (for organizations only)"
    )

    # Validate and ensure timestamp has timezone info
    @validator( "available_from", "available_until")
    def ensure_timezone(cls, v):
        if v is not None and v.tzinfo is None:
            raise ValueError("Timestamp must include timezone information")
        return v.astimezone(timezone.utc) if v is not None else v  # Convert to UTC for storage

    @validator("skill_sets")
    def validate_skill_sets(cls, v):
        if not v or len(v) == 0:
            raise ValueError("At least one skill set is required")
        return v

    @validator("interview_duration")
    def validate_duration(cls, v):
        if v <= 0:
            raise ValueError("Interview duration must be positive")
        return v

    class Config:
        # This helps Pydantic handle datetime objects with timezone info
        json_encoders = {datetime: lambda dt: dt.isoformat()}


# Schema for creating interviews
class InterviewCreate(InterviewBase):
    pass


# Schema for creating interviews from job applications
class InterviewCreateFromApplication(BaseModel):
    """Schema for scheduling interviews from job applications.
    
    All candidate details, skill sets, job role, and resume/JD links
    are automatically populated from the application and job posting.
    """
    experience_level: int = Field(description="Experience level required for the position")
    available_from: Optional[datetime] = Field(
        default=None, description="Start of availability window (must be in UTC)"
    )
    available_until: Optional[datetime] = Field(
        default=None, description="End of availability window (must be in UTC)"
    )
    interview_duration: int = Field(default=30, description="Duration of interview in minutes")

    # Validate and ensure timestamp has timezone info
    @validator("available_from", "available_until")
    def ensure_timezone(cls, v):
        if v is not None and v.tzinfo is None:
            raise ValueError("Timestamp must include timezone information")
        return v.astimezone(timezone.utc) if v is not None else v  # Convert to UTC for storage

    @validator("interview_duration")
    def validate_duration(cls, v):
        if v <= 0:
            raise ValueError("Interview duration must be positive")
        return v

    class Config:
        # This helps Pydantic handle datetime objects with timezone info
        json_encoders = {datetime: lambda dt: dt.isoformat()}
        schema_extra = {
            "example": {
                "experience_level": 3,
                "available_from": "2025-06-15T10:00:00Z",
                "available_until": "2025-06-15T12:00:00Z",
                "interview_duration": 60
            }
        }


# Schema for updating interviews
class InterviewUpdate(BaseModel):
    available_from: Optional[datetime] = Field(
        default=None, description="Start of availability window (must be in UTC)"
    )
    available_until: Optional[datetime] = Field(
        default=None, description="End of availability window (must be in UTC)"
    )
    interview_duration: Optional[int] = Field(
        default=None, description="Duration of interview in minutes"
    )
    interview_agenda: Optional[List[str]] = Field(
        default=None, description="List of agenda items for the interview"
    )
    interview_questions: Optional[List[dict]] = Field(
        default=None, description="List of question sections with related questions"
    )

    # Validate and ensure timestamp has timezone info and is in UTC
    @validator("available_from", "available_until")
    def ensure_utc_timezone(cls, v):
        if v is not None:
            if v.tzinfo is None:
                raise ValueError("Timestamp must include timezone information")
            if v.tzinfo != timezone.utc:
                raise ValueError("Timestamp must be in UTC timezone")
            return v  # Already in UTC, no conversion needed
        return v

    @validator("interview_duration")
    def validate_duration(cls, v):
        if v is not None and v <= 0:
            raise ValueError("Interview duration must be positive")
        return v

    @validator("interview_questions")
    def validate_questions(cls, v):
        if v is not None:
            # Check if questions format is valid
            for item in v:
                if not isinstance(item, dict):
                    raise ValueError("Each question item must be a dictionary")
                if "agenda" not in item or "questions" not in item:
                    raise ValueError("Each question item must have 'agenda' and 'questions' keys")
                if not isinstance(item["questions"], list):
                    raise ValueError("Questions field must be a list")
        return v

    class Config:
        json_encoders = {datetime: lambda dt: dt.isoformat()}


class InterviewCompletionUpdate(BaseModel):
    interview_transcript: str
    interview_violation: str

    class Config:
        schema_extra = {
            "example": {
                "interview_transcript": "Full transcript of the interview…",
                "interview_violation":  "Multiple faces detected; No face detected"
            }
        }


# Schema for returning interviews
class InterviewResponse(InterviewBase):
    id: str
    organization_id: str
    resume_path: Optional[str] = None
    is_completed: bool = False
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


# Schema for listing interviews
class InterviewList(BaseModel):
    id: str
    candidate_name: str
    candidate_email: EmailStr
    job_role: str
    available_from: Optional[datetime] = None
    available_until: Optional[datetime] = None
    interview_duration: int
    is_completed: bool

    class Config:
        orm_mode = True
