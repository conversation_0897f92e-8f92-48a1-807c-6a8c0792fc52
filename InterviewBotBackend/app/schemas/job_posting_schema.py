from typing import List, Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime
from app.models.enums import WorkplaceType, EmploymentType, JobStatus, QuestionType


class ScreeningQuestion(BaseModel):
    question: str = Field(..., description="The screening question text")
    type: QuestionType = Field(..., description="Type of question (text or multiple_choice)")
    required: bool = Field(default=False, description="Whether this question is required")
    options: Optional[List[str]] = Field(None, description="Options for multiple choice questions")

    class Config:
        use_enum_values = False


class JobPostingCreateSchema(BaseModel):
    job_title: str = Field(..., description="Job title")
    workplace_type: WorkplaceType = Field(..., description="Workplace type (remote/hybrid/on_site)")
    job_location: str = Field(..., description="Job location")
    employment_type: EmploymentType = Field(..., description="Employment type")
    job_role: str = Field(..., description="Job role (must exist in organization profile)")
    jd_link: Optional[str] = Field(None, description="S3 URL to job description file")
    jd_id: Optional[str] = Field(None, description="ID of standalone job description")
    required_skills: List[str] = Field(..., description="List of required skills")
    screening_questions: Optional[List[ScreeningQuestion]] = Field(None, description="Optional screening questions")

    @validator('jd_id')
    def validate_jd_reference(cls, v, values):
        jd_link = values.get('jd_link')
        jd_id = v
        
        if not jd_link and not jd_id:
            raise ValueError("Either jd_link or jd_id must be provided")
        if jd_link and jd_id:
            raise ValueError("Only one of jd_link or jd_id should be provided")
        return v

    class Config:
        # This tells Pydantic to convert WorkplaceType and EmploymentType (and any nested enums) into their .value
        use_enum_values = False


class JobPostingUpdateSchema(BaseModel):
    job_title: Optional[str] = Field(None, description="Job title")
    workplace_type: Optional[WorkplaceType] = Field(None, description="Workplace type")
    job_location: Optional[str] = Field(None, description="Job location")
    employment_type: Optional[EmploymentType] = Field(None, description="Employment type")
    job_role: Optional[str] = Field(None, description="Job role")
    jd_link: Optional[str] = Field(None, description="S3 URL to job description file")
    jd_id: Optional[str] = Field(None, description="ID of standalone job description")
    required_skills: Optional[List[str]] = Field(None, description="Required skills")
    screening_questions: Optional[List[ScreeningQuestion]] = Field(None, description="Screening questions")


class JobPostingResponseSchema(BaseModel):
    id: str
    organization_id: str
    job_title: str
    company_name: str
    workplace_type: WorkplaceType
    job_location: str
    employment_type: EmploymentType
    job_role: Optional[str] = None
    jd_link: Optional[str] = None
    jd_id: Optional[str] = None
    required_skills: List[str]
    status: JobStatus
    screening_questions: Optional[List[ScreeningQuestion]] = None
    company_location: Optional[str] = None
    company_website: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class JobAnalyticsSchema(BaseModel):
    total_applications: int = Field(..., description="Total number of applications")
    interviews_scheduled: int = Field(..., description="Number of interviews scheduled")
    interviews_completed: int = Field(..., description="Number of interviews completed")
    pending_applications: int = Field(..., description="Number of pending applications")


class JobSearchFiltersSchema(BaseModel):
    job_title: Optional[str] = Field(None, description="Filter by job title")
    location: Optional[str] = Field(None, description="Filter by location")
    workplace_type: Optional[str] = Field(None, description="Filter by workplace type")
    employment_type: Optional[str] = Field(None, description="Filter by employment type")
    skills: Optional[List[str]] = Field(None, description="Filter by required skills")
    company_name: Optional[str] = Field(None, description="Filter by company name")


class OrganizationProfileSchema(BaseModel):
    name: str = Field(..., description="Organization name")
    profile_image: Optional[str] = Field(None, description="Organization profile image URL")


class JobPostingBrowseResponseSchema(BaseModel):
    id: str
    organization_id: str
    job_title: str
    company_name: str
    workplace_type: WorkplaceType
    job_location: str
    employment_type: EmploymentType
    job_role: Optional[str] = None
    jd_link: Optional[str] = None
    jd_id: Optional[str] = None
    required_skills: List[str]
    status: JobStatus
    screening_questions: Optional[List[ScreeningQuestion]] = None
    company_location: Optional[str] = None
    company_website: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    organization_profile: OrganizationProfileSchema

    class Config:
        from_attributes = True


class JobPostingListResponseSchema(BaseModel):
    jobs: List[JobPostingResponseSchema]
    total: int
    page: int
    per_page: int
    total_pages: int
