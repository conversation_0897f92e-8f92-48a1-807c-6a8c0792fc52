from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr, Field, HttpUrl, field_validator
from datetime import datetime
from enum import Enum
from typing import Union


class RegistrationType(str, Enum):
    individual = "individual"
    organization = "organization"
    admin = "admin"


# Base schema for common user fields
class RegisterBase(BaseModel):
    email: EmailStr
    full_name: str


# Schema for creating a new user (with password)
class RegistrationCreate(RegisterBase):
    password: str
    registration_type: RegistrationType
    referral_code: Optional[str] = Field(None, min_length=12, max_length=12, description="Optional referral code")

    @field_validator('referral_code', mode='before')
    @classmethod
    def validate_referral_code(cls, v):
        if v is None or v == "":
            return None

        if not isinstance(v, str):
            raise ValueError('Referral code must be a string')

        # Remove whitespace and convert to uppercase
        normalized = v.strip().upper()

        if len(normalized) != 12:
            raise ValueError('Referral code must be exactly 12 characters')

        return normalized


# Schema for user response (what's returned to the client)
class RegisterResponse(RegisterBase):
    id: str
    created_at: datetime
    updated_at: datetime


# Schema for token response (only access token)
class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"  # Default to "bearer" since it's standard


class UserResponse(RegisterBase):
    id: str
    full_name: str
    email: EmailStr
    created_at: datetime
    updated_at: datetime


# Employment History Schemas
class EmploymentEntry(BaseModel):
    id: Optional[str] = Field(None, description="Unique identifier for the employment entry")
    company: str = Field(..., description="Company name")
    position: str = Field(..., description="Job title/position")
    start_date: str = Field(..., description="Start date in YYYY-MM format")
    end_date: Optional[str] = Field(None, description="End date in YYYY-MM format (null if current)")
    current: bool = Field(False, description="Whether this is the current job")
    description: Optional[str] = Field(None, description="Job description and responsibilities")
    location: Optional[str] = Field(None, description="Job location (City, Country)")

    @field_validator('end_date')
    def validate_end_date(cls, v, info):
        values = info.data
        current = values.get('current', False)
        
        # If current is True, end_date should be None
        if current and v is not None:
            raise ValueError("End date should be null for current employment")
        
        # If current is False, end_date should be provided
        if not current and v is None:
            raise ValueError("End date is required for past employment")
        
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "company": "Google",
                "position": "Senior Software Engineer",
                "start_date": "2022-03",
                "end_date": "2024-01",
                "current": False,
                "description": "Led development of scalable web applications using React and Node.js. Managed a team of 5 developers.",
                "location": "Mountain View, CA"
            }
        }


# Schema for updating user profile
class UserProfileUpdate(BaseModel):
    position: Optional[str] = None
    location: Optional[str] = None
    phone_number: Optional[str] = None
    bio: Optional[str] = None
    profile_picture: Optional[str] = None
    website: Optional[str] = None
    instagram: Optional[str] = None
    tiktok: Optional[str] = None
    youtube: Optional[str] = None
    twitter: Optional[str] = None
    linkedin: Optional[str] = None
    resume_link: Optional[str] = None
    employment_history: Optional[List[EmploymentEntry]] = None
    skills: Optional[List[str]] = None

    class Config:
        json_schema_extra = {
            "example": {
                "position": "Product Design Lead",
                "location": "London, UK",
                "phone_number": "+97-6985472310",
                "bio": "Experienced product designer with 5+ years in the industry.",
                "profile_picture": "uploads/123e4567-e89b-12d3-a456-426614174000/abc123_profile.jpg",
                "website": "https://myportfolio.com",
                "instagram": "@designerhandle",
                "tiktok": "@designertiktok",
                "youtube": "@designerchannel",
                "twitter": "@designertwitter",
                "linkedin": "https://linkedin.com/in/designer",
                "resume_link": "https://bucket-name.s3.us-east-1.amazonaws.com/uploads/user-id/resume.pdf",
                "employment_history": [
                    {
                        "company": "Google",
                        "position": "Senior Software Engineer",
                        "start_date": "2022-03",
                        "end_date": "2024-01",
                        "current": False,
                        "description": "Led development of scalable web applications using React and Node.js. Managed a team of 5 developers.",
                        "location": "Mountain View, CA"
                    },
                    {
                        "company": "Microsoft",
                        "position": "Software Engineer",
                        "start_date": "2020-06",
                        "end_date": "2022-02",
                        "current": False,
                        "description": "Developed cloud-based solutions using Azure services.",
                        "location": "Seattle, WA"
                    }
                ],
                "skills": [
                    "Python",
                    "JavaScript",
                    "React",
                    "Node.js",
                    "AWS",
                    "Docker",
                    "Project Management",
                    "UI/UX Design"
                ]
            }
        }


class StandaloneJobDescription(BaseModel):
    name: str = Field(..., description="Name of the job description")
    jd_link: Optional[str] = Field(None, description="Link to the job description document (S3 URL)")
    jd_text: Optional[str] = Field(None, description="Job description text content directly")

    @field_validator('jd_link', 'jd_text')
    def validate_jd_source(cls, v, info):
        # Get the values from the model
        values = info.data

        # Ensure at least one of jd_link or jd_text is provided
        if ('jd_link' in values and values.get('jd_link') is None and
            'jd_text' in values and values.get('jd_text') is None):
            raise ValueError("Either jd_link or jd_text must be provided")
        return v


class OrganizationUpdateSkillsJobsSchema(BaseModel):
    skill_sets: Optional[List[str]] = Field(None, description="List of skill sets")
    job_roles: Optional[List[str]] = Field(None, description="List of job roles")
    standalone_job_descriptions: Optional[List[StandaloneJobDescription]] = Field(None, description="List of standalone job descriptions not tied to skill sets or job roles")


class UserUpdateJobDescriptionsSchema(BaseModel):
    standalone_job_descriptions: List[StandaloneJobDescription] = Field(..., description="List of standalone job descriptions for the user")


class TokenResponse(BaseModel):
    success: bool
    message: str
    access_token: str
    token_type: str
    admin_id: str
    email: str
    created_at: str


# Schema for past interview entry
class PastInterviewEntry(BaseModel):
    id: str = Field(..., description="Interview ID")
    company: str = Field(..., description="Company or organization name")
    position: str = Field(..., description="Job position/role")
    date: str = Field(..., description="Interview date in YYYY-MM-DD format")
    skills_tested: List[str] = Field(default=[], description="Skills that were tested")
    experience_level: str = Field(..., description="Experience level required")
    duration_minutes: int = Field(..., description="Interview duration in minutes")
    status: str = Field(..., description="Interview status")
    feedback: Optional[str] = Field(None, description="Interview feedback")
    score: Optional[float] = Field(None, description="Interview score")
    result: Optional[str] = Field(None, description="Interview result/recommendation")

    class Config:
        json_schema_extra = {
            "example": {
                "id": "interview_123",
                "company": "Google",
                "position": "Senior Software Engineer",
                "date": "2024-01-15",
                "skills_tested": ["Python", "JavaScript", "System Design"],
                "experience_level": "Senior",
                "duration_minutes": 60,
                "status": "completed",
                "feedback": "Great technical skills and problem-solving approach",
                "score": 85.5,
                "result": "recommended"
            }
        }


# Schema for public user profile data
class UserProfilePublic(BaseModel):
    id: str
    email: EmailStr
    full_name: str
    position: Optional[str] = None
    location: Optional[str] = None
    bio: Optional[str] = None
    website: Optional[str] = None
    profile_picture: Optional[str] = None
    instagram: Optional[str] = None
    tiktok: Optional[str] = None
    youtube: Optional[str] = None
    twitter: Optional[str] = None
    linkedin: Optional[str] = None
    resume_link: Optional[str] = None
    employment_history: Optional[List[EmploymentEntry]] = None
    skills: Optional[List[str]] = None
    past_interviews: Optional[List[PastInterviewEntry]] = None
    rating: float
    created_at: str

    class Config:
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "position": "Product Design Lead",
                "location": "London, UK",
                "bio": "Experienced product designer with 5+ years in the industry.",
                "website": "https://myportfolio.com",
                "profile_picture": "uploads/123e4567-e89b-12d3-a456-426614174000/abc123_profile.jpg",
                "instagram": "@designerhandle",
                "tiktok": "@designertiktok",
                "youtube": "@designerchannel",
                "twitter": "@designertwitter",
                "linkedin": "https://linkedin.com/in/designer",
                "resume_link": "https://bucket-name.s3.us-east-1.amazonaws.com/uploads/user-id/resume.pdf",
                "employment_history": [
                    {
                        "company": "Google",
                        "position": "Senior Software Engineer",
                        "start_date": "2022-03",
                        "end_date": "2024-01",
                        "current": False,
                        "description": "Led development of scalable web applications using React and Node.js.",
                        "location": "Mountain View, CA"
                    },
                    {
                        "company": "Microsoft",
                        "position": "Software Engineer",
                        "start_date": "2020-06",
                        "end_date": "2022-02",
                        "current": False,
                        "description": "Developed cloud-based solutions using Azure services.",
                        "location": "Seattle, WA"
                    }
                ],
                "skills": [
                    "Python",
                    "JavaScript",
                    "React",
                    "Node.js",
                    "AWS",
                    "Docker",
                    "Project Management",
                    "UI/UX Design"
                ],
                "past_interviews": [
                    {
                        "id": "interview_123",
                        "company": "TechCorp",
                        "position": "Senior Frontend Developer",
                        "date": "2024-01-15",
                        "skills_tested": ["React", "JavaScript", "CSS"],
                        "experience_level": "Senior",
                        "duration_minutes": 60,
                        "status": "completed",
                        "feedback": "Excellent technical skills and communication",
                        "score": 88.5,
                        "result": "recommended"
                    },
                    {
                        "id": "interview_456",
                        "company": "StartupXYZ",
                        "position": "Full Stack Developer",
                        "date": "2023-12-10",
                        "skills_tested": ["Node.js", "Python", "Database Design"],
                        "experience_level": "Mid-level",
                        "duration_minutes": 45,
                        "status": "completed",
                        "feedback": "Good problem-solving approach",
                        "score": 82.0,
                        "result": "recommended"
                    }
                ],
                "skill_set": ["Blockchain", "AI", "Python", "JavaScript"],
                "rating": 4.5,
                "created_at": "2024-04-04T12:34:56Z",
            }
        }


# Schema for social media links grouped together
class SocialMediaLinks(BaseModel):
    instagram: Optional[str] = None
    tiktok: Optional[str] = None
    youtube: Optional[str] = None
    twitter: Optional[str] = None
    linkedin: Optional[str] = None


# Schema for pagination metadata (matching interview endpoints pattern)
class ProfilePaginationMetadata(BaseModel):
    total_users: int
    total_pages: int
    current_page: int
    current_page_length: int
    page_size: int

    class Config:
        json_schema_extra = {
            "example": {
                "total_users": 150,
                "total_pages": 15,
                "current_page": 3,
                "current_page_length": 10,
                "page_size": 10
            }
        }


# Schema for public organization profile data
class OrganizationProfilePublic(BaseModel):
    id: str
    email: EmailStr
    organization_name: str
    position: Optional[str] = None
    location: Optional[str] = None
    bio: Optional[str] = None
    website: Optional[str] = None
    profile_picture: Optional[str] = None
    instagram: Optional[str] = None
    tiktok: Optional[str] = None
    youtube: Optional[str] = None
    twitter: Optional[str] = None
    linkedin: Optional[str] = None
    total_interviews: int
    created_at: str

    class Config:
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "email": "<EMAIL>",
                "organization_name": "Acme Corp",
                "position": "Tech Company",
                "location": "San Francisco, CA",
                "bio": "Leading technology company specializing in AI solutions.",
                "website": "https://company.com",
                "profile_picture": "uploads/organizations/123e4567-e89b-12d3-a456-426614174000/logo.jpg",
                "instagram": "@companyhandle",
                "tiktok": "@companytiktok",
                "youtube": "@companychannel",
                "twitter": "@companytwitter",
                "linkedin": "https://linkedin.com/company/companyname",
                "total_interviews": 125,
                "created_at": "2024-04-04T12:34:56Z"
            }
        }


# Schema for organization pagination metadata
class OrganizationPaginationMetadata(BaseModel):
    total_organizations: int
    total_pages: int
    current_page: int
    current_page_length: int
    page_size: int

    class Config:
        json_schema_extra = {
            "example": {
                "total_organizations": 75,
                "total_pages": 8,
                "current_page": 2,
                "current_page_length": 10,
                "page_size": 10
            }
        }


# Schema for the complete paginated organizations response
class PaginatedOrganizationsResponse(BaseModel):
    status: str
    message: str
    data: List[OrganizationProfilePublic]
    pagination: OrganizationPaginationMetadata

    class Config:
        json_schema_extra = {
            "example": {
                "status": "success",
                "message": "Organizations retrieved successfully",
                "data": [
                    {
                        "id": "123e4567-e89b-12d3-a456-426614174000",
                        "email": "<EMAIL>",
                        "organization_name": "Acme Corp",
                        "position": "Tech Company",
                        "location": "San Francisco, CA",
                        "bio": "Leading technology company specializing in AI solutions.",
                        "website": "https://company.com",
                        "profile_picture": "uploads/organizations/123e4567-e89b-12d3-a456-426614174000/logo.jpg",
                        "instagram": "@companyhandle",
                        "tiktok": "@companytiktok",
                        "youtube": "@companychannel",
                        "twitter": "@companytwitter",
                        "linkedin": "https://linkedin.com/company/companyname",
                        "total_interviews": 125,
                        "created_at": "2024-04-04T12:34:56Z"
                    }
                ],
                "pagination": {
                    "total_organizations": 75,
                    "total_pages": 8,
                    "current_page": 2,
                    "current_page_length": 10,
                    "page_size": 10
                }
            }
        }


# Schema for the complete paginated user profiles response (matching interview endpoints pattern)
class PaginatedUserProfilesResponse(BaseModel):
    status: str
    message: str
    data: List[UserProfilePublic]
    pagination: ProfilePaginationMetadata

    class Config:
        json_schema_extra = {
            "example": {
                "status": "success",
                "message": "User profiles retrieved successfully",
                "data": [
                    {
                        "id": "123e4567-e89b-12d3-a456-426614174000",
                        "email": "<EMAIL>",
                        "full_name": "John Doe",
                        "position": "Product Design Lead",
                        "location": "London, UK",
                        "bio": "Experienced product designer with 5+ years in the industry.",
                        "website": "https://myportfolio.com",
                        "profile_picture": "uploads/123e4567-e89b-12d3-a456-426614174000/abc123_profile.jpg",
                        "instagram": "@designerhandle",
                        "tiktok": "@designertiktok",
                        "youtube": "@designerchannel",
                        "twitter": "@designertwitter",
                        "linkedin": "https://linkedin.com/in/designer",
                        "employment_history": [
                            {
                                "company": "Google",
                                "position": "Senior Software Engineer",
                                "start_date": "2022-03",
                                "end_date": "2024-01",
                                "current": False,
                                "description": "Led development of scalable web applications using React and Node.js.",
                                "location": "Mountain View, CA"
                            }
                        ],
                        "skills": [
                            "Python",
                            "JavaScript",
                            "React",
                            "Node.js",
                            "AWS",
                            "Docker"
                        ],
                        "past_interviews": [
                            {
                                "id": "interview_789",
                                "company": "TechCorp",
                                "position": "Senior Developer",
                                "date": "2024-01-20",
                                "skills_tested": ["Python", "JavaScript"],
                                "experience_level": "Senior",
                                "duration_minutes": 60,
                                "status": "completed",
                                "feedback": "Strong technical performance",
                                "score": 85.0,
                                "result": "recommended"
                            }
                        ],
                        "skill_sets": ["Blockchain", "AI", "Python", "JavaScript"],
                        "rating": 4.5,
                        "created_at": "2024-04-04T12:34:56Z"
                    }
                ],
                "pagination": {
                    "total_users": 150,
                    "total_pages": 15,
                    "current_page": 3,
                    "current_page_length": 10,
                    "page_size": 10
                }
            }
        }
