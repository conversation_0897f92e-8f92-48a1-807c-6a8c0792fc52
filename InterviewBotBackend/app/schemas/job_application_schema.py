from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
from app.models.enums import ApplicationStatus


class ScreeningAnswer(BaseModel):
    question: str = Field(..., description="The screening question")
    answer: str = Field(..., description="The applicant's answer")


class JobApplicationCreateSchema(BaseModel):
    cover_letter: Optional[str] = Field(None, description="Optional cover letter")
    resume_link: Optional[str] = Field(None, description="S3 URL to resume")
    screening_answers: Optional[List[ScreeningAnswer]] = Field(None, description="Answers to screening questions")


class JobApplicationUpdateSchema(BaseModel):
    cover_letter: Optional[str] = Field(None, description="Cover letter")
    resume_link: Optional[str] = Field(None, description="Resume S3 URL")
    screening_answers: Optional[List[ScreeningAnswer]] = Field(None, description="Screening answers")


class JobApplicationStatusUpdateSchema(BaseModel):
    status: ApplicationStatus = Field(..., description="New application status")

    class Config:
        use_enum_values = False


class JobApplicationResponseSchema(BaseModel):
    id: str
    job_posting_id: str
    user_id: str
    cover_letter: Optional[str] = None
    resume_link: Optional[str] = None
    screening_answers: Optional[List[ScreeningAnswer]] = None
    status: ApplicationStatus
    applied_at: datetime
    updated_at: datetime
    
    # Computed fields
    applicant_name: Optional[str] = None
    applicant_email: Optional[str] = None
    job_title: Optional[str] = None
    company_name: Optional[str] = None

    class Config:
        from_attributes = True
        use_enum_values = True


class JobApplicationListResponseSchema(BaseModel):
    applications: List[JobApplicationResponseSchema]
    total: int
    page: int
    per_page: int
    total_pages: int


class JobApplicationWithJobDetailsSchema(BaseModel):
    id: str
    job_posting_id: str
    cover_letter: Optional[str] = None
    resume_link: Optional[str] = None
    screening_answers: Optional[List[ScreeningAnswer]] = None
    status: ApplicationStatus
    applied_at: datetime
    updated_at: datetime
    
    # Job details
    job_title: str
    company_name: str
    workplace_type: str
    job_location: str
    employment_type: str

    class Config:
        from_attributes = True
        use_enum_values = True


class UserApplicationsResponseSchema(BaseModel):
    applications: List[JobApplicationWithJobDetailsSchema]
    total: int
    page: int
    per_page: int
    total_pages: int


class ApplicationStatusCheckSchema(BaseModel):
    job_id: str
    user_id: str
    has_applied: bool
    application_id: Optional[str] = None
    application_status: Optional[str] = None
    applied_at: Optional[str] = None
    job_title: str
    company_name: str
    job_status: str

    class Config:
        from_attributes = True


class ApplicationStatusResponseSchema(BaseModel):
    status: str
    message: str
    data: ApplicationStatusCheckSchema

    class Config:
        from_attributes = True