from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

# Import the detailed resume models from MCP tool schema
from app.schemas.mcp_tool_schema import (
    BuildResumeSchema,
    ContactInfo,
    WorkExperience,
    Education,
    Project,
    ResearchExperience
)


class ResumeTemplate(str, Enum):
    professional = "professional"
    modern = "modern"
    academic = "academic"


class ResumeCreate(BaseModel):
    title: str = Field(..., description="Title/name for the resume")
    content: BuildResumeSchema = Field(..., description="Resume content in structured format")

    class Config:
        json_schema_extra = {
            "example": {
                "title": "Software Engineer Resume",
                "content": {
                    "template_id": "professional",
                    "contact_info": {
                        "name": "<PERSON>",
                        "email": "<EMAIL>",
                        "phone": "******-0123",
                        "location": "San Francisco, CA",
                        "linkedin": "linkedin.com/in/johndoe",
                        "website": "johndoe.dev"
                    },
                    "professional_summary": "Experienced Software Engineer with 8+ years of experience...",
                    "work_experience": [
                        {
                            "company": "Google",
                            "position": "Senior Software Engineer",
                            "start_date": "2020-01",
                            "end_date": "2024-01",
                            "description": "Led development of scalable web applications..."
                        }
                    ],
                    "education": [
                        {
                            "institution": "Stanford University",
                            "degree": "Bachelor of Science in Computer Science",
                            "graduation_date": "2016-06"
                        }
                    ],
                    "skills": ["Python", "JavaScript", "React", "Node.js", "AWS"],
                    "certifications": ["AWS Certified Solutions Architect"]
                }
            }
        }


class ResumeResponse(BaseModel):
    id: str
    user_id: str
    title: str
    template: str
    content: BuildResumeSchema
    pdf_s3_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        json_schema_extra = {
            "example": {
                "id": "resume_123",
                "user_id": "user_456",
                "title": "Software Engineer Resume",
                "template": "professional",
                "content": {
                    "contact_info": {
                        "name": "John Doe",
                        "email": "<EMAIL>"
                    }
                },
                "pdf_s3_url": "https://bucket.s3.amazonaws.com/resumes/user_456/resume_123.pdf",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z"
            }
        }


class ResumeListResponse(BaseModel):
    success: bool
    message: str
    data: List[ResumeResponse]

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Resumes retrieved successfully",
                "data": [
                    {
                        "id": "resume_123",
                        "user_id": "user_456",
                        "title": "Software Engineer Resume",
                        "template": "professional",
                        "content": {
                            "contact_info": {
                                "name": "John Doe",
                                "email": "<EMAIL>"
                            }
                        },
                        "pdf_s3_url": "https://bucket.s3.amazonaws.com/resumes/user_456/resume_123.pdf",
                        "created_at": "2024-01-15T10:30:00Z",
                        "updated_at": "2024-01-15T10:30:00Z"
                    }
                ]
            }
        }