from typing import Optional
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime


class SendOTPRequest(BaseModel):
    """Request schema for sending OTP"""
    email: EmailStr = Field(..., description="Email address to send OTP to")
    user_type: str = Field(default="candidate", description="Type of user (candidate/organization)")

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "user_type": "candidate"
            }
        }


class VerifyOTPRequest(BaseModel):
    """Request schema for verifying OTP"""
    email: EmailStr = Field(..., description="Email address")
    otp_code: str = Field(..., min_length=4, max_length=10, description="OTP code to verify")

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "otp_code": "123456"
            }
        }


class ResendOTPRequest(BaseModel):
    """Request schema for resending OTP"""
    email: EmailStr = Field(..., description="Email address to resend OTP to")

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


class EmailVerificationResponse(BaseModel):
    """Response schema for email verification operations"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    email: str = Field(..., description="Email address")
    expires_in_minutes: Optional[int] = Field(None, description="OTP expiry time in minutes")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Verification email sent successfully",
                "email": "<EMAIL>",
                "expires_in_minutes": 10
            }
        }


class OTPVerificationResponse(BaseModel):
    """Response schema for OTP verification"""
    success: bool = Field(..., description="Whether the verification was successful")
    message: str = Field(..., description="Verification message")
    email: str = Field(..., description="Email address")
    verified_at: Optional[datetime] = Field(None, description="Timestamp when email was verified")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Email verified successfully",
                "email": "<EMAIL>",
                "verified_at": "2024-01-01T12:00:00Z"
            }
        }
