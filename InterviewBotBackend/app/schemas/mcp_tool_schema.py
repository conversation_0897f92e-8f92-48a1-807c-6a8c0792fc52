# Assuming you have a file like app/schemas/tool_requests.py
from pydantic import BaseModel, Field, HttpUrl
from typing import Dict, Any, Optional, List


class ToolCallRequest(BaseModel):
    interview_id: str = Field(
        ..., description="The ID of the interview to associate the tool call with."
    )
    tool_name: str = Field(..., description="The name of the tool to execute.")
    arguments: Optional[Dict[str, Any]] = Field(
        None, description="Optional arguments for tools that don't require an interview"
    )

    # Example for OpenAPI documentation if needed
    class Config:
        json_schema_extra = {
            "example": {
                "interview_id": "interview_abc_123",
                "tool_name": "generate_interview_agenda",
                "arguments": {
                    "experience_level": "Senior",
                    "job_role": "Software Engineer",
                    "custom_prompt": "Focus on cloud technologies"
                }
            }
        }


class CandidateSuitabilityPreSchema(BaseModel):
    resume_s3_link: HttpUrl = Field(..., description="S3 link to the candidate's resume")
    job_description_s3_link: HttpUrl = Field(..., description="S3 link to the job description")

    model_config = {
        "json_schema_extra": {
            "example": {
                "resume_s3_link": "https://s3.amazonaws.com/bucket_name/resume.pdf",
                "job_description_s3_link": "https://s3.amazonaws.com/bucket_name/job_description.pdf",
            }
        }
    }


# ----------------------------
# JOB DESCRIPTION CREATION
# ----------------------------
class JobDescriptionSchema(BaseModel):
    experience_level: int = Field(
        ..., description="Experience level for the job (e.g., 1 , 2 , 3)"
    )
    job_role: str = Field(
        ..., description="The specific job role or title"
    )
    custom_prompt: Optional[str] = Field(
        None, description="Optional custom prompt to guide the job description generation"
    )


class EditJobDescriptionSchema(BaseModel):
    existing_job_description: str = Field(
        ..., description="The existing job description to be edited"
    )
    custom_prompt: str = Field(
        ..., description="Instructions on how to update the job description"
    )


# Response schemas for job descriptions
class JobResponsibility(BaseModel):
    description: str = Field(..., description="Description of a job responsibility")


class JobRequirement(BaseModel):
    description: str = Field(..., description="Description of a job requirement")


class HiringStep(BaseModel):
    description: str = Field(..., description="Description of a hiring process step")


class JobDescriptionResponse(BaseModel):
    job_title: str = Field(..., description="The title of the job position")
    company_name: str = Field(..., description="The name of the company")
    job_description: str = Field(..., description="Overview of the job role and its purpose")
    responsibilities: List[JobResponsibility] = Field(..., description="List of job responsibilities")
    requirements: List[JobRequirement] = Field(..., description="List of job requirements")
    hiring_process: List[HiringStep] = Field(..., description="Steps in the hiring process")
    pdf_url: Optional[str] = Field(None, description="S3 URL to the PDF version of the job description")


# ----------------------------
# RESUME TEMPLATES AND BUILDING
# ----------------------------
class ContactInfo(BaseModel):
    name: str = Field(..., description="Full name")
    email: str = Field(..., description="Email address")
    phone: str = Field(..., description="Phone number")
    location: str = Field(..., description="Location/Address")
    linkedin: Optional[str] = Field(None, description="LinkedIn profile URL")
    website: Optional[str] = Field(None, description="Personal website URL")


class WorkExperience(BaseModel):
    company: str = Field(..., description="Company name")
    position: str = Field(..., description="Job position/title")
    start_date: str = Field(..., description="Start date")
    end_date: str = Field(None, description="End date (leave empty for current position)")
    description: str = Field(..., description="Job description and achievements")


class Education(BaseModel):
    institution: str = Field(..., description="Educational institution name")
    degree: str = Field(..., description="Degree/qualification")
    field_of_study: Optional[str] = Field(None, description="Field of study/major")
    graduation_date: str = Field(..., description="Graduation date")
    gpa: Optional[str] = Field(None, description="GPA (optional)")


class Project(BaseModel):
    name: str = Field(..., description="Project name")
    description: str = Field(..., description="Project description")
    technologies: List[str] = Field(..., description="Technologies used")
    url: Optional[str] = Field(None, description="Project URL (optional)")


class ResearchExperience(BaseModel):
    institution: str = Field(..., description="Research institution")
    position: str = Field(..., description="Research position")
    start_date: str = Field(..., description="Start date")
    end_date: str = Field(None, description="End date (leave empty for current position)")
    description: str = Field(..., description="Research description")


class BuildResumeSchema(BaseModel):
    template_id: str = Field(..., description="Template ID (professional, modern, academic)")
    contact_info: ContactInfo = Field(..., description="Contact information")
    professional_summary: Optional[str] = Field(
        None, description="Professional summary (for professional/modern templates)"
    )
    objective: Optional[str] = Field(None, description="Career objective (for academic template)")
    work_experience: Optional[List[WorkExperience]] = Field(
        default=[], description="Work experience list"
    )
    education: List[Education] = Field(
        ..., description="Education list (required for all templates)"
    )
    skills: List[str] = Field(..., description="Skills list (required for all templates)")
    projects: Optional[List[Project]] = Field(
        default=[],
        description="Projects list (optional for professional, required for modern/academic)",
    )
    certifications: Optional[List[str]] = Field(
        default=[], description="Certifications list (optional)"
    )
    research_experience: Optional[List[ResearchExperience]] = Field(
        default=[], description="Research experience (for academic template)"
    )
    publications: Optional[List[str]] = Field(
        default=[], description="Publications list (for academic template)"
    )
    additional_instructions: Optional[str] = Field(
        None, description="Additional instructions for resume generation"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "template_id": "professional",
                "contact_info": {
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "phone": "******-0123",
                    "location": "San Francisco, CA",
                    "linkedin": "https://linkedin.com/in/johndoe",
                    "website": "https://johndoe.dev",
                },
                "professional_summary": "Experienced backend engineer with 5+ years...",
                "work_experience": [
                    {
                        "company": "Google",
                        "position": "Software Engineer",
                        "start_date": "2020-01",
                        "end_date": "2023-06",
                        "description": "Worked on scaling microservices",
                    }
                ],
                "education": [
                    {
                        "institution": "MIT",
                        "degree": "B.Tech",
                        "field_of_study": "Computer Science",
                        "graduation_date": "2019-05",
                        "gpa": "3.9",
                    }
                ],
                "skills": ["Python", "Docker", "Kubernetes"],
                "projects": [
                    {
                        "name": "AI Chatbot",
                        "description": "Built a customer support chatbot using GPT.",
                        "technologies": ["Python", "FastAPI", "OpenAI"],
                        "url": "https://github.com/johndoe/chatbot",
                    }
                ],
                "certifications": ["AWS Certified Developer"],
                "additional_instructions": "Make the summary bold and use compact layout",
            }
        }


class ResumeTemplate(BaseModel):
    id: str = Field(..., description="Template ID")
    name: str = Field(..., description="Template name")
    sections: List[str] = Field(..., description="Template sections")
    description: str = Field(..., description="Template description")


class ResumeTemplatesResponse(BaseModel):
    templates: List[ResumeTemplate] = Field(..., description="List of available resume templates")


class ResumeResponse(BaseModel):
    resume_id: str = Field(..., description="Generated resume ID")
    template_id: str = Field(..., description="Template used")
    created_at: str = Field(..., description="Creation timestamp")
    content: dict = Field(..., description="Resume content")
