from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from datetime import datetime
from app.services.livekit_service import LiveKitService
import os
from livekit import api
from app.schemas.livekit_schema import RoomDetails, TokenRequest, TokenResponse
from app.services.livekit_service import LiveKitService
from app.db.session import SessionLocal
from typing import Any, Dict, Optional
from app.core.auth_guard import BaseAuthGuard


livekit_router = APIRouter(prefix="/livekit", tags=["LiveKit"])

livekit_service = LiveKitService(SessionLocal())


@livekit_router.post("/create-room", status_code=201)
async def create_room(
    details: RoomDetails,
    current_user: Dict[str, Any] = Depends(BaseAuthGuard()),
):
    try:
        result = await livekit_service.create_room(
            details, current_user
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@livekit_router.post(
    "/generate-token",
    response_model=TokenResponse,
    summary="Generate LiveKit Token",
    description="""
    Generates a token for accessing a LiveKit room.

    - Requires authentication
    - Requires a valid room name
    - Returns an access token and the room name
    """,
    responses={
        200: {
            "description": "Token generated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "room_name": "interview-123e4567-e89b-12d3-a456-426614174000"
                    }
                }
            }
        },
        400: {
            "description": "Bad Request - Invalid room name",
            "content": {"application/json": {"example": {"detail": "Invalid room name"}}}
        },
        401: {
            "description": "Unauthorized - User not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}}
        },
        404: {
            "description": "Not Found - Room not found",
            "content": {"application/json": {"example": {"detail": "Room not found"}}}
        },
        500: {
            "description": "Internal Server Error - Token generation failed",
            "content": {"application/json": {"example": {"detail": "Failed to generate token"}}}
        }
    }
)
async def generate_token(
    request: TokenRequest,
    current_user: Dict[str, Any] = Depends(BaseAuthGuard()),
):
    try:
        token = await livekit_service.generate_token(
            request.room_name,
            current_user
        )
        return TokenResponse(access_token=token, room_name=request.room_name)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))
