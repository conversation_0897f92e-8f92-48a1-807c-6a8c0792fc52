from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.core.auth_guard import roles_required
from app.db.session import get_db
from app.schemas.resume_schema import ResumeCreate, ResumeResponse, ResumeListResponse
from app.services.resume_service import ResumeService
from app.utils.exceptions.user_exceptions import UnAuthorizedUserException
from app.utils.exceptions.database_exceptions import DatabaseOperationException
from app.schemas.user_schema import RegistrationType

# Router with tags
router = APIRouter(prefix="/resumes", tags=["resumes"])


@router.post("/create", response_model=Dict[str, Any])
async def create_resume(
    resume_data: ResumeCreate,
    current_user: dict = Depends(
        roles_required([RegistrationType.individual])
    ),
    db: Session = Depends(get_db),
):
    """
    Create a new resume for the authenticated user.
    Converts the resume to PDF and uploads to S3.
    """
    try:
        resume_service = ResumeService(db)
        result = await resume_service.create_resume(resume_data, current_user["user_id"])
        return result
    except UnAuthorizedUserException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except DatabaseOperationException as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )


@router.get("/list", response_model=Dict[str, Any])
def get_user_resumes(
    current_user: dict = Depends(roles_required([RegistrationType.individual])),
    db: Session = Depends(get_db),
):
    """
    Get all resumes for the authenticated user.
    """
    try:
        resume_service = ResumeService(db)
        result = resume_service.get_user_resumes(current_user["user_id"])
        return result
    except UnAuthorizedUserException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except DatabaseOperationException as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )


@router.get("/{resume_id}", response_model=Dict[str, Any])
def get_resume_by_id(
    resume_id: str,
    current_user: dict = Depends(roles_required([RegistrationType.individual])),
    db: Session = Depends(get_db),
):
    """
    Get a specific resume by ID for the authenticated user.
    """
    try:
        resume_service = ResumeService(db)
        result = resume_service.get_resume_by_id(resume_id, current_user["user_id"])
        return result
    except UnAuthorizedUserException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except DatabaseOperationException as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )


@router.delete("/{resume_id}", response_model=Dict[str, Any])
def delete_resume(
    resume_id: str,
    current_user: dict = Depends(roles_required([RegistrationType.individual])),
    db: Session = Depends(get_db),
):
    """
    Delete a specific resume for the authenticated user.
    """
    try:
        resume_service = ResumeService(db)
        result = resume_service.delete_resume(resume_id, current_user["user_id"])
        return result
    except UnAuthorizedUserException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except DatabaseOperationException as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )
