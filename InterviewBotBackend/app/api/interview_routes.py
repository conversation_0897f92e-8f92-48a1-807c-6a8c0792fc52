# app/routers/interview_router.py
from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, APIRouter, Depends, Form, File, UploadFile, Path, Query, Body
from sqlalchemy.orm import Session
from typing import List, Optional
from fastapi.responses import JSONResponse, StreamingResponse
from app.db.session import SessionLocal, get_db
from app.models.organization import Organization
from app.schemas.interview_schema import (
    Interview<PERSON><PERSON>,
    InterviewUpdate,
    InterviewCompletionUpdate,
)
from app.core.auth_guard import (
    BaseAuthGuard,
    roles_required,
)
from app.services.interview_service import InterviewService
from app.schemas.user_schema import RegistrationType

interview_router = APIRouter(prefix="/interviews", tags=["interviews"])
interview_service = InterviewService(SessionLocal())


@interview_router.post(
    "/",
    response_model=dict,
    summary="Create a New Interview",
    description="""
    This endpoint allows an organization to schedule a new interview by providing candidate details,
    availability window, and other required information.

    - The `skill_set` and `job_role` must match the organization's predefined options.
    - Timestamps (`available_from`, `available_until`) must be in UTC.
    - The availability window must be in the future, and `available_from` must be before `available_until`.
    - For organizations, the `jd_link` is NOT required. The system will use the job description
      associated with the selected skill set and job role from the organization's profile.
    - For individual users, the `jd_link` is required.
    - On success, returns the interview ID and a success message.
    - On failure, raises an appropriate error (e.g., invalid skill set, past availability window).
    """,
    responses={
        200: {
            "description": "Interview successfully created",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Interview scheduled successfully",
                        "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid input data",
            "content": {
                "application/json": {
                    "example": {"detail": "Available from time must be in the future"}
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Forbidden - Insufficient role",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Access restricted to individual, organization only",
                        "error_type": "insufficient_role"
                    }
                }
            },
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Failed to create interview"}}},
        },
    },
)
async def create_interview(
    interview_create: InterviewCreate,
    current_user: dict = Depends(
        roles_required([RegistrationType.organization, RegistrationType.individual])
    ),
):
    """Create a new interview with the provided details."""
    try:
        response = await interview_service.create_interview(interview_create, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/scheduler-upcoming",
    response_model=dict,
    summary="Get Upcoming Interviews",
    description="""
    This endpoint retrieves a paginated list of upcoming interviews for the authenticated organization or individual user.
    Upcoming interviews are those where the `available_until` timestamp is in the future.

    - Interviews are sorted by the soonest date first (based on `available_from` timestamp).
    - For example, if today is May 11 and there are interviews on May 15, 16, 13, and 20,
      they will be returned in order: May 13, 15, 16, 20.
    - Use `skip` and `limit` for pagination.
    - Optionally filter by `completed` status (true/false).
    - Returns a list of interviews with their details and pagination metadata.
    """,
    responses={
        200: {
            "description": "Upcoming interviews retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Upcoming Interviews fetched successfully",
                        "data": [
                            {
                                "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                                "candidate_name": "John Doe",
                                "candidate_email": "<EMAIL>",
                                "skill_sets": "Python",
                                "job_role": "Developer",
                                "experience_level": "Mid",
                                "available_from": "2025-04-07T12:00:00Z",
                                "available_until": "2025-04-07T14:00:00Z",
                                "interview_duration": 30,
                                "resume_link": "resume.pdf",
                                "jd_link": "jd.pdf",
                                "candidate_suitability": "candidate_suitability",
                                "agenda": "agenda",
                                "questions": "questions",
                                "is_completed": False,
                                "status": "completed/upcoming/passedWithoutJoining/live",
                            }
                        ],
                        "pagination": {
                            "total_interviews": 25,
                            "total_pages": 3,
                            "current_page": 2,
                            "current_page_length": 1,
                            "page_size": 10,
                        },
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Error fetching interviews"}}},
        },
    },
)
async def get_upcoming_interviews(
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(100, description="Limit to N records"),
    completed: Optional[bool] = Query(None, description="Filter by completion status"),
    current_user: dict = Depends(roles_required([RegistrationType.organization,RegistrationType.individual])),
):
    try:
        response = interview_service.get_upcoming_interviews(skip, limit, completed, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/scheduler-past",
    response_model=dict,
    summary="Get Past Interviews",
    description="""
    This endpoint retrieves a paginated list of past interviews for the authenticated organization or individual user.
    Past interviews are those where the `available_until` timestamp is in the past.

    - Interviews are sorted by the most recent date first (based on `available_until` timestamp).
    - For example, if today is May 11 and there are past interviews from May 1, 5, 3, and 8,
      they will be returned in order: May 8, 5, 3, 1.
    - Use `skip` and `limit` for pagination.
    - Optionally filter by `completed` status (true/false).
    - Returns a list of interviews with their details and pagination metadata.
    """,
    responses={
        200: {
            "description": "Past interviews retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Past Interviews fetched successfully",
                        "data": [
                            {
                                "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                                "candidate_name": "John Doe",
                                "candidate_email": "<EMAIL>",
                                "skill_sets": "Python",
                                "job_role": "Developer",
                                "experience_level": "Mid",
                                "available_from": "2025-04-01T12:00:00Z",
                                "available_until": "2025-04-01T14:00:00Z",
                                "interview_duration": 30,
                                "resume_link": "resume.pdf",
                                "jd_link": "jd.pdf",
                                "candidate_suitability": "candidate_suitability",
                                "agenda": "agenda",
                                "questions": "questions",
                                "is_completed": True,
                                "status": "completed/upcoming/passedWithoutJoining/live",
                            }
                        ],
                        "pagination": {
                            "total_interviews": 15,
                            "total_pages": 3,
                            "current_page": 2,
                            "current_page_length": 1,
                            "page_size": 5,
                        },
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Error fetching interviews"}}},
        },
    },
)
async def get_past_interviews(
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(100, description="Limit to N records"),
    completed: Optional[bool] = Query(None, description="Filter by completion status"),
    current_user: dict = Depends(roles_required([RegistrationType.organization,RegistrationType.individual])),
):
    try:
        response = interview_service.get_past_interviews(skip, limit, completed, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/get-scheduler-stats",
    response_model=dict,
    summary="Get Interview Statistics",
    description="""
    Retrieves detailed statistics about interviews conducted by the authenticated organization.

    This includes:
    - Total interviews conducted
    - Number of interviews by job role
    - Percentage distribution of interviews by skill set
    - Interview trends over time (daily, weekly, monthly, yearly)

    These insights help organizations monitor their interview activity and hiring patterns.
    """,
    responses={
        200: {
            "description": "Interview statistics fetched successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Interview statistics fetched successfully",
                        "total_interviews": 50,
                        "interviews_by_job_role": {"Developer": 30, "Manager": 20},
                        "interviews_by_skill": {"Python": 60.0, "JavaScript": 40.0},
                        "time_based_statistics": {
                            "daily": {
                                "2025-04-01": 2,
                                "2025-04-02": 4,
                                "2025-04-03": 6,
                                "2025-04-04": 3,
                                "2025-04-05": 5,
                                "2025-04-06": 2,
                                "2025-04-07": 1,
                            },
                            "weekly": {
                                "Week 14, 2025": 12,
                                "Week 13, 2025": 10,
                                "Week 12, 2025": 8,
                                "Week 11, 2025": 7,
                                "Week 10, 2025": 6,
                                "Week 9, 2025": 5,
                                "Week 8, 2025": 2,
                            },
                            "monthly": {
                                "April 2025": 10,
                                "March 2025": 15,
                                "February 2025": 8,
                                "January 2025": 5,
                                "December 2024": 3,
                                "November 2024": 4,
                                "October 2024": 2,
                                "September 2024": 1,
                                "August 2024": 1,
                                "July 2024": 0,
                                "June 2024": 1,
                                "May 2024": 0,
                            },
                            "yearly": {
                                "2025": 38,
                                "2024": 12,
                                "2023": 0,
                                "2022": 0,
                                "2021": 0,
                                "2020": 0,
                                "2019": 0,
                            },
                        },
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error - Failed to fetch statistics",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Failed to fetch interview statistics: DB connection failed"
                    }
                }
            },
        },
    },
)
async def get_interviews_stats(
    current_user: Organization = Depends(roles_required([RegistrationType.organization,RegistrationType.individual])),
):
    try:
        response = await interview_service.get_interview_statistics(current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/candidate/get-candidate-interviews-stats",
    response_model=dict,
    summary="Get Candidate Interview Statistics",
    description="""
    Retrieves detailed statistics about interviews scheduled for the authenticated candidate.

    This includes:
    - Total interviews scheduled
    - Completed interviews with organization details
    - Upcoming interviews with organization details
    - Interviews that passed without completion
    - Interviews grouped by skill set
    - Interviews grouped by job role
    - Daily breakdown of interviews scheduled in the last 7 days


    For each interview, detailed information is provided including organization name,
    skill set, job role, and scheduled times. This helps candidates track their interview activity.
    """,
    responses={
        200: {
            "description": "Candidate interview statistics fetched successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Candidate interview statistics fetched successfully",
                        "total_interviews": 5,
                        "completed_interviews": {
                            "count": 2,
                            "interviews": [
                                {
                                    "interview_id": "12345",
                                    "organization_id": "org123",
                                    "organization_name": "Tech Solutions Inc.",
                                    "skill_sets": "Python",
                                    "job_role": "Backend Developer",
                                    "available_from": "2025-03-15T14:00:00+00:00",
                                    "available_until": "2025-03-15T15:30:00+00:00",
                                    "interview_duration": 90,
                                }
                            ],
                        },
                        "upcoming_interviews": {
                            "count": 2,
                            "interviews": [
                                {
                                    "interview_id": "67890",
                                    "organization_id": "org456",
                                    "organization_name": "Cloud Enterprises",
                                    "skill_sets": "AWS",
                                    "job_role": "DevOps Engineer",
                                    "available_from": "2025-04-25T10:00:00+00:00",
                                    "available_until": "2025-04-25T11:00:00+00:00",
                                    "interview_duration": 60,
                                    "experience_level": "Senior",
                                }
                            ],
                        },
                        "passed_without_completion": {
                            "count": 1,
                            "interviews": [
                                {
                                    "interview_id": "24680",
                                    "organization_id": "org789",
                                    "organization_name": "Data Insights",
                                    "skill_sets": "SQL",
                                    "job_role": "Data Analyst",
                                    "available_from": "2025-02-20T13:00:00+00:00",
                                    "available_until": "2025-02-20T14:00:00+00:00",
                                    "interview_duration": 60,
                                    "experience_level": "Junior",
                                }
                            ],
                        },
                        "interviews_by_skill_set": {
                            "Python": {
                                "count": 2,
                                "interviews": [
                                    {
                                        "interview_id": "12345",
                                        "organization_name": "Tech Solutions Inc.",
                                        "skill_sets": "Python",
                                        "job_role": "Backend Developer",
                                    },
                                    {
                                        "interview_id": "fghij",
                                        "organization_name": "FinTech Solutions",
                                        "skill_sets": "Python",
                                        "job_role": "Data Engineer",
                                    },
                                ],
                            },
                            "AWS": {
                                "count": 2,
                                "interviews": [
                                    {
                                        "interview_id": "67890",
                                        "organization_name": "Cloud Enterprises",
                                        "skill_sets": "AWS",
                                        "job_role": "DevOps Engineer",
                                    },
                                    {
                                        "interview_id": "pqrst",
                                        "organization_name": "Cloud Services",
                                        "skill_sets": "AWS",
                                        "job_role": "DevOps Engineer",
                                    },
                                ],
                            },
                            "SQL": {
                                "count": 1,
                                "interviews": [
                                    {
                                        "interview_id": "24680",
                                        "organization_name": "Data Insights",
                                        "skill_sets": "SQL",
                                        "job_role": "Data Analyst",
                                    }
                                ],
                            },
                        },
                        "interviews_by_job_role": {
                            "Backend Developer": {
                                "count": 2,
                                "interviews": [
                                    {
                                        "interview_id": "12345",
                                        "organization_name": "Tech Solutions Inc.",
                                        "skill_sets": "Python",
                                        "job_role": "Backend Developer",
                                    },
                                    {
                                        "interview_id": "klmno",
                                        "organization_name": "Healthcare Systems",
                                        "skill_sets": "Java",
                                        "job_role": "Backend Developer",
                                    },
                                ],
                            },
                            "DevOps Engineer": {
                                "count": 2,
                                "interviews": [
                                    {
                                        "interview_id": "67890",
                                        "organization_name": "Cloud Enterprises",
                                        "skill_sets": "AWS",
                                        "job_role": "DevOps Engineer",
                                    },
                                    {
                                        "interview_id": "pqrst",
                                        "organization_name": "Cloud Services",
                                        "skill_sets": "AWS",
                                        "job_role": "DevOps Engineer",
                                    },
                                ],
                            },
                            "Data Analyst": {
                                "count": 1,
                                "interviews": [
                                    {
                                        "interview_id": "24680",
                                        "organization_name": "Data Insights",
                                        "skill_sets": "SQL",
                                        "job_role": "Data Analyst",
                                    }
                                ],
                            },
                        },
                        "last_seven_days": {
                            "2025-04-18": {
                                "count": 1,
                                "interviews": [
                                    {
                                        "interview_id": "abcde",
                                        "organization_name": "Tech Innovators",
                                        "skill_sets": "JavaScript",
                                        "job_role": "Frontend Developer",
                                    }
                                ],
                            },
                            "2025-04-17": {"count": 0, "interviews": []},
                            "2025-04-16": {
                                "count": 2,
                                "interviews": [
                                    {
                                        "interview_id": "fghij",
                                        "organization_name": "FinTech Solutions",
                                        "skill_sets": "Python",
                                        "job_role": "Data Engineer",
                                    },
                                    {
                                        "interview_id": "klmno",
                                        "organization_name": "Healthcare Systems",
                                        "skill_sets": "Java",
                                        "job_role": "Backend Developer",
                                    },
                                ],
                            },
                            "2025-04-15": {"count": 0, "interviews": []},
                            "2025-04-14": {
                                "count": 1,
                                "interviews": [
                                    {
                                        "interview_id": "pqrst",
                                        "organization_name": "Cloud Services",
                                        "skill_sets": "AWS",
                                        "job_role": "DevOps Engineer",
                                    }
                                ],
                            },
                            "2025-04-13": {"count": 0, "interviews": []},
                            "2025-04-12": {
                                "count": 1,
                                "interviews": [
                                    {
                                        "interview_id": "uvwxy",
                                        "organization_name": "Security Systems",
                                        "skill_sets": "Cybersecurity",
                                        "job_role": "Security Analyst",
                                    }
                                ],
                            },
                        },
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - User not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Forbidden - User not of type 'individual'",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Access not allowed. This endpoint is for individual users only."
                    }
                }
            },
        },
        500: {
            "description": "Internal Server Error - Failed to fetch candidate statistics",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Failed to fetch candidate interview statistics: DB connection failed"
                    }
                }
            },
        },
    },
)
async def get_candidate_interview_stats(
    current_user: dict = Depends(roles_required([RegistrationType.individual])),
):
    """
    Fetch interview statistics for the currently authenticated candidate.

    Args:
        current_user: The authenticated user (must be of type 'individual')
        db: Database session

    Returns:
        JSONResponse with interview statistics for the candidate
    """
    try:
        # Verify this is an individual user (candidate)

        # Call the function to get candidate stats
        response = await interview_service.get_candidate_interview_statistics(current_user)

        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/candidate/get-candidate-all-interviews",
    response_model=dict,
    summary="Get All Candidate Interviews",
    description="""
    Retrieves a list of all interviews associated with the currently authenticated candidate.

    Interviews are sorted in the following order:
    - Upcoming interviews first (sorted by available_from in ascending order - soonest first)
    - Past interviews next (sorted by available_until in descending order - most recent first)

    For example, if today is May 11 and there are:
    - Upcoming interviews on May 13, 15, 16, and 20
    - Past interviews from May 1, 5, 3, and 8
    They will be returned in order: May 13, 15, 16, 20, 8, 5, 3, 1

     - Use `skip` and `limit` for pagination.
     - Returns pagination metadata plus the interviews array.

    Each interview includes detailed information:
    - Organization name
    - Skill set
    - Job role
    - Scheduled time
    - Duration
    - Completion status
    - Creation timestamp

    This endpoint helps candidates view their full interview history.
    """,
    responses={
        200: {
            "description": "All candidate interviews fetched successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "All candidate interviews fetched successfully.",
                        "total_count": 3,
                        "interviews": [
                            {
                                "interview_id": "12345",
                                "organization_id": "org123",
                                "organization_name": "Tech Solutions Inc.",
                                "skill_sets": "Python",
                                "job_role": "Backend Developer",
                                "available_from": "2025-03-15T14:00:00+00:00",
                                "available_until": "2025-03-15T15:30:00+00:00",
                                "interview_duration": 90,
                                "is_completed": True,
                                "created_at": "2025-03-01T10:00:00+00:00",
                            },
                            {
                                "interview_id": "67890",
                                "organization_id": "org456",
                                "organization_name": "Cloud Enterprises",
                                "skill_sets": "AWS",
                                "job_role": "DevOps Engineer",
                                "available_from": "2025-04-25T10:00:00+00:00",
                                "available_until": "2025-04-25T11:00:00+00:00",
                                "interview_duration": 60,
                                "is_completed": False,
                                "created_at": "2025-04-10T09:00:00+00:00",
                            },
                        ],
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - User not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Forbidden - User not of type 'individual'",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Access not allowed. This endpoint is for individual users only."
                    }
                }
            },
        },
        500: {
            "description": "Internal Server Error - Failed to fetch candidate interviews",
            "content": {
                "application/json": {
                    "example": {"detail": "Failed to retrieve interviews due to a database error."}
                }
            },
        },
    },
)
async def get_candidate_all_interviews(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, gt=0, description="Maximum number of records to return"),
    current_user: dict = Depends(roles_required([RegistrationType.individual])),
):
    """
    Fetch all interviews for the currently authenticated candidate.

    Args:
        current_user: The authenticated user (must be of type 'individual')

    Returns:
        JSONResponse with all interview details
    """
    try:
        response = await interview_service.get_candidate_all_interviews(current_user, skip, limit)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/get-all-scheduler-interviews",
    response_model=dict,
    summary="Get All Interviews",
    description="""
    This endpoint retrieves a paginated list of all interviews for the authenticated organization or individual user.

    Interviews are sorted in the following order:
    - Upcoming interviews first (sorted by available_from in ascending order - soonest first)
    - Past interviews next (sorted by available_until in descending order - most recent first)

    For example, if today is May 11 and there are:
    - Upcoming interviews on May 13, 15, 16, and 20
    - Past interviews from May 1, 5, 3, and 8
    They will be returned in order: May 13, 15, 16, 20, 8, 5, 3, 1

    - Use `skip` and `limit` for pagination.
    - Optionally filter by `completed` status (true/false).
    - Returns a list of interviews with their details and pagination metadata.
    """,
    responses={
        200: {
            "description": "Interviews retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Interviews fetched successfully",
                        "data": [
                            {
                                "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                                "candidate_name": "John Doe",
                                "candidate_email": "<EMAIL>",
                                "skill_sets": "Python",
                                "job_role": "Developer",
                                "experience_level": "Mid",
                                "available_from": "2025-04-07T12:00:00Z",
                                "available_until": "2025-04-07T14:00:00Z",
                                "interview_duration": 30,
                                "candidate_suitability": "candidate_suitability",
                                "is_completed": False,
                                "status": "upcoming",
                                "scheduled_by_type": "organization",
                                "created_at": "2025-04-01T10:00:00Z",
                                "updated_at": "2025-04-01T10:00:00Z",
                                "candidate_profile": {
                                    "email": "<EMAIL>",
                                    "full_name": "John Doe",
                                    "position": None,
                                    "location": None,
                                    "phone_number": None,
                                    "bio": None,
                                    "website": None,
                                    "instagram": None,
                                    "tiktok": None,
                                    "youtube": None,
                                    "twitter": None,
                                    "linkedin": None,
                                    "profile_picture": None
                                },
                            }
                        ],
                        "pagination": {
                            "total_interviews": 30,
                            "total_pages": 3,
                            "current_page": 2,
                            "current_page_length": 1,
                            "page_size": 10,
                        },
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Error fetching interviews"}}},
        },
    },
)
async def get_all_scheduler_interviews(
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(100, description="Limit to N records"),
    completed: Optional[bool] = Query(None, description="Filter by completion status"),
    current_user=Depends(
        roles_required([RegistrationType.organization, RegistrationType.individual])
    ),
):
    try:
        response = interview_service.get_all_interviews(skip, limit, completed, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/{interview_id}",
    response_model=dict,
    summary="Get Interview by ID",
    description="""
    This endpoint retrieves the details of a specific interview by its ID.

    - The interview must belong to the authenticated organization.
    - Returns the interview details if found.
    - Raises a 404 error if the interview is not found.
    """,
    responses={
        200: {
            "description": "Interview retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Interview scheduled successfully",
                        "interview": {
                            "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                            "candidate_name": "John Doe",
                            "candidate_email": "<EMAIL>",
                            "skill_sets": "Python",
                            "job_role": "Developer",
                            "experience_level": "Mid",
                            "available_from": "2025-04-07T12:00:00Z",
                            "available_until": "2025-04-07T14:00:00Z",
                            "interview_duration": 30,
                            "candidate_suitability": "candidate_suitability",
                            "is_completed": False,
                            "status": "upcoming",
                            "scheduled_by_type": "organization",
                            "created_at": "2025-04-01T10:00:00Z",
                            "updated_at": "2025-04-01T10:00:00Z",
                            "candidate_profile": {
                                "email": "<EMAIL>",
                                "full_name": "John Doe",
                                "position": None,
                                "location": None,
                                "phone_number": None,
                                "bio": None,
                                "website": None,
                                "instagram": None,
                                "tiktok": None,
                                "youtube": None,
                                "twitter": None,
                                "linkedin": None,
                                "profile_picture": None
                            },
                            "agenda": ["Introduction", "Technical Questions", "Behavioral Questions"],
                            "questions": [
                                {
                                    "section": "Technical",
                                    "questions": ["Tell me about your experience with Python", "Explain OOP concepts"]
                                }
                            ]
                        }
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        404: {
            "description": "Not Found - Interview does not exist",
            "content": {"application/json": {"example": {"detail": "Interview not found"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Error fetching interview"}}},
        },
    },
)
async def get_interview(
    interview_id: str = Path(...),
    current_user: dict = Depends(
        roles_required([RegistrationType.organization, RegistrationType.individual])
    ),
):
    try:
        response = interview_service.get_interview(interview_id, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.put(
    "/{interview_id}",
    response_model=dict,
    summary="Update an Interview",
    description="""
    This endpoint allows updating the availability window and duration of an existing interview.

    - Only `available_from`, `available_until`, and `interview_duration` can be updated.
    - The interview must not be completed.
    - Timestamps must be in UTC and in the future.
    - `available_from` must be before `available_until`.
    - On success, returns the updated interview details.
    """,
    responses={
        200: {
            "description": "Interview updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Interview updated successfully",
                        "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                        "candidate_name": "John Doe",
                        "candidate_email": "<EMAIL>",
                        "skill_sets": "Python",
                        "job_role": "Developer",
                        "experience_level": "Mid",
                        "available_from": "2025-04-07T12:00:00Z",
                        "available_until": "2025-04-07T14:00:00Z",
                        "interview_duration": 45,
                        "resume_link": "resume.pdf",
                        "jd_link": "jd.pdf",
                        "candidate_suitability": "candidate_suitability",
                        "agenda": "agenda",
                        "questions": "questions",
                        "is_completed": False,
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid input data",
            "content": {
                "application/json": {
                    "example": {"detail": "Available from time must be in the future"}
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        404: {
            "description": "Not Found - Interview does not exist",
            "content": {"application/json": {"example": {"detail": "Interview not found"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Failed to update interview"}}},
        },
    },
)
async def update_interview(
    interview_id: str = Path(...),
    update_data: InterviewUpdate = Body(...),  # Use InterviewUpdate schema for request body
    current_user: Organization = Depends(roles_required([RegistrationType.organization,RegistrationType.individual])),
):
    try:
        # Convert the Pydantic model to a dict, excluding None values
        update_dict = update_data.dict(exclude_unset=True)

        # Call the service method to update the interview
        response = await interview_service.update_interview(
            interview_id,
            update_dict,
            current_user,
        )
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.delete(
    "/{interview_id}",
    response_model=dict,
    summary="Delete an Interview",
    description="""
    This endpoint deletes an interview by its ID.

    - The interview must belong to the authenticated organization.
    - The interview must not be completed.
    - The availability window must not be in the past.
    - On success, returns a confirmation message with the deleted interview ID.
    """,
    responses={
        200: {
            "description": "Interview deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Interview deleted successfully",
                        "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        404: {
            "description": "Not Found - Interview does not exist",
            "content": {"application/json": {"example": {"detail": "Interview not found"}}},
        },
        400: {
            "description": "Bad Request - Cannot delete a completed interview",
            "content": {
                "application/json": {"example": {"detail": "Cannot delete a completed interview"}}
            },
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Failed to delete interview"}}},
        },
    },
)
async def delete_interview(
    interview_id: str = Path(...),
    current_user: Organization = Depends(
        roles_required([RegistrationType.organization, RegistrationType.individual])
    ),
):
    try:
        response = interview_service.delete_interview(interview_id, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.put(
    "/{interview_id}/complete",
    response_model=dict,
    summary="Mark Interview as Completed",
    description="""
    This endpoint marks an interview as completed.

    - The interview must belong to the authenticated organization.
    - On success, returns the updated interview details with `is_completed` set to true.
    """,
    responses={
        200: {
            "description": "Interview marked as completed",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Interview marked completed successfully",
                        "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                        "candidate_name": "John Doe",
                        "candidate_email": "<EMAIL>",
                        "skill_sets": "Python",
                        "job_role": "Developer",
                        "experience_level": "Mid",
                        "available_from": "2025-04-07T12:00:00Z",
                        "available_until": "2025-04-07T14:00:00Z",
                        "interview_duration": 30,
                        "resume_link": "resume.pdf",
                        "jd_link": "jd.pdf",
                        "candidate_suitability": "candidate_suitability",
                        "agenda": "agenda",
                        "questions": "questions",
                        "is_completed": True,
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        404: {
            "description": "Not Found - Interview does not exist",
            "content": {"application/json": {"example": {"detail": "Interview not found"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {
                "application/json": {"example": {"detail": "Failed to mark interview as completed"}}
            },
        },
    },
)
async def mark_interview_completed(
    interview_id: str = Path(...),
    current_user = Depends(
       BaseAuthGuard()
    ),
):
    try:
        response = interview_service.mark_interview_completed(interview_id, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.put(
    "/{interview_id}/transcript",
    response_model=dict,
    summary="Update Interview Transcript",
    description="""
    This endpoint allows updating the transcript of an existing interview.

    - Only updates the `interview_transcript` field.
    - The interview must exist and belong to the authenticated organization.
    - On success, returns the updated interview details.
    """,
    responses={
        200: {
            "description": "Transcript updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Interview transcript updated successfully",
                        "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                        "interview_transcript": "Updated transcript content...",
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid input data",
            "content": {"application/json": {"example": {"detail": "Invalid transcript format"}}},
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        404: {
            "description": "Not Found - Interview does not exist",
            "content": {"application/json": {"example": {"detail": "Interview not found"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Failed to update transcript"}}},
        },
    },
)
async def update_interview_transcript_and_violation(
    interview_id: str = Path(...),
    payload: InterviewCompletionUpdate = Body(...),
    current_user: dict = Depends(BaseAuthGuard()),
):
    try:
        # Call the service method to update the interview transcript
        response = interview_service.update_interview_transcript_and_violation(
            interview_id,
            payload.interview_transcript,
            payload.interview_violation,
            current_user,
        )
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/{interview_id}/report",
    response_model=dict,
    summary="Get Interview report by ID",
    description="""
    This endpoint retrieves the details of a specific interview by its ID.

    - The interview must belong to the authenticated organization.
    - Returns the interview details if found.
    - Raises a 404 error if the interview is not found.
    """,
    responses={
        200: {
            "description": "Interview retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "interview_id": "123e4567-e89b-12d3-a456-426614174000",
                        "candidate_name": "John Doe",
                        "candidate_email": "<EMAIL>",
                        "agenda": ["Introduction", "Technical Questions", "Behavioral Questions"],
                        "questions": [
                            {
                                "section": "Technical",
                                "questions": ["Tell me about your experience with Python", "Explain OOP concepts"]
                            }
                        ],
                        "is_completed": True,
                        "status": "completed",
                        "skill_sets": "Python",
                        "job_role": "Developer",
                        "experience_level": "Mid",
                        "interview_duration": 30,
                        "available_from": "2025-04-07T12:00:00Z",
                        "available_until": "2025-04-07T14:00:00Z",
                        "interview_transcript": "Full transcript of the interview...",
                        "verification_result": "Verification analysis results...",
                        "feedback_result": "Feedback analysis results...",
                        "technical_evaluation_result": "Technical evaluation results...",
                        "recommendation_result": "Recommendation results...",
                        "interview_summary_result": "Interview summary results...",
                        "verification_stats": [
                            {"agenda_item": "Introduction", "score": 0.95},
                            {"agenda_item": "Technical Questions", "score": 0.85}
                        ],
                        "violation_result": "No violations detected",
                        "created_at": "2025-04-01T10:00:00Z",
                        "updated_at": "2025-04-07T15:00:00Z",
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Organization not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        404: {
            "description": "Not Found - Interview does not exist",
            "content": {"application/json": {"example": {"detail": "Interview not found"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Error fetching interview"}}},
        },
    },
)
async def get_interview_report(
    interview_id: str = Path(...),
    current_user=Depends(
        roles_required([RegistrationType.organization, RegistrationType.individual])
    ),
):
    try:
        response = interview_service.get_interview_report(interview_id, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@interview_router.get(
    "/{interview_id}/report-pdf",
    summary="Get Interview Report as PDF Data",
    description="""
    This endpoint generates a PDF report for a specific interview and returns it as base64-encoded data.

    - The interview must belong to the authenticated organization or individual.
    - The interview must be completed.
    - Returns a JSON response containing the base64-encoded PDF data.
    - The client can use the base64 data to create and download the PDF directly.
    - Raises a 404 error if the interview is not found.
    - Raises a 400 error if the interview is not completed.
    """,
    responses={
        200: {
            "description": "PDF report generated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "PDF report generated successfully",
                        "pdf_data": "JVBERi0xLjQKJeLjz9MKMyAwIG9iago8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDQ5Nj4+c3RyZWFtCnicfVRNj9MwEL3zK+ZIK..."
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - User not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        404: {
            "description": "Not Found - Interview does not exist",
            "content": {"application/json": {"example": {"detail": "Interview not found"}}},
        },
        400: {
            "description": "Bad Request - Interview not completed",
            "content": {"application/json": {"example": {"detail": "Interview not completed"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {"application/json": {"example": {"detail": "Error generating PDF report"}}},
        },
    },
)
async def download_interview_report_pdf(
    interview_id: str = Path(...),
    current_user=Depends(
        roles_required([RegistrationType.organization, RegistrationType.individual])
    ),
):
    try:
        pdf_buffer = interview_service.get_interview_report_pdf(interview_id, current_user)

        # Convert the PDF buffer to base64 for direct download
        import base64
        pdf_buffer.seek(0)  # Reset buffer position to the beginning
        pdf_base64 = base64.b64encode(pdf_buffer.read()).decode('utf-8')

        # Return the PDF data as JSON
        return JSONResponse(
            content={
                "status": "success",
                "message": "PDF report generated successfully",
                "pdf_data": pdf_base64
            },
            status_code=200
        )
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))
