from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.core.auth_guard import  roles_required
from app.schemas.user_schema import RegistrationType
from app.models.enums import JobStatus
from app.services.job_posting_service import JobPostingService
from app.services.job_application_service import JobApplicationService
from app.schemas.job_posting_schema import (
    JobPostingCreateSchema,
    JobPostingUpdateSchema,
    JobPostingResponseSchema,
    JobAnalyticsSchema,
    JobSearchFiltersSchema,
    JobPostingListResponseSchema,
    JobPostingBrowseResponseSchema,
)
from app.schemas.job_application_schema import (
    JobApplicationCreateSchema,
    JobApplicationUpdateSchema,
    JobApplicationStatusUpdateSchema,
    JobApplicationResponseSchema,
    JobApplicationListResponseSchema,
    UserApplicationsResponseSchema,
    JobApplicationWithJobDetailsSchema,
    ApplicationStatusResponseSchema,
)
from app.schemas.interview_schema import <PERSON><PERSON><PERSON>, Interview<PERSON><PERSON>FromApplication, InterviewResponse
from app.utils.exceptions.job_exceptions import (
    JobPostingNotFoundException,
    JobApplicationNotFoundException
)

# Router with tags
job_router = APIRouter(prefix="/jobs", tags=["jobs"])
job_application_router = APIRouter(prefix="/applications", tags=["job-applications"])
job_browse_router = APIRouter(prefix="/browse", tags=["job-browsing"])

# ============================================================================
# ORGANIZATION ROUTES - Job Posting Management
# ============================================================================

@job_router.post(
    "/",
    response_model=JobPostingResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a New Job Posting",
    description="""
    Create a new job posting for the organization. Company information will be auto-filled
    from the organization's profile.
    
    **Required Fields:**
    - job_title: Position title (e.g., "Senior Software Engineer")
    - workplace_type: remote/hybrid/on_site
    - job_location: Job location (e.g., "San Francisco, CA" or "Remote")
    - employment_type: full_time/part_time/contract/internship/temporary
    - jd_link: S3 URL to job description file (optional)
    - jd_id: Reference to standalone job description (optional)
    - required_skills: Array of required skills (e.g., ["Python", "FastAPI", "PostgreSQL"])
    
    **Optional Fields:**
    - screening_questions: Array of custom screening questions
    
    **Auto-filled Fields:**
    - company_name: From organization profile
    - company_location: From organization profile
    - company_website: From organization profile
    - status: Set to ACTIVE by default
    
    """,
)
async def create_job_posting(
    job_data: JobPostingCreateSchema,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Create a new job posting."""
    try:
        job_posting_service = JobPostingService(db)
        job_posting = job_posting_service.create_job_posting(current_user["user_id"], job_data)
        return job_posting
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_router.get(
    "/",
    response_model=dict,
    summary="Get Organization's Job Postings",
    description="""
    Retrieve all job postings for the authenticated organization with pagination support.
    
    **Query Parameters:**
    - status: Filter by job status (active/closed) - optional
    - skip: Number of records to skip for pagination (default: 0)
    - limit: Maximum number of records to return (default: 100)
    
    **Usage Examples:**
    - Get all jobs: `GET /jobs/`
    - Get only active jobs: `GET /jobs/?status=active`
    - Get only closed jobs: `GET /jobs/?status=closed`
    - Pagination: `GET /jobs/?skip=10&limit=5`
    """,
)
async def get_organization_jobs(
    status_filter: Optional[JobStatus] = Query(None, alias="status"),
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(100, description="Limit to N records"),
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Get all job postings for the organization."""
    try:
        job_posting_service = JobPostingService(db)
        response = job_posting_service.get_organization_jobs(current_user["user_id"], status_filter, skip, limit)
        return response
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_router.get(
    "/{job_id}",
    response_model=dict,
    summary="Get Specific Job Posting",
    description="""
    Retrieve details of a specific job posting owned by the organization.
    
    **Access Control:**
    - Only the organization that created the job posting can access it
    - Returns 404 if job posting doesn't exist or doesn't belong to the organization
    
    **Response Includes:**
    - Complete job posting details
    - Analytics properties (total_applications, pending_applications, etc.)
    - Screening questions if configured
    - Company information auto-filled from organization profile
    
    """,
)
async def get_job_posting(
    job_id: str,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Get a specific job posting."""
    try:
        job_posting_service = JobPostingService(db)
        job_posting = job_posting_service.get_job_posting(job_id, current_user["user_id"])
        return job_posting
    except JobPostingNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_router.put(
    "/{job_id}",
    response_model=JobPostingResponseSchema,
    summary="Update Job Posting",
    description="""
    Update an existing job posting. Only ACTIVE job postings can be updated.
    
    **Restrictions:**
    - Only the organization that created the job can update it
    - Only ACTIVE job postings can be updated (CLOSED jobs cannot be modified)
    - To reopen a closed job posting, use the toggle-status endpoint first
    
    **Update Rules:**
    - All fields are optional in the update request
    - Only provided fields will be updated
    - Screening questions can be completely replaced or updated
    
    **Error Cases:**
    - 404: Job posting not found or not owned by organization
    - 400: Job posting is CLOSED and cannot be updated
    - 400: Invalid enum values for workplace_type or employment_type
    """,
)
async def update_job_posting(
    job_id: str,
    job_data: JobPostingUpdateSchema,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Update a job posting."""
    try:
        job_posting_service = JobPostingService(db)
        job_posting = job_posting_service.update_job_posting(job_id, current_user["user_id"], job_data)
        return job_posting
    except JobPostingNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_router.patch(
    "/{job_id}/toggle-status",
    response_model=JobPostingResponseSchema,
    summary="Toggle Job Posting Status",
    description="""
    Toggle job posting status between ACTIVE and CLOSED.
    
    - If job posting is ACTIVE, it will be set to CLOSED
    - If job posting is CLOSED, it will be set to ACTIVE
    - Only the organization that owns the job posting can toggle its status
    - When a job is reactivated, it becomes available for new applications
    - When a job is closed, no new applications can be submitted
    
    **Examples:**
    - ACTIVE → CLOSED: Job posting is closed and hidden from public job listings
    - CLOSED → ACTIVE: Job posting is reopened and becomes visible in public job listings
    """,
)
async def toggle_job_posting_status(
    job_id: str,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Toggle job posting status between active and closed."""
    try:
        job_posting_service = JobPostingService(db)
        job_posting = job_posting_service.toggle_job_posting_status(job_id, current_user["user_id"])
        return job_posting
    except JobPostingNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_router.get(
    "/{job_id}/analytics",
    response_model=JobAnalyticsSchema,
    summary="Get Job Posting Analytics",
    description="""
    Get analytics and metrics for a specific job posting.
    
    **Access Control:**
    - Only the organization that owns the job posting can access analytics
    - Returns 404 if job posting doesn't exist or doesn't belong to the organization
    
    **Analytics Metrics:**
    - total_applications: Total number of applications received for this job
    - interviews_scheduled: Number of applications that have interviews scheduled
    - interviews_completed: Number of completed interviews from applications
    - pending_applications: Number of applications still in PENDING status

    
    **Use Cases:**
    - Track job posting performance
    - Monitor application pipeline
    - Identify bottlenecks in hiring process
    - Generate hiring reports
    """,
)
async def get_job_analytics(
    job_id: str,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Get analytics for a job posting."""
    try:
        job_posting_service = JobPostingService(db)
        analytics = job_posting_service.get_job_analytics(job_id, current_user["user_id"])
        return analytics
    except JobPostingNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_router.get(
    "/{job_id}/applications",
    response_model=dict,
    summary="Get Job Applications",
    description="""
    Get all applications for a specific job posting with pagination.
    
    - Only the organization that owns the job posting can access applications
    - Applications are ordered by application date (newest first)
    - Includes applicant details and application status
    - Uses interview-style pagination with skip/limit
    """,
)
async def get_job_applications(
    job_id: str,
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(100, description="Limit to N records"),
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Get all applications for a job posting."""
    try:
        job_application_service = JobApplicationService(db)
        response = job_application_service.get_job_applications(job_id, current_user["user_id"], skip, limit)
        return response
    except JobPostingNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_router.get(
    "/{job_id}/applications/{application_id}",
    response_model=JobApplicationResponseSchema,
    summary="Get Application Details",
    description="""
    Get detailed information about a specific job application.
    
    - Only the organization that owns the job posting can access application details
    - When an organization views an application for the first time, the status automatically changes from PENDING to REVIEWED
    - Returns complete application details including applicant information
    """,
)
async def get_application_details(
    job_id: str,
    application_id: str,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Get application details and auto-update status to REVIEWED if PENDING."""
    try:
        job_application_service = JobApplicationService(db)
        application = job_application_service.get_application_details(job_id, application_id, current_user["user_id"])
        return application
    except JobApplicationNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# ============================================================================
# ORGANIZATION ROUTES - Application Management
# ============================================================================

@job_application_router.put(
    "/{application_id}/status",
    response_model=JobApplicationResponseSchema,
    summary="Update Application Status",
    description="""
    Update the status of a job application. Only the organization that owns the job posting can update application status.
    
    Available statuses:
    - PENDING: Initial status when application is submitted
    - REVIEWED: Application has been reviewed
    - INTERVIEW_SCHEDULED: Interview has been scheduled
    - REJECTED: Application has been rejected
    - HIRED: Candidate has been hired
    """,
)
async def update_application_status(
    application_id: str,
    status_data: JobApplicationStatusUpdateSchema,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Update application status."""
    try:
        job_application_service = JobApplicationService(db)
        application = job_application_service.update_application_status(application_id, status_data, current_user["user_id"])
        return application
    except JobApplicationNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_application_router.post(
    "/{application_id}/schedule-interview",
    response_model=InterviewResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Schedule Interview from Application",
    description="""
    Schedule an interview directly from a job application. This creates a new interview
    linked to the application and updates the application status to INTERVIEW_SCHEDULED.
    
    **Automatically Populated Fields:**
    - candidate_name: From application user's full name
    - candidate_email: From application user's email
    - skill_sets: From job posting's required_skills
    - job_role: From job posting's job_role
    - resume_link: From application's resume_link
    - jd_link/jd_id: From job posting's JD reference
    
    **Required Fields:**
    - experience_level: Experience level required for the position
    - available_from: Start of availability window (UTC)
    - available_until: End of availability window (UTC)
    - interview_duration: Duration in minutes (default: 30)
    
    **Benefits:**
    - No need to manually enter candidate details
    - Consistent data from application and job posting
    - Automatic application status update to INTERVIEW_SCHEDULED
    - Maintains link between interview and original application
    """,
)
async def schedule_interview_from_application(
    application_id: str,
    interview_data: InterviewCreateFromApplication,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.organization])),
    db: Session = Depends(get_db),
):
    """Schedule an interview from a job application."""
    try:
        job_application_service = JobApplicationService(db)
        interview = await job_application_service.schedule_interview_from_application(application_id, interview_data, current_user["user_id"])
        return interview
    except JobApplicationNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# ============================================================================
# PUBLIC ROUTES - Job Browsing
# ============================================================================

@job_browse_router.get(
    "/jobs",
    response_model=dict,
    summary="Browse Available Jobs",
    description="""
    Browse and search available job postings. This is a public endpoint that shows only ACTIVE job postings.
    
    **Access:** Public endpoint - no authentication required
    
    **Response includes organization profile information:**
    - Each job posting includes organization_profile with name and profile_image
    - This provides visual context about the hiring organization
    
    **Search and Filter Options:**
    - job_title: Search in job titles (partial match, case-insensitive)
    - location: Search in job location or company location (partial match)
    - workplace_type: Filter by workplace type (remote/hybrid/on_site)
    - employment_type: Filter by employment type (full_time/part_time/contract/internship/temporary)
    - company_name: Search by company name (partial match, case-insensitive)
    - skills: Filter by required skills (provide multiple skills as separate query parameters)
    - skip: Number of records to skip for pagination (default: 0)
    - limit: Maximum number of records to return (default: 100)
    
    **Example Requests:**
    - Search by title: `GET /browse/jobs?job_title=python developer`
    - Filter by location: `GET /browse/jobs?location=new york`
    - Filter by workplace type: `GET /browse/jobs?workplace_type=remote`
    - Filter by employment type: `GET /browse/jobs?employment_type=full_time`
    - Filter by skills: `GET /browse/jobs?skills=python&skills=fastapi`
    - Combined filters: `GET /browse/jobs?job_title=developer&workplace_type=remote&skills=python`
    - With pagination: `GET /browse/jobs?skip=20&limit=10`
    
    """,
)
async def browse_jobs(
    job_title: Optional[str] = Query(None),
    location: Optional[str] = Query(None),
    workplace_type: Optional[str] = Query(None),
    employment_type: Optional[str] = Query(None),
    company_name: Optional[str] = Query(None),
    skills: Optional[List[str]] = Query(None),
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(100, description="Limit to N records"),
    db: Session = Depends(get_db),
):
    """Browse available job postings (public access)."""
    try:
        job_posting_service = JobPostingService(db)
        filters = JobSearchFiltersSchema(
            job_title=job_title,
            location=location,
            workplace_type=workplace_type,
            employment_type=employment_type,
            company_name=company_name,
            skills=skills,
        )
        response = job_posting_service.search_jobs(filters, skip, limit)
        return response
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_browse_router.get(
    "/jobs/{job_id}",
    response_model=JobPostingBrowseResponseSchema,
    summary="Get Job Details",
    description="""
    Get detailed information about a specific job posting for public viewing.
    
    - Only ACTIVE job postings are accessible through this endpoint
    - Returns complete job details including screening questions and organization profile
    - Used by job seekers to view job details before applying
    - Includes organization profile with name and profile image
    """,
)
async def get_public_job_posting(
    job_id: str,
    db: Session = Depends(get_db),
):
    """Get a specific job posting for public viewing with organization profile."""
    try:
        job_posting_service = JobPostingService(db)
        job_posting = job_posting_service.get_public_job_posting(job_id)
        return job_posting
    except JobPostingNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_browse_router.get(
    "/organization/{org_id}/jobs",
    response_model=dict,
    summary="Get Organization's Public Job Postings",
    description="""
    Retrieve all job postings for a specific organization. This is a public endpoint
    that can be called by anyone without authentication.
    
    **Path Parameters:**
    - org_id: The organization ID to get job postings for
    
    **Query Parameters:**
    - status: Filter by job status (active/closed) - optional
    - skip: Number of records to skip for pagination (default: 0)
    - limit: Maximum number of records to return (default: 100)
    
    **Usage Examples:**
    - Get all jobs for org: `GET /browse/organization/{org_id}/jobs`
    - Get only active jobs: `GET /browse/organization/{org_id}/jobs?status=active`
    - Get only closed jobs: `GET /browse/organization/{org_id}/jobs?status=closed`
    - Pagination: `GET /browse/organization/{org_id}/jobs?skip=10&limit=5`
    
    **Access:** Public endpoint - no authentication required
    """,
)
async def get_organization_public_jobs(
    org_id: str,
    status_filter: Optional[JobStatus] = Query(None, alias="status"),
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(100, description="Limit to N records"),
    db: Session = Depends(get_db),
):
    """Get all job postings for a specific organization (public access) with organization profile."""
    try:
        job_posting_service = JobPostingService(db)
        response = job_posting_service.get_organization_jobs(org_id, status_filter, skip, limit, include_org_profile=True)
        return response
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_browse_router.get(
    "/featured-jobs",
    response_model=List[JobPostingBrowseResponseSchema],
    summary="Get Featured Jobs",
    description="""
    Get a list of featured job postings. Returns the most recently posted active jobs.
    
    - Limited to a maximum of 50 jobs
    - Jobs are ordered by creation date (newest first)
    - Only ACTIVE job postings are included
    - Includes organization profile with name and profile image for each job
    """,
)
async def get_featured_jobs(
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db),
):
    """Get featured job postings with organization profile."""
    try:
        job_posting_service = JobPostingService(db)
        jobs = job_posting_service.get_featured_jobs(limit)
        return jobs
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# ============================================================================
# USER ROUTES - Job Applications
# ============================================================================

@job_browse_router.post(
    "/jobs/{job_id}/apply",
    response_model=JobApplicationResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Apply to Job",
    description="""
    Apply to a job posting. User must be authenticated as an individual to apply.
    
    **Requirements:**
    - User must be authenticated with individual account type
    - Job posting must be ACTIVE (closed jobs cannot receive applications)
    - Users can only apply once to each job posting
    - Resume link is required and should be an S3 URL (use resume upload endpoint first)
    
    **Optional Fields:**
    - cover_letter: Optional cover letter text
    
    **Screening Questions:**
    - If the job has required screening questions, answers must be provided
    - Screening answers should match the question types (text or multiple_choice)
    - Required questions must be answered
    
    **Response:**
    - Application status starts as PENDING
    - Returns complete application details including job information
    - Organization will be notified of the new application
    
    **Error Cases:**
    - 400: User has already applied to this job
    - 400: Job posting is CLOSED
    - 400: Missing required screening answers
    - 404: Job posting not found
    """,
)
async def apply_to_job(
    job_id: str,
    application_data: JobApplicationCreateSchema,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.individual])),
    db: Session = Depends(get_db),
):
    """Apply to a job posting."""
    try:
        job_application_service = JobApplicationService(db)
        application = job_application_service.apply_to_job(current_user["user_id"], job_id, application_data)
        return application
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_application_router.get(
    "/my-applications",
    response_model=dict,
    summary="Get My Applications",
    description="""
    Get all job applications submitted by the authenticated user.
    
    - Applications are ordered by submission date (newest first)
    - Includes job details for each application
    - Shows current application status
    - Uses interview-style pagination with skip/limit
    """,
)
async def get_my_applications(
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(100, description="Limit to N records"),
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.individual])),
    db: Session = Depends(get_db),
):
    """Get user's job applications."""
    try:
        job_application_service = JobApplicationService(db)
        response = job_application_service.get_user_applications(current_user["user_id"], skip, limit)
        
        # Convert serialized applications to include job details schema
        applications_with_details = []
        for app in response["data"]:
            # app is now a serialized dict with all the required fields
            app_dict = {
                "id": app["id"],
                "job_posting_id": app["job_posting_id"],
                "cover_letter": app["cover_letter"],
                "resume_link": app["resume_link"],
                "screening_answers": app["screening_answers"],
                "status": app["status"],
                "applied_at": app["applied_at"],
                "updated_at": app["updated_at"],
                "job_title": app["job_title"],
                "company_name": app["company_name"],
                "workplace_type": app["workplace_type"],
                "job_location": app["job_location"],
                "employment_type": app["employment_type"],
            }
            applications_with_details.append(JobApplicationWithJobDetailsSchema(**app_dict))
        
        # Update response data with detailed applications
        response["data"] = applications_with_details
        return response
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_application_router.put(
    "/my-applications/{application_id}",
    response_model=JobApplicationResponseSchema,
    summary="Update My Application",
    description="""
    Update user's own job application. Only applications in PENDING status can be updated.
    
    - Users can only update their own applications
    - Only pending applications can be modified (once reviewed by organization, editing is blocked)
    - All fields are optional in the update request
    - Useful for updating cover letter or screening answers before organization review
    """,
)
async def update_my_application(
    application_id: str,
    application_data: JobApplicationUpdateSchema,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.individual])),
    db: Session = Depends(get_db),
):
    """Update user's own job application."""
    try:
        job_application_service = JobApplicationService(db)
        application = job_application_service.update_user_application(application_id, current_user["user_id"], application_data)
        return application
    except JobApplicationNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@job_browse_router.get(
    "/jobs/{job_id}/application-status",
    response_model=ApplicationStatusResponseSchema,
    summary="Check Application Status for Job",
    description="""
    Check if the authenticated candidate has applied for a specific job posting.
    
    **Access:** Requires individual user authentication
    
    **Response includes:**
    - has_applied: Boolean indicating if the user has applied
    - application_id: ID of the application if it exists
    - application_status: Current status of the application (if applied)
    - applied_at: Timestamp when the application was submitted (if applied)
    - job_title: Title of the job posting
    - company_name: Name of the company
    - job_status: Current status of the job posting (active/closed)
    
    **Use Cases:**
    - Candidates can check which jobs they've already applied for
    - Frontend can show different UI states based on application status
    - Prevent duplicate applications by checking status first
    - Track application progress and status
    
    **Example Response:**
    ```json
    {
        "status": "success",
        "message": "Application status checked successfully",
        "data": {
            "job_id": "job123",
            "user_id": "user456",
            "has_applied": true,
            "application_id": "app789",
            "application_status": "pending",
            "applied_at": "2024-01-15T10:30:00Z",
            "job_title": "Senior Software Engineer",
            "company_name": "Tech Corp",
            "job_status": "active"
        }
    }
    ```
    """,
)
async def check_job_application_status(
    job_id: str,
    current_user: Dict[str, Any] = Depends(roles_required([RegistrationType.individual])),
    db: Session = Depends(get_db),
):
    """Check if the authenticated user has applied for a specific job."""
    try:
        job_application_service = JobApplicationService(db)
        response = job_application_service.check_application_status(current_user["user_id"], job_id)
        return response
    except JobPostingNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
