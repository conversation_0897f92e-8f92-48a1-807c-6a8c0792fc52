# app/api/routes/s3.py
from typing import Any, Dict
from fastapi import HTTPException
from fastapi import APIRouter, Depends, Query
from fastapi.responses import  JSONResponse
from app.core.auth_guard import BaseAuthGuard,roles_required
from app.services.s3_service import S3Service  # Import the new service
from app.schemas.user_schema import RegistrationType
from app.services.user_service import UserService
from app.db.session import SessionLocal


s3_router = APIRouter(prefix="/utils", tags=["utils"])


@s3_router.get(
    "/generate-presigned-url",
    summary="Generate a Presigned URL",
    description="""
    Generates a presigned URL for a file in S3 so that it can be uploaded temporarily without public access.

    - Requires the `key` of the file in the S3 bucket.
    - Optional `content_type` parameter to specify the MIME type of the file.
    - Supports PDF, PNG, JPEG/JPG file types.
    - The URL will expire in 1 hour by default.
    - Requires authentication.
    """,
    responses={
        200: {
            "description": "Presigned URL generated",
            "content": {
                "application/json": {
                    "examples": {
                        "pdf": {
                            "summary": "PDF File Upload URL",
                            "value": {
                                "success": True,
                                "message": "PresignedURL generated successfully",
                                "url": "https://your-bucket.s3.amazonaws.com/uploads/user-id/file.pdf?...",
                            }
                        },
                        "image": {
                            "summary": "Image Upload URL",
                            "value": {
                                "success": True,
                                "message": "PresignedURL generated successfully",
                                "url": "https://your-bucket.s3.amazonaws.com/uploads/user-id/profile.png?...",
                            }
                        }
                    }
                }
            },
        },
        400: {
            "description": "Missing or invalid key",
            "content": {"application/json": {"example": {"detail": "Missing 'key' query param"}}},
        },
        401: {"description": "Unauthorized - Invalid or missing token"},
        500: {
            "description": "Internal Server Error - Could not generate URL",
            "content": {
                "application/json": {"example": {"detail": "Failed to generate presigned URL"}}
            },
        },
    },
)
async def get_presigned_url(
    key: str = Query(..., description="S3 key of the file"),
    content_type: str = Query(None, description="Optional MIME type of the file (e.g., 'application/pdf', 'image/png', 'image/jpeg')"),
    current_user: dict = Depends(BaseAuthGuard()),
):
    try:
        response = await S3Service.get_presigned_url(key, content_type)
        return JSONResponse(content=response)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))
