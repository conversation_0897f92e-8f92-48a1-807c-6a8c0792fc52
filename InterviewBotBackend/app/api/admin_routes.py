from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse
from app.services.admin_service import AdminService
from app.db.session import SessionLocal
from app.schemas.user_schema import TokenResponse


admin_router = APIRouter(prefix="/admin", tags=["Admin"])


admin_service = AdminService(SessionLocal())


@admin_router.post(
    "/login",
    summary="Admin Login",
    description="""
    This endpoint authenticates a registered admin and returns an access token.

    - Accepts email (as username) and password using OAuth2 password flow.
    - Email is case-insensitive (converted to lowercase).
    - On success, returns an access token, token type, and admin details.
    - On failure, raises appropriate error messages.

    **Request Format (x-www-form-urlencoded):**
    - `username`: Admin email address (required)
    - `password`: Admin password (required)

    Example:
    ```
    username=<EMAIL>
    password=yourpassword
    ```
    """,
    response_model=TokenResponse,
    responses={
        200: {
            "description": "Admin successfully authenticated",
            "content": {
                "application/json": {
                    "examples": {
                        "Admin": {
                            "summary": "Successful Admin Login",
                            "value": {
                                "success": True,
                                "message": "Admin login successful",
                                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                                "token_type": "bearer",
                                "admin_id": "123e4567-e89b-12d3-a456-426614174000",
                                "email": "<EMAIL>",
                                "created_at": "2025-04-16T12:00:00.000Z",
                            },
                        },
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid input data",
            "content": {"application/json": {"example": {"detail": "Invalid email or password"}}},
        },
        401: {
            "description": "Unauthorized - Invalid credentials",
            "content": {"application/json": {"example": {"detail": "Incorrect email or password"}}},
        },
        500: {
            "description": "Internal Server Error - Authentication service issue",
            "content": {"application/json": {"example": {"detail": "Error during login"}}},
        },
    },
)
async def admin_login(form_data: OAuth2PasswordRequestForm = Depends()):
    try:
        response = await admin_service.login(
            email=form_data.username, password=form_data.password
        )
        return JSONResponse(content=response)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))
