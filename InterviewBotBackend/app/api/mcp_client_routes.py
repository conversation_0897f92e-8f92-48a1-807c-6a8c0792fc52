import asyncio
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>outer, Depends, status
from app.core.auth_guard import BaseAuthGuard, roles_required
from fastapi.responses import JSONResponse
from app.services.mcp_service import MCPService
from app.schemas.mcp_tool_schema import (
    ToolCallRequest,
    CandidateSuitabilityPreSchema,
    JobDescriptionSchema,
    EditJobDescriptionSchema,
    BuildResumeSchema,
)
from app.models.organization import Organization
from app.db.session import SessionLocal
from app.schemas.user_schema import RegistrationType

mcp_client_router = APIRouter(prefix="/mcp-client", tags=["mcp-client"])
mcp_service = MCPService(SessionLocal())


# GET all available tools
@mcp_client_router.get(
    "/tools",
    summary="List Available Tools",
    description="""
    Fetches a list of all available tools from the MCP client.
    Requires authentication.
    """,
    responses={
        200: {
            "description": "Successfully retrieved list of tools",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Tools retrieved successfully",
                        "data": [
                            {
                                "name": "load_job_description",
                                "description": "download the job description from the drive url",
                                "inputSchema": {
                                    "properties": {
                                        "link": {
                                            "description": "Google Drive link to the job description file.",
                                            "format": "uri",
                                            "maxLength": 2083,
                                            "minLength": 1,
                                            "title": "Link",
                                            "type": "string",
                                        }
                                    },
                                    "required": ["link"],
                                    "title": "JobLoadSchema",
                                    "type": "object",
                                },
                            }
                        ],
                    }
                }
            },
        },
        401: {"description": "Unauthorized - Invalid or missing token"},
        500: {
            "description": "Internal Server Error - Could not fetch tools",
            "content": {
                "application/json": {
                    "example": {
                        "status": "error",
                        "message": "Failed to list tools: <error message>",
                    }
                }
            },
        },
    },
)
async def list_tools(
    current_user: dict = Depends(BaseAuthGuard()),
):
    try:
        response = await mcp_service.get_tools()
        return JSONResponse(content=response)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


# POST to call a tool
@mcp_client_router.post(
    "/tools/call",
    summary="Call a Tool for a specific Interview",
    description="""
    Executes a specified tool (e.g., generate agenda, check suitability)
    for a given interview, providing the tool name and required arguments.
    Requires authentication, and the user's organization must own the interview.
    """,
    responses={
        # Keep 200 response as before
        200: {
            "description": "Tool executed successfully",
            # ... (content omitted for brevity)
        },
        400: {  # Refined description
            "description": "Bad Request - Invalid arguments for the tool itself",
            # ... (content omitted for brevity)
        },
        401: {"description": "Unauthorized - Invalid or missing authentication token"},
        403: {  # Added Forbidden
            "description": "Forbidden - Organization does not own the specified interview",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Forbidden: You do not have permission to access tools for this interview."
                    }
                }
            },
        },
        404: {  # Added Not Found
            "description": "Not Found - The specified interview ID does not exist",
            "content": {"application/json": {"example": {"detail": "Interview not found"}}},
        },
        422: {  # Added Unprocessable Entity (FastAPI handles missing/invalid request body fields)
            "description": "Validation Error - Missing or invalid request body parameters (e.g., missing interview_id)",
        },
        # Keep 500 response as before
        500: {
            "description": "Internal Server Error - Tool execution failed",
            # ... (content omitted for brevity)
        },
    },
)
async def call_tool(
    request: ToolCallRequest,
    current_user: Organization = Depends(BaseAuthGuard()),  # Get current org
):
    """
    Handles incoming requests to call a specific tool associated with an interview.
    Validates ownership before proceeding.
    """
    try:
        # Pass the full request and the current organization to the service
        response_data = await mcp_service.execute_tool(
            request=request,
            current_user=current_user,
        )
        # Return standard success response structure if execute_tool returns it
        return JSONResponse(content=response_data, status_code=status.HTTP_200_OK)
    except Exception as e:
        # Catch unexpected errors during the service call or response processing
        # Log the exception details here for debugging
        # logger.error(f"Unexpected error calling tool: {e}", exc_info=True)
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@mcp_client_router.post(
    "/tools/suitability-check-tool",
    summary="Call a Tool to check suitability",
    description="""
    Executes a suitability tool
    for a given interview, providing the tool name and required arguments.
    Requires authentication, and the user's organization must own the interview.
    """,
    responses={
        # Keep 200 response as before
        200: {
            "description": "Tool executed successfully",
            # ... (content omitted for brevity)
        },
        400: {  # Refined description
            "description": "Bad Request - Invalid arguments for the tool itself",
            # ... (content omitted for brevity)
        },
        401: {"description": "Unauthorized - Invalid or missing authentication token"},
        422: {  # Added Unprocessable Entity (FastAPI handles missing/invalid request body fields)
            "description": "Validation Error - Missing or invalid request body parameters (e.g., missing links)",
        },
        # Keep 500 response as before
        500: {
            "description": "Internal Server Error - Tool execution failed",
            # ... (content omitted for brevity)
        },
    },
)
async def call_suitability_tool(
    request: CandidateSuitabilityPreSchema,
    current_org: Organization = Depends(
        roles_required([RegistrationType.organization])
    ),  # Get current org
):
    """
    Handles incoming requests to call a specific tool associated with an interview.
    Validates ownership before proceeding.
    """
    try:
        # Pass the full request and the current organization to the service
        response_data = await mcp_service.execute_pre_candidate_suitability_tool(
            request=request,
            current_org=current_org,
        )
        # Return standard success response structure if execute_tool returns it
        return JSONResponse(content=response_data, status_code=status.HTTP_200_OK)
    except Exception as e:
        # Catch unexpected errors during the service call or response processing
        # Log the exception details here for debugging
        # logger.error(f"Unexpected error calling tool: {e}", exc_info=True)
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@mcp_client_router.post(
    "/tools/generate-job-description",
    summary="Generate a Job Description",
    description="""
    Generates a well-structured job description based on experience level and job role.

    This endpoint allows organizations and individuals to create professional job descriptions by providing:
    - Experience level (e.g., Entry-level, Mid-level, Senior)
    - Job role (e.g., Software Engineer, Product Manager)
    - Optional custom prompt to guide the generation

    The generated job description includes:
    - Job title and company information
    - Overview of the role
    - Key responsibilities
    - Requirements and qualifications
    - Hiring process details

    The response includes the generated job description text.

    When called by an organization, the job description will include the organization's details.
    """,
    responses={
        200: {
            "description": "Job description generated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Job description generated successfully",
                        "job_description": "# Senior Software Engineer\n\nCompany: [Your Company Name]\n\n## Job Description\nWe are seeking an experienced Software Engineer to join our team...",
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid arguments",
            "content": {
                "application/json": {"example": {"detail": "Invalid experience level or job role"}}
            },
        },
        401: {"description": "Unauthorized - Invalid or missing authentication token"},
        403: {
            "description": "Forbidden - Authentication required",
            "content": {
                "application/json": {
                    "example": {"detail": "Authentication required to access this endpoint"}
                }
            },
        },
        422: {
            "description": "Validation Error - Missing or invalid request body parameters",
        },
        500: {
            "description": "Internal Server Error - Tool execution failed",
            "content": {
                "application/json": {"example": {"detail": "Failed to generate job description"}}
            },
        },
    },
)
async def generate_job_description(
    request: JobDescriptionSchema,
    current_user: dict = Depends(roles_required([RegistrationType.organization,RegistrationType.individual])),
):
    """
    Handles requests to generate a job description based on experience level and job role.
    """
    try:
        response_data = await mcp_service.generate_job_description(
            request=request,
            current_user=current_user,
        )
        return JSONResponse(content=response_data, status_code=status.HTTP_200_OK)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@mcp_client_router.post(
    "/tools/edit-job-description",
    summary="Edit an Existing Job Description",
    description="""
    Edits an existing job description based on custom instructions.

    This endpoint allows organizations and individuals to improve or modify existing job descriptions by providing:
    - The existing job description text
    - Custom instructions on how to update it (e.g., "Make it more inclusive", "Add remote work policy")

    The response includes the updated job description with the requested changes.

    When called by an organization, the job description will include the organization's details.
    """,
    responses={
        200: {
            "description": "Job description updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Job description updated successfully",
                        "updated_job_description": "# Senior Software Engineer (Remote-Friendly)\n\nCompany: [Your Company Name]\n\n## Job Description\nWe are seeking an experienced Software Engineer to join our diverse team...",
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid arguments",
            "content": {
                "application/json": {
                    "example": {"detail": "Invalid job description or instructions"}
                }
            },
        },
        401: {"description": "Unauthorized - Invalid or missing authentication token"},
        403: {
            "description": "Forbidden - Authentication required",
            "content": {
                "application/json": {
                    "example": {"detail": "Authentication required to access this endpoint"}
                }
            },
        },
        422: {
            "description": "Validation Error - Missing or invalid request body parameters",
        },
        500: {
            "description": "Internal Server Error - Tool execution failed",
            "content": {
                "application/json": {"example": {"detail": "Failed to edit job description"}}
            },
        },
    },
)
async def edit_job_description(
    request: EditJobDescriptionSchema,
    current_user: dict = Depends(roles_required([RegistrationType.organization,RegistrationType.individual])),
):
    """
    Handles requests to edit an existing job description based on custom instructions.
    """
    try:
        response_data = await mcp_service.edit_job_description(
            request=request,
            current_user=current_user,
        )
        return JSONResponse(content=response_data, status_code=status.HTTP_200_OK)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@mcp_client_router.get(
    "/tools/list-resume-templates",
    summary="List Available Resume Templates",
    description="""
    Retrieves a list of all available resume templates with their descriptions and sections.

    This endpoint allows users to see what resume templates are available for building resumes.
    Each template includes:
    - Template ID (used for building resumes)
    - Template name and description
    - List of sections included in the template

    Available for both organizations and individuals.
    """,
    responses={
        200: {
            "description": "Resume templates retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Resume templates retrieved successfully",
                        "data": {
                            "templates": [
                                {
                                    "id": "professional",
                                    "name": "Professional Template",
                                    "description": "A clean, professional template suitable for most industries",
                                    "sections": ["Contact Info", "Professional Summary", "Work Experience", "Education", "Skills"]
                                },
                                {
                                    "id": "modern",
                                    "name": "Modern Template",
                                    "description": "A contemporary template with project highlights",
                                    "sections": ["Contact Info", "Professional Summary", "Work Experience", "Projects", "Education", "Skills"]
                                },
                                {
                                    "id": "academic",
                                    "name": "Academic Template",
                                    "description": "Designed for academic and research positions",
                                    "sections": ["Contact Info", "Objective", "Education", "Research Experience", "Publications", "Skills"]
                                }
                            ]
                        }
                    }
                }
            },
        },
        401: {"description": "Unauthorized - Invalid or missing authentication token"},
        500: {
            "description": "Internal Server Error - Failed to retrieve templates",
            "content": {
                "application/json": {"example": {"detail": "Failed to retrieve resume templates"}}
            },
        },
    },
)
async def list_resume_templates(
    current_user: dict = Depends(roles_required([RegistrationType.organization, RegistrationType.individual])),
):
    """
    Handles requests to list all available resume templates.
    """
    try:
        response_data = await mcp_service.list_resume_templates(
            current_user=current_user,
        )
        return JSONResponse(content=response_data, status_code=status.HTTP_200_OK)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@mcp_client_router.post(
    "/tools/build-resume",
    summary="Build a Professional Resume",
    description="""
    Builds a professional resume using a selected template and user-provided data.

---

### 🔹 Required Top-Level Fields

| Field | Type | Description |
|-------|------|-------------|
| `template_id` | `str` | Template to use (`professional`, `modern`, `academic`) |
| `contact_info` | `ContactInfo` | Candidate’s personal contact details |
| `education` | `List[Education]` | Education history (at least one entry) |
| `skills` | `List[str]` | List of core technical and soft skills |

---

### 🔸 Optional Top-Level Fields

| Field | Type | Description |
|-------|------|-------------|
| `professional_summary` | `str` | Summary of experience (for professional/modern templates) |
| `objective` | `str` | Academic/career goal (for academic template) |
| `work_experience` | `List[WorkExperience]` | Past job roles with timeline |
| `projects` | `List[Project]` | Project details |
| `certifications` | `List[str]` | List of certifications |
| `research_experience` | `List[ResearchExperience]` | Academic research roles |
| `publications` | `List[str]` | List of research papers or publications |
| `additional_instructions` | `str` | Any user-specific instructions |

---

### 🧾 Field Definitions

#### `ContactInfo` (Required)
- `name` (str) – ✅
- `email` (str) – ✅
- `phone` (str) – ✅
- `location` (str) – ✅
- `linkedin` (str) – optional
- `website` (str) – optional

#### `WorkExperience`
- `company` (str) – ✅
- `position` (str) – ✅
- `start_date` (str) – ✅
- `end_date` (str) – optional
- `description` (str) – ✅

#### `Education` (Required list, at least 1)
- `institution` (str) – ✅
- `degree` (str) – ✅
- `field_of_study` (str) – optional
- `graduation_date` (str) – ✅
- `gpa` (str) – optional

#### `Project`
- `name` (str) – ✅
- `description` (str) – ✅
- `technologies` (List[str]) – ✅
- `url` (str) – optional

#### `ResearchExperience`
- `institution` (str) – ✅
- `position` (str) – ✅
- `start_date` (str) – ✅
- `end_date` (str) – optional
- `description` (str) – ✅

---
    """,
    responses={
        200: {
            "description": "Resume built successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Resume built successfully",
                        "data": {
                            "resume_id": "resume_abc_123",
                            "template_id": "professional",
                            "created_at": "2024-01-15T10:30:00Z",
                            "content": {
                                "contact_info": {
                                    "name": "John Doe",
                                    "email": "<EMAIL>",
                                    "phone": "******-0123",
                                    "location": "San Francisco, CA",
                                },
                                "professional_summary": "Experienced software engineer with 5+ years...",
                                "sections": "...",
                            },
                        },
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid template ID or missing required fields",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Invalid template ID or missing required contact information"
                    }
                }
            },
        },
        401: {"description": "Unauthorized - Invalid or missing authentication token"},
        422: {
            "description": "Validation Error - Missing or invalid request body parameters",
        },
        500: {
            "description": "Internal Server Error - Resume building failed",
            "content": {"application/json": {"example": {"detail": "Failed to build resume"}}},
        },
    },
)
async def build_resume(
    request: BuildResumeSchema,
    current_user: dict = Depends(roles_required([RegistrationType.organization, RegistrationType.individual])),
):
    """
    Handles requests to build a resume using the provided template and data.
    """
    try:
        response_data = await mcp_service.build_resume(
            request=request,
            current_user=current_user,
        )
        return JSONResponse(content=response_data, status_code=status.HTTP_200_OK)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))
