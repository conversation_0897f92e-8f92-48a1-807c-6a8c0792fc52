from app.schemas.user_schema import RegistrationType
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.db.session import get_db
from app.core.auth_guard import BaseAuthGuard, roles_required
from app.services.subscription_service import SubscriptionService
from app.schemas.subscription_schema import (
    SubscriptionPlanCreate,
    SubscriptionPlanUpdate,
    SubscriptionPlanResponse,
    UserSubscriptionResponse,
    OrganizationSubscriptionResponse,
    SubscriptionUsageResponse,
    SubscriptionStatsResponse,
    UserSubscriptionCreateRequest,
    OrganizationSubscriptionCreateRequest,
    BulkSubscriptionUpdateRequest,
    BulkOrganizationSubscriptionUpdateRequest,
    PlanType,
)

router = APIRouter(prefix="/subscriptions", tags=["subscriptions"])


# Admin Routes - Subscription Plan Management
@router.post("/admin/plans", response_model=SubscriptionPlanResponse, status_code=status.HTTP_201_CREATED)
async def create_subscription_plan(
    plan_data: SubscriptionPlanCreate,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(roles_required([RegistrationType.admin])),
):
    """Create a new subscription plan (Admin only)."""
    subscription_service = SubscriptionService(db)
    return subscription_service.create_subscription_plan(plan_data)


@router.put("/admin/plans/{plan_id}", response_model=SubscriptionPlanResponse)
async def update_subscription_plan(
    plan_id: str,
    plan_data: SubscriptionPlanUpdate,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(roles_required([RegistrationType.admin])),
):
    """Update a subscription plan (Admin only)."""
    subscription_service = SubscriptionService(db)
    return subscription_service.update_subscription_plan(plan_id, plan_data)


@router.delete("/admin/plans/{plan_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(roles_required([RegistrationType.admin])),
):
    """Delete (deactivate) a subscription plan (Admin only)."""
    subscription_service = SubscriptionService(db)
    subscription_service.delete_subscription_plan(plan_id)


@router.post("/admin/assign-subscription")
async def assign_subscription(
    user_ids: List[str],
    subscription_plan_id: str,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(roles_required([RegistrationType.admin])),
):
    """Assign subscription plan to user(s). Supports both single and bulk assignment."""
    subscription_service = SubscriptionService(db)
    
    # Handle single user (return single response)
    if len(user_ids) == 1:
        return subscription_service.assign_subscription_to_user(
            user_ids[0],
            subscription_plan_id
        )
    
    # Handle multiple users (return bulk response)
    results = []
    errors = []

    for user_id in user_ids:
        try:
            result = subscription_service.assign_subscription_to_user(
                user_id,
                subscription_plan_id
            )
            results.append({"user_id": user_id, "status": "success", "subscription": result})
        except Exception as e:
            errors.append({"user_id": user_id, "status": "error", "message": str(e)})

    return {
        "success_count": len(results),
        "error_count": len(errors),
        "results": results,
        "errors": errors
    }


@router.post("/admin/assign-organization-subscription")
async def assign_organization_subscription(
    organization_ids: List[str],
    subscription_plan_id: str,
    db: Session = Depends(get_db),
    current_admin: dict = Depends(roles_required([RegistrationType.admin])),
):
    """Assign subscription plan to organization(s). Supports both single and bulk assignment."""
    subscription_service = SubscriptionService(db)
    
    # Handle single organization (return single response)
    if len(organization_ids) == 1:
        return subscription_service.assign_subscription_to_organization(
            organization_ids[0],
            subscription_plan_id
        )
    
    # Handle multiple organizations (return bulk response)
    results = []
    errors = []

    for org_id in organization_ids:
        try:
            result = subscription_service.assign_subscription_to_organization(
                org_id,
                subscription_plan_id
            )
            results.append({"organization_id": org_id, "status": "success", "subscription": result})
        except Exception as e:
            errors.append({"organization_id": org_id, "status": "error", "message": str(e)})

    return {
        "success_count": len(results),
        "error_count": len(errors),
        "results": results,
        "errors": errors
    }


@router.get("/admin/stats", response_model=SubscriptionStatsResponse)
async def get_subscription_stats(
    db: Session = Depends(get_db),
    current_admin: dict = Depends(roles_required([RegistrationType.admin])),
):
    """Get subscription statistics (Admin only)."""
    subscription_service = SubscriptionService(db)
    return subscription_service.get_subscription_stats()


# User/Organization Routes - Subscription Information
@router.get("/my-subscription")
async def get_my_subscription(
    db: Session = Depends(get_db),
    current_user: dict = Depends(BaseAuthGuard())
):
    """Get current user's or organization's complete subscription details including usage and limits."""
    subscription_service = SubscriptionService(db)

    # Check if it's an individual user or organization
    if current_user["registration_type"] == RegistrationType.individual:
        # Get subscription details for individual user
        subscription = subscription_service.get_user_subscription(current_user["user_id"])

        # Get usage information
        usage = subscription_service.get_subscription_usage(current_user["user_id"])

    elif current_user["registration_type"] == RegistrationType.organization:
        # Get subscription details for organization
        subscription = subscription_service.get_organization_subscription(current_user["user_id"])

        # Get usage information
        usage = subscription_service.get_organization_subscription_usage(current_user["user_id"])

    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only individual users and organizations can access subscription information"
        )

    # Return comprehensive subscription information
    return {"subscription": subscription, "usage": usage}


# Unified Routes - Plans (authenticated users, admin privileges if admin)
@router.get("/plans", response_model=List[SubscriptionPlanResponse])
async def get_plans(
    include_inactive: bool = False,
    db: Session = Depends(get_db),
    current_user: dict = Depends(BaseAuthGuard())
):
    """Get subscription plans filtered by user type. Admins can include inactive plans and see all plan types."""
    subscription_service = SubscriptionService(db)

    # Check if user is admin
    is_admin = current_user.get("registration_type") == RegistrationType.admin
    user_type = current_user.get("registration_type")

    # If user is admin, they can choose to include inactive plans and see all plan types
    if is_admin:
        plan_type = None  # Admins can see all plan types
    else:
        include_inactive = False  # Non-admins can only see active plans
        # Filter plans by user type
        if user_type == RegistrationType.individual:
            plan_type = PlanType.individual
        elif user_type == RegistrationType.organization:
            plan_type = PlanType.organization
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid user type for accessing subscription plans"
            )

    return subscription_service.get_all_subscription_plans(include_inactive, plan_type)


@router.get("/plans/{plan_id}", response_model=SubscriptionPlanResponse)
async def get_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(BaseAuthGuard())
):
    """Get details of a specific subscription plan (authenticated users only)."""
    subscription_service = SubscriptionService(db)
    return subscription_service.get_subscription_plan(plan_id)
