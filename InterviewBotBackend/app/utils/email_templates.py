"""
Email Templates for Email Verification
Contains HTML templates for various email types
"""

def get_otp_verification_email_template(user_name: str, otp_code: str, user_type: str = "candidate") -> str:
    """
    Generate OTP verification email HTML template
    
    Args:
        user_name: Name of the user
        otp_code: OTP code to include in email
        user_type: Type of user (candidate/organization)
        
    Returns:
        str: HTML email content
    """
    return f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - EvalFast</title>
    <style>
        body {{ 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f8f9fa; 
        }}
        .container {{ 
            max-width: 600px; 
            margin: 0 auto; 
            background-color: #ffffff; 
            border-radius: 10px; 
            overflow: hidden; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
        }}
        .header {{ 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }}
        .header h1 {{ 
            margin: 0; 
            font-size: 24px; 
            font-weight: bold; 
        }}
        .content {{ 
            padding: 30px 20px; 
        }}
        .otp-box {{ 
            background-color: #f8f9fa; 
            border: 2px dashed #667eea; 
            padding: 20px; 
            text-align: center; 
            margin: 20px 0; 
            border-radius: 8px; 
        }}
        .otp-code {{ 
            font-size: 32px; 
            font-weight: bold; 
            color: #667eea; 
            letter-spacing: 8px; 
            margin: 10px 0; 
        }}
        .footer {{ 
            background-color: #343a40; 
            color: white; 
            padding: 20px; 
            text-align: center; 
            font-size: 14px; 
        }}
        .warning {{ 
            background-color: #fff3cd; 
            border: 1px solid #ffeaa7; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 20px 0; 
        }}
        .highlight {{
            background-color: #e8f4fd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Email Verification</h1>
            <p style="margin: 10px 0 0 0;">Verify your email to complete registration</p>
        </div>
        
        <div class="content">
            <h2>Hello {user_name}!</h2>
            <p>Thank you for registering with <strong>EvalFast</strong> as a {user_type}. To complete your registration and secure your account, please verify your email address using the OTP code below:</p>
            
            <div class="otp-box">
                <p style="margin: 0; font-size: 16px; color: #666;">Your verification code is:</p>
                <div class="otp-code">{otp_code}</div>
                <p style="margin: 0; font-size: 14px; color: #666;">This code will expire in 10 minutes</p>
            </div>
            
            <p>Enter this code in the verification screen to complete your registration and start using EvalFast.</p>
            
            <div class="warning">
                <strong>⚠️ Security Notice:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Never share this code with anyone</li>
                    <li>EvalFast will never ask for this code via phone or email</li>
                    <li>If you didn't request this verification, please ignore this email</li>
                </ul>
            </div>
            
            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
            
            <p>Welcome to EvalFast!<br>
            <strong>The EvalFast Team</strong></p>
        </div>
        
        <div class="footer">
            <p><strong>EvalFast</strong> - Your Interview Preparation Platform</p>
            <p style="font-size: 12px; margin-top: 10px;">
                This is an automated email. Please do not reply to this message.
            </p>
        </div>
    </div>
</body>
</html>
    """


def get_welcome_email_template(user_name: str, user_type: str = "candidate") -> str:
    """
    Generate welcome email template after successful verification
    
    Args:
        user_name: Name of the user
        user_type: Type of user (candidate/organization)
        
    Returns:
        str: HTML email content
    """
    return f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to EvalFast!</title>
    <style>
        body {{ 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f8f9fa; 
        }}
        .container {{ 
            max-width: 600px; 
            margin: 0 auto; 
            background-color: #ffffff; 
            border-radius: 10px; 
            overflow: hidden; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
        }}
        .header {{ 
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }}
        .header h1 {{ 
            margin: 0; 
            font-size: 24px; 
            font-weight: bold; 
        }}
        .content {{ 
            padding: 30px 20px; 
        }}
        .footer {{ 
            background-color: #343a40; 
            color: white; 
            padding: 20px; 
            text-align: center; 
            font-size: 14px; 
        }}
        .cta-button {{ 
            background-color: #667eea; 
            color: white; 
            padding: 12px 30px; 
            border: none; 
            border-radius: 25px; 
            font-weight: bold; 
            text-decoration: none; 
            display: inline-block; 
            margin: 10px; 
            transition: all 0.3s; 
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Welcome to EvalFast!</h1>
            <p style="margin: 10px 0 0 0;">Your email has been successfully verified</p>
        </div>
        
        <div class="content">
            <h2>Hello {user_name}!</h2>
            <p>Congratulations! Your email has been successfully verified and your EvalFast account is now active.</p>
            
            <p>As a verified {user_type}, you now have access to all EvalFast features:</p>
            
            <ul>
                <li>🎯 AI-powered interview simulations</li>
                <li>📊 Comprehensive skill assessments</li>
                <li>🤝 Direct connections with employers</li>
                <li>📈 Career growth tracking and insights</li>
            </ul>
            
            <p style="text-align: center;">
                <a href="https://evalfast.com/dashboard" class="cta-button">Get Started Now</a>
            </p>
            
            <p>If you have any questions or need assistance, our support team is here to help.</p>
            
            <p>Best regards,<br>
            <strong>The EvalFast Team</strong></p>
        </div>
        
        <div class="footer">
            <p><strong>EvalFast</strong> - Your Interview Preparation Platform</p>
            <p style="font-size: 12px; margin-top: 10px;">
                This is an automated email. Please do not reply to this message.
            </p>
        </div>
    </div>
</body>
</html>
    """
