"""
PDF Generator utility for creating resume PDFs with proper error handling.
"""

import io
import json
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import (
    SimpleDocTemplate,
    Paragraph,
    Spacer,
    Table,
    TableStyle,
    PageBreak,
    Flowable,
    KeepTogether,
)
from reportlab.pdfgen import canvas
import logging

# Set up logging for debugging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_interview_report_pdf(interview_data):
    """
    Generate a PDF report for an interview.

    Args:
        interview_data: Dictionary containing interview data

    Returns:
        BytesIO object containing the PDF
    """
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(
        buffer,
        pagesize=letter,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=72,
        title=f"Interview Report - {interview_data.get('candidate_name', 'Candidate')}",
    )

    # Styles
    styles = getSampleStyleSheet()

    # Create custom styles by modifying the existing ones
    heading1_style = styles["Heading1"]
    heading1_style.fontSize = 18
    heading1_style.spaceAfter = 12
    heading1_style.textColor = colors.darkblue

    heading2_style = styles["Heading2"]
    heading2_style.fontSize = 14
    heading2_style.spaceAfter = 10
    heading2_style.textColor = colors.darkblue

    normal_style = styles["Normal"]
    normal_style.fontSize = 10
    normal_style.spaceAfter = 6

    # Create a bold style for labels
    bold_style = ParagraphStyle(
        name="Bold", parent=styles["Normal"], fontSize=10, fontName="Helvetica-Bold", spaceAfter=2
    )

    # Create a style for answers and content
    content_style = ParagraphStyle(
        name="Content", parent=styles["Normal"], fontSize=10, leftIndent=20, spaceAfter=6
    )

    # Build content
    content = []

    # Title
    content.append(Paragraph(f"Interview Report", styles["Title"]))
    content.append(Spacer(1, 0.25 * inch))

    # Basic Information
    content.append(Paragraph("Basic Information", heading1_style))

    basic_info = [
        ["Candidate Name", interview_data.get("candidate_name", "N/A")],
        ["Candidate Email", interview_data.get("candidate_email", "N/A")],
        ["Job Role", interview_data.get("job_role", "N/A")],
        ["Skill Sets", ", ".join(interview_data.get("skill_sets", []))],
        ["Experience Level", interview_data.get("experience_level", "N/A")],
        [
            "Interview Date",
            (
                datetime.fromisoformat(interview_data.get("created_at")).strftime("%B %d, %Y")
                if interview_data.get("created_at")
                else "N/A"
            ),
        ],
    ]

    basic_table = Table(basic_info, colWidths=[2 * inch, 4 * inch])
    basic_table.setStyle(
        TableStyle(
            [
                ("BACKGROUND", (0, 0), (0, -1), colors.lightgrey),
                ("TEXTCOLOR", (0, 0), (0, -1), colors.black),
                ("ALIGN", (0, 0), (0, -1), "LEFT"),
                ("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
                ("FONTSIZE", (0, 0), (-1, -1), 10),
                ("BOTTOMPADDING", (0, 0), (-1, -1), 6),
                ("BACKGROUND", (1, 0), (-1, -1), colors.white),
                ("GRID", (0, 0), (-1, -1), 1, colors.black),
            ]
        )
    )

    content.append(basic_table)
    content.append(Spacer(1, 0.25 * inch))

    # Verification Result
    if interview_data.get("verification_result"):
        content.append(Paragraph("Interview Verification", heading1_style))
        try:
            verification_data = interview_data.get("verification_result", {})
            if isinstance(verification_data, str):
                verification_data = json.loads(verification_data)

            # Handle the new structure with agendas
            if "agendas" in verification_data and isinstance(verification_data["agendas"], list):
                for agenda_item in verification_data["agendas"]:
                    agenda_title = agenda_item.get("agenda", "N/A")
                    content.append(Paragraph(f"Agenda: {agenda_title}", heading2_style))

                    if "questions" in agenda_item and isinstance(agenda_item["questions"], list):
                        for question_item in agenda_item["questions"]:
                            question_text = question_item.get("question", "N/A")
                            question_status = question_item.get("status", "N/A")
                            question_answer = question_item.get("answer", "N/A")

                            content.append(
                                Paragraph(f"<b>Question:</b> {question_text}", normal_style)
                            )
                            content.append(
                                Paragraph(f"<b>Status:</b> {question_status}", normal_style)
                            )
                            content.append(
                                Paragraph(f"<b>Answer:</b> {question_answer}", normal_style)
                            )
                            content.append(Spacer(1, 0.1 * inch))
            else:
                # Fallback for old format
                verification_text = f"Verification Status: {verification_data.get('verification_status', 'N/A')}\n\n"
                verification_text += f"Confidence: {verification_data.get('confidence', 'N/A')}\n\n"
                verification_text += f"Details: {verification_data.get('details', 'N/A')}"

                content.append(Paragraph(verification_text, normal_style))

            content.append(Spacer(1, 0.25 * inch))
        except (json.JSONDecodeError, TypeError) as e:
            content.append(Paragraph(f"Error parsing verification data: {str(e)}", normal_style))
            content.append(Spacer(1, 0.25 * inch))

    # Technical Evaluation
    if interview_data.get("technical_evaluation_result"):
        content.append(Paragraph("Technical Evaluation", heading1_style))
        try:
            tech_data = interview_data.get("technical_evaluation_result", {})
            if isinstance(tech_data, str):
                tech_data = json.loads(tech_data)

            if isinstance(tech_data, dict):
                # Handle the new structure with technical_scores
                if "technical_scores" in tech_data and isinstance(
                    tech_data["technical_scores"], dict
                ):
                    content.append(Paragraph("Technical Scores:", heading2_style))

                    score_data = [["Category", "Score"]]
                    for category, score in tech_data["technical_scores"].items():
                        # Convert snake_case to Title Case for display
                        display_category = " ".join(
                            word.capitalize() for word in category.split("_")
                        )
                        score_data.append([display_category, str(score)])

                    score_table = Table(score_data, colWidths=[4 * inch, 2 * inch])
                    score_table.setStyle(
                        TableStyle(
                            [
                                ("BACKGROUND", (0, 0), (-1, 0), colors.grey),
                                ("TEXTCOLOR", (0, 0), (-1, 0), colors.whitesmoke),
                                ("ALIGN", (0, 0), (-1, 0), "CENTER"),
                                ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                                ("FONTSIZE", (0, 0), (-1, -1), 10),
                                ("BOTTOMPADDING", (0, 0), (-1, -1), 6),
                                ("BACKGROUND", (0, 1), (-1, -1), colors.white),
                                ("GRID", (0, 0), (-1, -1), 1, colors.black),
                            ]
                        )
                    )

                    content.append(score_table)
                    content.append(Spacer(1, 0.15 * inch))

                # Overall technical score
                overall_score = tech_data.get("overall_technical_score", "N/A")
                content.append(
                    Paragraph(f"<b>Overall Technical Score:</b> {overall_score}", heading2_style)
                )

                # Strengths
                if "strengths" in tech_data and isinstance(tech_data["strengths"], list):
                    content.append(Paragraph("Strengths:", heading2_style))
                    for strength in tech_data["strengths"]:
                        content.append(Paragraph(f"• {strength}", normal_style))
                    content.append(Spacer(1, 0.1 * inch))

                # Weaknesses
                if "weaknesses" in tech_data and isinstance(tech_data["weaknesses"], list):
                    content.append(Paragraph("Weaknesses:", heading2_style))
                    for weakness in tech_data["weaknesses"]:
                        content.append(Paragraph(f"• {weakness}", normal_style))
                    content.append(Spacer(1, 0.1 * inch))

                # Red Flags
                if (
                    "red_flags" in tech_data
                    and isinstance(tech_data["red_flags"], list)
                    and tech_data["red_flags"]
                ):
                    content.append(Paragraph("Red Flags:", heading2_style))
                    for flag in tech_data["red_flags"]:
                        content.append(Paragraph(f"• {flag}", normal_style))
                    content.append(Spacer(1, 0.1 * inch))

                # Recommendation
                if "recommendation" in tech_data:
                    content.append(
                        Paragraph(
                            f"<b>Recommendation:</b> {tech_data['recommendation']}", heading2_style
                        )
                    )

                # Fallback for old format
                if "technical_score" in tech_data:
                    content.append(
                        Paragraph(
                            f"Technical Score: {tech_data.get('technical_score', 'N/A')}",
                            heading2_style,
                        )
                    )

                if "technical_feedback" in tech_data:
                    content.append(Paragraph("Technical Feedback:", heading2_style))
                    content.append(
                        Paragraph(tech_data.get("technical_feedback", "N/A"), normal_style)
                    )

                # Add detailed scores if available in old format
                if "detailed_scores" in tech_data and isinstance(
                    tech_data["detailed_scores"], list
                ):
                    content.append(Paragraph("Detailed Scores:", heading2_style))

                    score_data = [["Category", "Score", "Comments"]]
                    for score in tech_data["detailed_scores"]:
                        score_data.append(
                            [
                                score.get("category", "N/A"),
                                str(score.get("score", "N/A")),
                                score.get("comments", "N/A"),
                            ]
                        )

                    score_table = Table(score_data, colWidths=[2 * inch, 1 * inch, 3 * inch])
                    score_table.setStyle(
                        TableStyle(
                            [
                                ("BACKGROUND", (0, 0), (-1, 0), colors.grey),
                                ("TEXTCOLOR", (0, 0), (-1, 0), colors.whitesmoke),
                                ("ALIGN", (0, 0), (-1, 0), "CENTER"),
                                ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                                ("FONTSIZE", (0, 0), (-1, -1), 10),
                                ("BOTTOMPADDING", (0, 0), (-1, -1), 6),
                                ("BACKGROUND", (0, 1), (-1, -1), colors.white),
                                ("GRID", (0, 0), (-1, -1), 1, colors.black),
                            ]
                        )
                    )

                    content.append(score_table)
            else:
                content.append(Paragraph(str(tech_data), normal_style))

            content.append(Spacer(1, 0.25 * inch))
        except (json.JSONDecodeError, TypeError) as e:
            content.append(
                Paragraph(f"Error parsing technical evaluation data: {str(e)}", normal_style)
            )
            content.append(Spacer(1, 0.25 * inch))

    # Feedback Result
    if interview_data.get("feedback_result"):
        content.append(Paragraph("Interview Feedback", heading1_style))
        try:
            feedback_data = interview_data.get("feedback_result", {})
            if isinstance(feedback_data, str):
                feedback_data = json.loads(feedback_data)

            if isinstance(feedback_data, dict):
                # Handle the new structure with agendas
                if "agendas" in feedback_data and isinstance(feedback_data["agendas"], list):
                    for agenda_item in feedback_data["agendas"]:
                        agenda_title = agenda_item.get("agenda", "N/A")
                        content.append(Paragraph(f"Agenda: {agenda_title}", heading2_style))

                        if "questions" in agenda_item and isinstance(
                            agenda_item["questions"], list
                        ):
                            for question_item in agenda_item["questions"]:
                                question_text = question_item.get("question", "N/A")
                                question_status = question_item.get("status", "N/A")
                                question_answer = question_item.get("answer", "N/A")
                                question_feedback = question_item.get("feedback", "N/A")

                                content.append(
                                    Paragraph(f"<b>Question:</b> {question_text}", normal_style)
                                )
                                content.append(
                                    Paragraph(f"<b>Status:</b> {question_status}", normal_style)
                                )
                                if question_answer:
                                    content.append(
                                        Paragraph(f"<b>Answer:</b> {question_answer}", normal_style)
                                    )
                                if question_feedback:
                                    content.append(
                                        Paragraph(
                                            f"<b>Feedback:</b> {question_feedback}", normal_style
                                        )
                                    )
                                content.append(Spacer(1, 0.1 * inch))
                else:
                    # Fallback for old format
                    overall_feedback = feedback_data.get("overall_feedback", "N/A")
                    strengths = feedback_data.get("strengths", [])
                    areas_for_improvement = feedback_data.get("areas_for_improvement", [])

                    content.append(Paragraph("Overall Feedback:", heading2_style))
                    content.append(Paragraph(overall_feedback, normal_style))

                    content.append(Paragraph("Strengths:", heading2_style))
                    for strength in strengths:
                        content.append(Paragraph(f"• {strength}", normal_style))

                    content.append(Paragraph("Areas for Improvement:", heading2_style))
                    for area in areas_for_improvement:
                        content.append(Paragraph(f"• {area}", normal_style))
            else:
                content.append(Paragraph(str(feedback_data), normal_style))

            content.append(Spacer(1, 0.25 * inch))
        except (json.JSONDecodeError, TypeError) as e:
            content.append(Paragraph(f"Error parsing feedback data: {str(e)}", normal_style))
            content.append(Spacer(1, 0.25 * inch))

    # Recommendation Result
    if interview_data.get("recommendation_result"):
        content.append(Paragraph("Hiring Recommendation", heading1_style))
        try:
            recommendation_data = interview_data.get("recommendation_result", {})
            if isinstance(recommendation_data, str):
                recommendation_data = json.loads(recommendation_data)

            if isinstance(recommendation_data, dict):
                # Handle the new structure with areas to focus upon
                if "areas_to_focus_upon" in recommendation_data and isinstance(
                    recommendation_data["areas_to_focus_upon"], list
                ):
                    content.append(Paragraph("Areas to Focus Upon:", heading2_style))

                    for i, area in enumerate(recommendation_data["areas_to_focus_upon"]):
                        title = area.get("title", "N/A")
                        reason = area.get("reason", "N/A")

                        content.append(Paragraph(f"<b>{i+1}. {title}</b>", normal_style))
                        content.append(Paragraph(f"   <b>Reason:</b> {reason}", normal_style))
                        content.append(Spacer(1, 0.05 * inch))

                    content.append(Spacer(1, 0.1 * inch))

                # Next steps to improve each area
                if "next_steps_to_improve_each_area" in recommendation_data and isinstance(
                    recommendation_data["next_steps_to_improve_each_area"], list
                ):
                    content.append(Paragraph("Next Steps to Improve:", heading2_style))

                    for area_steps in recommendation_data["next_steps_to_improve_each_area"]:
                        focus_area = area_steps.get("focus_area", "N/A")
                        steps = area_steps.get("steps", [])

                        content.append(Paragraph(f"<b>Focus Area:</b> {focus_area}", normal_style))
                        for step in steps:
                            content.append(Paragraph(f"• {step}", normal_style))
                        content.append(Spacer(1, 0.05 * inch))

                # Fallback for old format
                if "recommendation" in recommendation_data:
                    content.append(
                        Paragraph(
                            f"<b>Recommendation:</b> {recommendation_data.get('recommendation', 'N/A')}",
                            heading2_style,
                        )
                    )

                if "confidence" in recommendation_data:
                    content.append(
                        Paragraph(
                            f"<b>Confidence:</b> {recommendation_data.get('confidence', 'N/A')}",
                            normal_style,
                        )
                    )

                if "justification" in recommendation_data:
                    content.append(Paragraph("Justification:", heading2_style))
                    content.append(
                        Paragraph(recommendation_data.get("justification", "N/A"), normal_style)
                    )
            else:
                content.append(Paragraph(str(recommendation_data), normal_style))

            content.append(Spacer(1, 0.25 * inch))
        except (json.JSONDecodeError, TypeError) as e:
            content.append(Paragraph(f"Error parsing recommendation data: {str(e)}", normal_style))
            content.append(Spacer(1, 0.25 * inch))

    # Interview Summary
    if interview_data.get("interview_summary_result"):
        content.append(Paragraph("Interview Summary", heading1_style))
        try:
            summary_data = interview_data.get("interview_summary_result", {})
            if isinstance(summary_data, str):
                summary_data = json.loads(summary_data)

            if isinstance(summary_data, dict):
                # Get the summary text
                summary = summary_data.get("summary", "N/A")
                content.append(Paragraph(summary, normal_style))
                content.append(Spacer(1, 0.1 * inch))

                # Check for suggestions
                if "suggestions" in summary_data and isinstance(summary_data["suggestions"], list):
                    content.append(Paragraph("Suggestions:", heading2_style))
                    for suggestion in summary_data["suggestions"]:
                        content.append(Paragraph(f"• {suggestion}", normal_style))
                    content.append(Spacer(1, 0.1 * inch))

                # Fallback for old format with key_points
                if "key_points" in summary_data and isinstance(summary_data["key_points"], list):
                    content.append(Paragraph("Key Points:", heading2_style))
                    for point in summary_data["key_points"]:
                        content.append(Paragraph(f"• {point}", normal_style))
            else:
                content.append(Paragraph(str(summary_data), normal_style))

            content.append(Spacer(1, 0.25 * inch))
        except (json.JSONDecodeError, TypeError) as e:
            content.append(Paragraph(f"Error parsing summary data: {str(e)}", normal_style))
            content.append(Spacer(1, 0.25 * inch))

    # Interview Violations
    if interview_data.get("violation_result"):
        content.append(Paragraph("Interview Violations", heading1_style))
        try:
            violation_data = interview_data.get("violation_result", {})
            if isinstance(violation_data, str):
                violation_data = json.loads(violation_data)

            if isinstance(violation_data, dict):
                violations = violation_data.get("violations", [])

                if violations:
                    for violation in violations:
                        v_type = violation.get("type", "N/A")
                        v_description = violation.get("description", "N/A")
                        v_timestamp = violation.get("timestamp", "N/A")

                        content.append(Paragraph(f"Violation Type: {v_type}", heading2_style))
                        content.append(Paragraph(f"Description: {v_description}", normal_style))
                        content.append(Paragraph(f"Timestamp: {v_timestamp}", normal_style))
                        content.append(Spacer(1, 0.1 * inch))
                else:
                    content.append(Paragraph("No violations detected", normal_style))
            else:
                content.append(Paragraph(str(violation_data), normal_style))

            content.append(Spacer(1, 0.25 * inch))
        except (json.JSONDecodeError, TypeError) as e:
            content.append(Paragraph(f"Error parsing violation data: {str(e)}", normal_style))
            content.append(Spacer(1, 0.25 * inch))

    # Also check for interview_violation_result (alternative field name)
    elif interview_data.get("interview_violation_result"):
        content.append(Paragraph("Interview Violations", heading1_style))
        try:
            violation_data = interview_data.get("interview_violation_result", {})
            if isinstance(violation_data, str):
                violation_data = json.loads(violation_data)

            if isinstance(violation_data, dict):
                violations = violation_data.get("violations", [])

                if violations:
                    for violation in violations:
                        v_type = violation.get("type", "N/A")
                        v_description = violation.get("description", "N/A")
                        v_timestamp = violation.get("timestamp", "N/A")

                        content.append(Paragraph(f"Violation Type: {v_type}", heading2_style))
                        content.append(Paragraph(f"Description: {v_description}", normal_style))
                        content.append(Paragraph(f"Timestamp: {v_timestamp}", normal_style))
                        content.append(Spacer(1, 0.1 * inch))
                else:
                    content.append(Paragraph("No violations detected", normal_style))
            else:
                content.append(Paragraph(str(violation_data), normal_style))

            content.append(Spacer(1, 0.25 * inch))
        except (json.JSONDecodeError, TypeError) as e:
            content.append(Paragraph(f"Error parsing violation data: {str(e)}", normal_style))
            content.append(Spacer(1, 0.25 * inch))

    # Build the PDF
    doc.build(content)
    buffer.seek(0)
    return buffer


def generate_job_description_pdf(job_data):
    """
    Generate a PDF for a job description.

    Args:
        job_data: Dictionary containing job description data with at least:
                 - job_title: The title of the job
                 - company_name: The name of the company
                 - job_description: The full job description text or structured data

    Returns:
        BytesIO object containing the PDF
    """
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(
        buffer,
        pagesize=letter,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=72,
        title=f"Job Description - {job_data.get('job_title', 'Position')}",
    )

    # Styles
    styles = getSampleStyleSheet()

    # Create custom styles
    title_style = styles["Title"]
    title_style.fontSize = 20
    title_style.spaceAfter = 12

    heading1_style = styles["Heading1"]
    heading1_style.fontSize = 16
    heading1_style.spaceAfter = 10
    heading1_style.textColor = colors.darkblue

    heading2_style = styles["Heading2"]
    heading2_style.fontSize = 14
    heading2_style.spaceAfter = 8
    heading2_style.textColor = colors.darkblue

    normal_style = styles["Normal"]
    normal_style.fontSize = 11
    normal_style.spaceAfter = 6

    # Build content
    content = []

    # Job Title
    job_title = job_data.get("job_title", "Job Position")
    content.append(Paragraph(job_title, title_style))
    content.append(Spacer(1, 0.1 * inch))

    # Company Name
    company_name = job_data.get("company_name", "Company")
    content.append(Paragraph(f"Company: {company_name}", heading2_style))
    content.append(Spacer(1, 0.2 * inch))

    # Process the job description
    try:
        job_description = job_data.get("job_description", "")

        # Check if job_description is already a dictionary (parsed JSON)
        if isinstance(job_description, dict):
            parsed_jd = job_description
        # If it's a string that looks like JSON, try to parse it
        elif isinstance(job_description, str) and job_description.strip().startswith("{"):
            try:
                parsed_jd = json.loads(job_description)
            except json.JSONDecodeError:
                # Not valid JSON, treat as plain text
                parsed_jd = None
        else:
            parsed_jd = None

        # If we have a parsed dictionary, process it as structured data
        if isinstance(parsed_jd, dict):
            # Job Description Overview
            if "job_description" in parsed_jd and isinstance(parsed_jd["job_description"], str):
                content.append(Paragraph("Overview", heading1_style))
                content.append(Paragraph(parsed_jd["job_description"], normal_style))
                content.append(Spacer(1, 0.2 * inch))

            # Responsibilities
            if "responsibilities" in parsed_jd and isinstance(parsed_jd["responsibilities"], list):
                content.append(Paragraph("Responsibilities", heading1_style))
                for resp in parsed_jd["responsibilities"]:
                    if isinstance(resp, dict) and "description" in resp:
                        content.append(Paragraph(f"• {resp['description']}", normal_style))
                    else:
                        content.append(Paragraph(f"• {resp}", normal_style))
                content.append(Spacer(1, 0.2 * inch))

            # Requirements
            if "requirements" in parsed_jd and isinstance(parsed_jd["requirements"], list):
                content.append(Paragraph("Requirements", heading1_style))
                for req in parsed_jd["requirements"]:
                    if isinstance(req, dict) and "description" in req:
                        content.append(Paragraph(f"• {req['description']}", normal_style))
                    else:
                        content.append(Paragraph(f"• {req}", normal_style))
                content.append(Spacer(1, 0.2 * inch))

            # Hiring Process
            if "hiring_process" in parsed_jd and isinstance(parsed_jd["hiring_process"], list):
                content.append(Paragraph("Hiring Process", heading1_style))
                for step in parsed_jd["hiring_process"]:
                    if isinstance(step, dict) and "description" in step:
                        content.append(Paragraph(f"• {step['description']}", normal_style))
                    else:
                        content.append(Paragraph(f"• {step}", normal_style))
                content.append(Spacer(1, 0.2 * inch))

            # Additional sections
            for key, value in parsed_jd.items():
                if key not in [
                    "overview",
                    "responsibilities",
                    "requirements",
                    "hiring_process",
                    "job_title",
                    "company_name",
                    "job_description",
                ]:
                    # Convert snake_case to Title Case for display
                    display_key = " ".join(word.capitalize() for word in key.split("_"))
                    content.append(Paragraph(display_key, heading1_style))

                    if isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict) and "description" in item:
                                content.append(Paragraph(f"• {item['description']}", normal_style))
                            else:
                                content.append(Paragraph(f"• {item}", normal_style))
                    else:
                        content.append(Paragraph(str(value), normal_style))

                    content.append(Spacer(1, 0.2 * inch))
        else:
            # If we don't have structured data, display as plain text
            content.append(Paragraph("Job Description", heading1_style))

            # Split by newlines and create paragraphs
            if isinstance(job_description, str):
                paragraphs = job_description.split("\n")
                for para in paragraphs:
                    if para.strip():  # Skip empty lines
                        content.append(Paragraph(para, normal_style))
            else:
                content.append(Paragraph(str(job_description), normal_style))

    except Exception as e:
        # If anything goes wrong, just display the raw job description
        content.append(Paragraph("Job Description", heading1_style))
        content.append(Paragraph(f"Error processing job description: {str(e)}", normal_style))
        content.append(
            Paragraph(
                str(job_data.get("job_description", "No description available")), normal_style
            )
        )

    # Build the PDF
    doc.build(content)
    buffer.seek(0)
    return buffer


def generate_resume_pdf(resume_data):
    """
    Generate a PDF resume based on the selected template.

    Args:
        resume_data: Dictionary containing resume data with template and content

    Returns:
        BytesIO object containing the PDF or None if generation fails
    """
    try:
        buffer = io.BytesIO()

        # Create document with error handling
        doc = SimpleDocTemplate(
            buffer,
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72,
            title=f"Resume - {resume_data.get('title', 'Resume')}",
        )

        # Validate input data
        if not isinstance(resume_data, dict):
            logger.error("Invalid resume_data: must be a dictionary")
            return None

        # Get template type and content
        template = resume_data.get("template", "professional")
        content_data = resume_data.get("content", {})
        # Generate PDF based on template
        try:
            if template == "professional":
                content = _generate_professional_resume(content_data)
            elif template == "modern":
                content = _generate_modern_resume(content_data)
            elif template == "academic":
                content = _generate_academic_resume(content_data)
            else:
                content = _generate_professional_resume(content_data)  # Default fallback

            # Validate content before building
            if not content:
                logger.warning("No content generated for resume")
                content = [Paragraph("Resume content is empty", getSampleStyleSheet()["Normal"])]

            # Build the PDF
            doc.build(content)
            buffer.seek(0)

            logger.info(f"Successfully generated PDF with {len(content)} elements")
            return buffer

        except Exception as content_error:
            logger.error(f"Error generating content: {str(content_error)}")
            return None

    except Exception as e:
        logger.error(f"Error generating PDF: {str(e)}")
        return None


def _generate_professional_resume(content_data):
    """Generate professional template resume content with two-column layout."""
    try:
        styles = getSampleStyleSheet()
        content = []

        # Professional template styling (remains the same)
        title_style = ParagraphStyle(
            name="ResumeTitle",
            parent=styles["Title"],
            fontSize=20,
            spaceAfter=1,
            textColor=colors.black,
            fontName="Helvetica-Bold",
            alignment=1,
        )
        subtitle_style = ParagraphStyle(
            name="ResumeSubtitle",
            parent=styles["Normal"],
            fontSize=12,
            spaceAfter=2,
            textColor=colors.HexColor("#666666"),
            fontName="Helvetica",
            alignment=1,
        )
        contact_style = ParagraphStyle(
            name="ContactStyle",
            parent=styles["Normal"],
            fontSize=9,
            spaceAfter=1,
            fontName="Helvetica",
            textColor=colors.black,
            alignment=1,
        )
        left_heading_style = ParagraphStyle(
            name="LeftHeading",
            parent=styles["Heading2"],
            fontSize=11,
            spaceAfter=3,
            spaceBefore=6,
            textColor=colors.white,
            fontName="Helvetica-Bold",
            borderWidth=0,
            borderPadding=4,
            leftIndent=0,
            rightIndent=0,
            backColor=colors.HexColor("#3498db"),
            alignment=0,
        )
        left_normal_style = ParagraphStyle(
            name="LeftNormal",
            parent=styles["Normal"],
            fontSize=9,
            spaceAfter=2,
            fontName="Helvetica",
            textColor=colors.black,
            leading=11,
        )
        right_heading_style = ParagraphStyle(
            name="RightHeading",
            parent=styles["Heading2"],
            fontSize=11,
            spaceAfter=3,
            spaceBefore=6,
            textColor=colors.white,
            fontName="Helvetica-Bold",
            borderWidth=0,
            borderPadding=4,
            leftIndent=0,
            rightIndent=0,
            backColor=colors.HexColor("#3498db"),
            alignment=0,
        )
        right_normal_style = ParagraphStyle(
            name="RightNormal",
            parent=styles["Normal"],
            fontSize=9,
            spaceAfter=2,
            fontName="Helvetica",
            textColor=colors.black,
            leading=10,
        )

        # Create two-column layout
        left_column_content = []
        right_column_content = []

        # Header Section - Add as header spanning both columns
        header_elements = []
        contact_info = content_data.get("contact_info", {})
        if contact_info and isinstance(contact_info, dict):
            # ... (Header code is unchanged, omitted for brevity) ...
            name = str(contact_info.get("name", "Name Not Provided"))
            header_elements.append(Paragraph(name, title_style))
            job_title = ""
            work_experience = content_data.get("work_experience", [])
            if work_experience and isinstance(work_experience, list) and len(work_experience) > 0:
                recent_job = work_experience[0]
                if isinstance(recent_job, dict):
                    job_title = str(recent_job.get("position", ""))
            if not job_title:
                prof_summary = content_data.get("professional_summary", "")
                if prof_summary and len(prof_summary) < 50:
                    job_title = prof_summary
                else:
                    job_title = "Professional"
            if job_title:
                header_elements.append(Paragraph(job_title, subtitle_style))
            all_contact_items = []
            phone = contact_info.get("phone")
            if phone:
                all_contact_items.append(f"📞 {str(phone)}")
            email = contact_info.get("email")
            if email:
                email_str = str(email)
                email_link = f'<a href="mailto:{email_str}" color="#3498db">{email_str}</a>'
                all_contact_items.append(f"✉ {email_link}")
            location = contact_info.get("location")
            if location:
                all_contact_items.append(f"📍 {str(location)}")
            github = contact_info.get("github")
            if github:
                github_str = str(github)
                github_url = (
                    f"https://{github_str}" if not github_str.startswith("http") else github_str
                )
                github_link = f'<a href="{github_url}" color="#3498db">GitHub</a>'
                all_contact_items.append(f"🔗 {github_link}")
            linkedin_value = contact_info.get("LinkedIn") or contact_info.get("linkedin")
            if linkedin_value:
                linkedin_str = str(linkedin_value)
                linkedin_url = (
                    f"https://{linkedin_str}"
                    if not linkedin_str.startswith("http")
                    else linkedin_str
                )
                linkedin_link = f'<a href="{linkedin_url}" color="#3498db">LinkedIn</a>'
                all_contact_items.append(f"💼 {linkedin_link}")
            website_value = contact_info.get("website")
            if website_value:
                website_str = str(website_value)
                website_url = (
                    f"https://{website_str}" if not website_str.startswith("http") else website_str
                )
                website_link = f'<a href="{website_url}" color="#3498db">Portfolio</a>'
                all_contact_items.append(f"🌐 {website_link}")
            if all_contact_items:
                contact_text = " • ".join(all_contact_items)
                header_elements.append(Paragraph(contact_text, contact_style))
            header_elements.append(Spacer(1, 0.15 * inch))

        # LEFT COLUMN CONTENT

        # Summary
        prof_summary = content_data.get("professional_summary")
        if prof_summary:
            left_column_content.append(Paragraph("Summary", left_heading_style))
            left_column_content.append(Spacer(1, 0.05 * inch))  # ADDED: Spacer for breathing room
            clean_summary = str(prof_summary).replace("**", "").replace("*", "")
            summary_style = ParagraphStyle(
                name="SummaryStyle",
                parent=left_normal_style,
                fontSize=9,
                spaceAfter=2,
                fontName="Helvetica",
                textColor=colors.HexColor("#2c3e50"),
                leading=12,
                leftIndent=3,
                rightIndent=3,
                alignment=0,
            )
            left_column_content.append(Paragraph(clean_summary, summary_style))
            left_column_content.append(Spacer(1, 0.02 * inch))

        # Skills
        skills = content_data.get("skills", [])
        if skills and isinstance(skills, list):
            left_column_content.append(Paragraph("Skills", left_heading_style))
            left_column_content.append(Spacer(1, 0.05 * inch))  # ADDED: Spacer for breathing room
            skill_strings = [str(skill) for skill in skills if skill]
            if skill_strings:
                skills_style = ParagraphStyle(
                    name="SkillsStyle",
                    parent=left_normal_style,
                    fontSize=9,
                    spaceAfter=2,
                    fontName="Helvetica",
                    textColor=colors.HexColor("#34495e"),
                    leading=12,
                    leftIndent=5,
                    bulletIndent=5,
                )
                skills_per_line = 3
                for i in range(0, len(skill_strings), skills_per_line):
                    line_skills = skill_strings[i : i + skills_per_line]
                    skills_line = " • ".join(line_skills)
                    left_column_content.append(Paragraph(f"• {skills_line}", skills_style))
            left_column_content.append(Spacer(1, 0.03 * inch))

        # Education
        education = content_data.get("education")
        if education:
            left_column_content.append(Paragraph("Education", left_heading_style))
            left_column_content.append(Spacer(1, 0.05 * inch))  # ADDED: Spacer for breathing room
            education_list = (
                [education]
                if isinstance(education, dict)
                else (education if isinstance(education, list) else [])
            )
            for edu in education_list:
                if not isinstance(edu, dict):
                    continue
                degree = str(edu.get("degree", "Degree"))
                institution = str(edu.get("institution", "Institution"))
                graduation_date = edu.get("graduation_date", "")
                degree_style = ParagraphStyle(
                    name="DegreeStyle", parent=left_normal_style, fontName="Helvetica-Bold"
                )
                left_column_content.append(Paragraph(degree, degree_style))
                left_column_content.append(Paragraph(institution, left_normal_style))
                if graduation_date:
                    left_column_content.append(Paragraph(str(graduation_date), left_normal_style))
                left_column_content.append(Spacer(1, 0.02 * inch))

        # Soft Skills
        soft_skills = content_data.get("soft_skills", [])
        if soft_skills and isinstance(soft_skills, list):
            left_column_content.append(Paragraph("Soft Skills", left_heading_style))
            left_column_content.append(Spacer(1, 0.05 * inch))  # ADDED: Spacer for breathing room
            soft_skill_strings = [str(skill) for skill in soft_skills if skill]
            if soft_skill_strings:
                soft_skills_text = ", ".join(soft_skill_strings)
                left_column_content.append(Paragraph(soft_skills_text, left_normal_style))
            left_column_content.append(Spacer(1, 0.03 * inch))

        # Languages
        languages = content_data.get("languages", [])
        if languages and isinstance(languages, list):
            left_column_content.append(Paragraph("Languages", left_heading_style))
            left_column_content.append(Spacer(1, 0.05 * inch))  # ADDED: Spacer for breathing room
            language_strings = [str(lang) for lang in languages if lang]
            if language_strings:
                languages_text = ", ".join(language_strings)
                left_column_content.append(Paragraph(languages_text, left_normal_style))
            left_column_content.append(Spacer(1, 0.03 * inch))

        # Certifications
        certifications = content_data.get("certifications", [])
        if certifications and isinstance(certifications, list):
            left_column_content.append(Paragraph("Certifications", left_heading_style))
            left_column_content.append(Spacer(1, 0.05 * inch))  # ADDED: Spacer for breathing room
            for cert in certifications:
                if isinstance(cert, dict):
                    cert_name = str(cert.get("name", "Certification"))
                    cert_text = cert_name
                    issuer = cert.get("issuer")
                    if issuer:
                        cert_text += f" - {str(issuer)}"
                    left_column_content.append(Paragraph(f"• {cert_text}", left_normal_style))
                elif cert:
                    left_column_content.append(Paragraph(f"• {str(cert)}", left_normal_style))

        # RIGHT COLUMN CONTENT

        # Work Experience
        work_experience = content_data.get("work_experience", [])
        if work_experience and isinstance(work_experience, list):
            right_column_content.append(Paragraph("Work Experience", right_heading_style))
            right_column_content.append(
                Spacer(1, 0.05 * inch)
            )  # ADJUSTED: Spacer for breathing room
            work_exp_desc_style = ParagraphStyle(
                name="WorkExpDesc",
                parent=styles["Normal"],
                fontSize=9,
                textColor=colors.HexColor("#2c3e50"),
                leading=12,
                leftIndent=6,
                rightIndent=3,
                spaceBefore=1,
                spaceAfter=1,
            )
            for exp in work_experience:
                if not isinstance(exp, dict):
                    continue
                company = str(exp.get("company", "Company"))
                position = str(exp.get("position", "Position"))
                start_date = exp.get("start_date", "")
                end_date = exp.get("end_date", "Present")
                location = exp.get("location", "")
                date_range = f"{str(start_date)} - {str(end_date)}" if start_date else ""
                company_date_data = [[company, date_range]]
                company_date_data.append([position, location if location else ""])
                company_table = Table(company_date_data, colWidths=[3 * inch, 1.5 * inch])
                company_table.setStyle(
                    TableStyle(
                        [
                            ("ALIGN", (0, 0), (0, -1), "LEFT"),
                            ("ALIGN", (1, 0), (1, -1), "RIGHT"),
                            ("FONTNAME", (0, 0), (0, 0), "Helvetica-Bold"),
                            ("FONTNAME", (1, 0), (1, 0), "Helvetica-Oblique"),
                            ("FONTNAME", (0, 1), (0, 1), "Helvetica-Oblique"),
                            ("FONTSIZE", (0, 0), (-1, -1), 10),
                            ("TEXTCOLOR", (0, 0), (0, 0), colors.HexColor("#2c3e50")),
                            ("TEXTCOLOR", (1, 0), (1, 0), colors.HexColor("#34495e")),
                            ("TEXTCOLOR", (0, 1), (-1, 1), colors.HexColor("#34495e")),
                            ("VALIGN", (0, 0), (-1, -1), "TOP"),
                            ("LEFTPADDING", (0, 0), (-1, -1), 3),
                            ("RIGHTPADDING", (0, 0), (-1, -1), 3),
                            ("TOPPADDING", (0, 0), (-1, -1), 1),
                            ("BOTTOMPADDING", (0, 0), (-1, -1), 1),
                        ]
                    )
                )
                right_column_content.append(company_table)
                description = exp.get("description", "")
                if description:
                    right_column_content.append(Spacer(1, 0.02 * inch))
                    desc_lines = (
                        description.replace("•", "").split("\n")
                        if "•" in description or "\n" in description
                        else [description]
                    )
                    for line in desc_lines:
                        line = line.strip()
                        if line:
                            right_column_content.append(Paragraph(f"• {line}", work_exp_desc_style))
                right_column_content.append(Spacer(1, 0.03 * inch))

        # Projects
        projects = content_data.get("projects", [])
        if projects and isinstance(projects, list):
            right_column_content.append(Paragraph("Projects", right_heading_style))
            right_column_content.append(
                Spacer(1, 0.05 * inch)
            )  # ADJUSTED: Spacer for breathing room
            project_desc_style = ParagraphStyle(
                name="ProjectDesc",
                parent=styles["Normal"],
                fontSize=9,
                textColor=colors.HexColor("#2c3e50"),
                leading=12,
                leftIndent=6,
                rightIndent=3,
                spaceBefore=1,
                spaceAfter=1,
            )
            project_tech_style = ParagraphStyle(
                name="ProjectTech",
                parent=styles["Normal"],
                fontSize=9,
                fontName="Helvetica-Oblique",
                textColor=colors.HexColor("#34495e"),
                leading=11,
                leftIndent=6,
                rightIndent=3,
                spaceBefore=1,
                spaceAfter=1,
            )
            for project in projects:
                if not isinstance(project, dict):
                    continue
                name = str(project.get("name", "Project"))
                description = project.get("description", "")
                technologies = project.get("technologies", [])
                year = project.get("year", "")
                project_header_data = [[name, str(year) if year else ""]]
                project_table = Table(project_header_data, colWidths=[3 * inch, 1.5 * inch])
                project_table.setStyle(
                    TableStyle(
                        [
                            ("ALIGN", (0, 0), (0, 0), "LEFT"),
                            ("ALIGN", (1, 0), (1, 0), "RIGHT"),
                            ("FONTNAME", (0, 0), (0, 0), "Helvetica-Bold"),
                            ("FONTNAME", (1, 0), (1, 0), "Helvetica-Oblique"),
                            ("FONTSIZE", (0, 0), (-1, -1), 10),
                            ("TEXTCOLOR", (0, 0), (0, 0), colors.HexColor("#2c3e50")),
                            ("TEXTCOLOR", (1, 0), (1, 0), colors.HexColor("#34495e")),
                            ("VALIGN", (0, 0), (-1, -1), "TOP"),
                            ("LEFTPADDING", (0, 0), (-1, -1), 3),
                            ("RIGHTPADDING", (0, 0), (-1, -1), 3),
                            ("TOPPADDING", (0, 0), (-1, -1), 1),
                            ("BOTTOMPADDING", (0, 0), (-1, -1), 1),
                        ]
                    )
                )
                right_column_content.append(project_table)
                if description:
                    right_column_content.append(Spacer(1, 0.02 * inch))
                    right_column_content.append(Paragraph(f"• {description}", project_desc_style))
                if technologies and isinstance(technologies, list):
                    tech_strings = [str(tech) for tech in technologies if tech]
                    if tech_strings:
                        for i in range(0, len(tech_strings), 3):
                            tech_line = " • ".join(tech_strings[i : i + 3])
                            right_column_content.append(
                                Paragraph(f"• {tech_line}", project_tech_style)
                            )
                right_column_content.append(Spacer(1, 0.03 * inch))

        # Add header elements first
        if "header_elements" in locals() and header_elements:
            content.extend(header_elements)

        # Create the two-column table
        if not left_column_content:
            left_column_content = [Paragraph("", left_normal_style)]
        if not right_column_content:
            right_column_content = [Paragraph("", right_normal_style)]
        table_data = [[left_column_content, right_column_content]]
        main_table = Table(table_data, colWidths=[2.5 * inch, 4.5 * inch])
        main_table.setStyle(
            TableStyle(
                [
                    ("VALIGN", (0, 0), (-1, -1), "TOP"),
                    ("LEFTPADDING", (0, 0), (0, 0), 0),
                    ("RIGHTPADDING", (1, 0), (1, 0), 0),
                    ("TOPPADDING", (0, 0), (-1, -1), 0),
                    ("BOTTOMPADDING", (0, 0), (-1, -1), 0),
                ]
            )
        )
        content.append(main_table)
        return content

    except Exception as e:
        logger.error(f"Error in _generate_professional_resume: {str(e)}")
        styles = getSampleStyleSheet()
        return [Paragraph("Error generating resume content", styles["Normal"])]


def _generate_modern_resume(content_data):
    """Generate modern template resume content with visual appeal."""
    try:
        styles = getSampleStyleSheet()
        content = []

        # Modern template styling
        title_style = ParagraphStyle(
            name="ModernTitle",
            parent=styles["Title"],
            fontSize=26,
            spaceAfter=12,
            textColor=colors.HexColor("#2E86AB"),
            fontName="Helvetica-Bold",
        )

        heading_style = ParagraphStyle(
            name="ModernHeading",
            parent=styles["Heading2"],
            fontSize=16,
            spaceAfter=8,
            spaceBefore=16,
            textColor=colors.HexColor("#2E86AB"),
            fontName="Helvetica-Bold",
        )

        normal_style = ParagraphStyle(
            name="ModernNormal",
            parent=styles["Normal"],
            fontSize=11,
            spaceAfter=6,
            fontName="Helvetica",
        )

        # Contact Information
        contact_info = content_data.get("contact_info", {})
        if contact_info and isinstance(contact_info, dict):
            name = str(contact_info.get("name", "Name Not Provided"))
            content.append(Paragraph(name, title_style))

            # Create contact info as simple paragraphs instead of table
            contact_items = []
            if contact_info.get("email"):
                contact_items.append(f"Email: {str(contact_info['email'])}")
            if contact_info.get("phone"):
                contact_items.append(f"Phone: {str(contact_info['phone'])}")
            if contact_info.get("location"):
                contact_items.append(f"Location: {str(contact_info['location'])}")
            if contact_info.get("linkedin"):
                contact_items.append(f"LinkedIn: {str(contact_info['linkedin'])}")
            if contact_info.get("website"):
                contact_items.append(f"Website: {str(contact_info['website'])}")

            for item in contact_items:
                content.append(Paragraph(item, normal_style))

            content.append(Spacer(1, 0.2 * inch))

        # Add similar safe handling for other sections...
        # (Following the same pattern as professional template)

        return content

    except Exception as e:
        logger.error(f"Error in _generate_modern_resume: {str(e)}")
        styles = getSampleStyleSheet()
        return [Paragraph("Error generating modern resume content", styles["Normal"])]


def _generate_academic_resume(content_data):
    """
    Generate a beautiful and elegant academic-style resume (CV).

    This template uses a classic serif typeface, a refined color palette, and
    meticulous spacing to create a professional and highly readable document
    perfect for academic and research applications.
    """
    try:
        styles = getSampleStyleSheet()
        content = []

        # --- Define a Beautiful & Professional Style Palette ---

        # Usable width of the document
        doc_width = letter[0] - 2 * 72

        # Colors
        text_color = colors.HexColor("#2C3E50")  # A dark, soft charcoal
        accent_color_hex = "#005A9C"  # The hex string for links
        accent_color_obj = colors.HexColor(accent_color_hex)  # The object for styles
        subtle_color = colors.dimgrey

        # Styles
        name_style = ParagraphStyle(
            name="NameStyle",
            fontName="Times-Bold",
            fontSize=22,
            leading=28,
            alignment=1,
            spaceAfter=2,
            textColor=text_color,
        )

        objective_style = ParagraphStyle(
            name="ObjectiveStyle",
            fontName="Times-Italic",
            fontSize=11,
            leading=14,
            alignment=1,
            spaceAfter=12,
            textColor=subtle_color,
        )

        contact_icon_style = ParagraphStyle(
            name="ContactIcon",
            fontName="Helvetica",  # Icons look better in sans-serif
            fontSize=10,
            textColor=accent_color_obj,  # Use the Color object here
            alignment=2,  # Right align icons
        )

        contact_text_style = ParagraphStyle(
            name="ContactText",
            fontName="Times-Roman",
            fontSize=10,
            leading=12,
            textColor=text_color,
        )

        section_heading_style = ParagraphStyle(
            name="SectionHeading",
            fontName="Times-Bold",
            fontSize=11,
            textColor=text_color,
            leading=14,
            spaceBefore=12,
            spaceAfter=3,
        )

        entry_heading_style = ParagraphStyle(
            name="EntryHeading",
            fontName="Times-Bold",
            fontSize=11,
            textColor=text_color,
        )

        entry_subheading_style = ParagraphStyle(
            name="EntrySubHeading",
            fontName="Times-Italic",
            fontSize=10,
            textColor=text_color,
        )

        entry_date_style = ParagraphStyle(
            name="EntryDateStyle",
            fontName="Times-Roman",
            fontSize=10,
            alignment=2,
            textColor=subtle_color,
        )

        bullet_style = ParagraphStyle(
            name="BulletStyle",
            parent=styles["Normal"],
            fontName="Times-Roman",
            fontSize=10.5,
            leading=14,
            spaceAfter=4,
            leftIndent=15,
            bulletIndent=5,
            textColor=text_color,
        )

        # --- Helper Flowable for the Horizontal Line ---
        class HorizontalLine(Flowable):
            def __init__(self, width, height=0.3, color=colors.lightgrey):
                super().__init__()
                self.width = width
                self.height = height
                self.color = color

            def draw(self):
                self.canv.setStrokeColor(self.color)
                self.canv.setLineWidth(self.height)
                self.canv.line(0, 0, self.width, 0)

        # --- Helper Functions to Process Each Section ---
        def process_education(edu_item):
            elements = []
            institution = edu_item.get("institution", "Institution")
            grad_date = edu_item.get("graduation_date", "")

            degree = edu_item.get("degree", "Degree")
            field = edu_item.get("field_of_study")
            degree_line = f"{degree} in {field}" if field else degree

            gpa = edu_item.get("gpa")
            gpa_line = f"GPA: {gpa}" if gpa else ""

            header_data = [
                [
                    Paragraph(institution, entry_heading_style),
                    Paragraph(grad_date, entry_date_style),
                ],
                [
                    Paragraph(degree_line, entry_subheading_style),
                    Paragraph(gpa_line, entry_date_style),
                ],
            ]
            table = Table(header_data, colWidths=[doc_width * 0.7, doc_width * 0.3])
            table.setStyle(
                TableStyle(
                    [("VALIGN", (0, 0), (-1, -1), "TOP"), ("BOTTOMPADDING", (0, 0), (-1, -1), 8)]
                )
            )
            elements.append(table)
            return [KeepTogether(elements)]

        def process_experience(exp_item, is_research=False):
            elements = []
            title = (
                exp_item.get("company")
                if not is_research
                else exp_item.get("institution", "Institution")
            )
            position = exp_item.get("position", "Position")

            start = exp_item.get("start_date", "")
            end = exp_item.get("end_date", "Present")
            date_range = f"{start} – {end}" if start else ""  # Use en-dash for ranges

            header_data = [
                [Paragraph(title, entry_heading_style), Paragraph(date_range, entry_date_style)],
                [Paragraph(position, entry_subheading_style), ""],
            ]
            table = Table(header_data, colWidths=[doc_width * 0.7, doc_width * 0.3])
            table.setStyle(
                TableStyle(
                    [("VALIGN", (0, 0), (-1, -1), "TOP"), ("BOTTOMPADDING", (0, 0), (-1, -1), 4)]
                )
            )
            elements.append(table)

            description = exp_item.get("description", "")
            if description:
                desc_lines = str(description).strip().split("\n")
                for line in desc_lines:
                    line = line.strip().lstrip("*-• ")
                    if line:
                        elements.append(Paragraph(f"• {line}", bullet_style))

            elements.append(Spacer(1, 0.12 * inch))
            return [KeepTogether(elements)]

        # --- Main Section Builder Function ---
        def build_section(title, items, processor_func, **kwargs):
            if items and isinstance(items, list):
                content.append(Paragraph(title.upper(), section_heading_style))
                content.append(HorizontalLine(doc_width))
                content.append(Spacer(1, 0.1 * inch))
                for item in items:
                    content.extend(processor_func(item, **kwargs))

        # --- Build the Resume Content ---

        # 1. Header Section
        header_elements = []
        contact_info = content_data.get("contact_info", {})
        if contact_info:
            header_elements.append(
                Paragraph(contact_info.get("name", "Name Not Provided"), name_style)
            )
            objective_text = content_data.get("objective") or content_data.get(
                "professional_summary"
            )
            if objective_text:
                header_elements.append(Paragraph(objective_text, objective_style))

            # Create a beautiful, icon-driven contact table
            contact_data = []
            if contact_info.get("email"):
                email = contact_info["email"]
                contact_data.append(
                    [
                        Paragraph("📧", contact_icon_style),
                        Paragraph(
                            f'<a href="mailto:{email}" color="{accent_color_hex}">{email}</a>',
                            contact_text_style,
                        ),
                    ]
                )
            if contact_info.get("phone"):
                contact_data.append(
                    [
                        Paragraph("📞", contact_icon_style),
                        Paragraph(contact_info["phone"], contact_text_style),
                    ]
                )
            if contact_info.get("location"):
                contact_data.append(
                    [
                        Paragraph("📍", contact_icon_style),
                        Paragraph(contact_info["location"], contact_text_style),
                    ]
                )
            if contact_info.get("linkedin"):
                linkedin_url = contact_info["linkedin"]
                contact_data.append(
                    [
                        Paragraph("💼", contact_icon_style),
                        Paragraph(
                            f'<a href="{linkedin_url}" color="{accent_color_hex}">LinkedIn Profile</a>',
                            contact_text_style,
                        ),
                    ]
                )
            if contact_info.get("website"):
                website_url = contact_info["website"]
                contact_data.append(
                    [
                        Paragraph("🌐", contact_icon_style),
                        Paragraph(
                            f'<a href="{website_url}" color="{accent_color_hex}">Personal Website</a>',
                            contact_text_style,
                        ),
                    ]
                )

            if contact_data:
                num_items = len(contact_data)
                cols = (num_items + 1) // 2 if num_items > 3 else num_items
                rows = 2 if num_items > 3 else 1
                table_data = [[] for _ in range(rows)]
                for i, item in enumerate(contact_data):
                    table_data[i % rows].extend(item)

                col_width = doc_width / (cols * 2)
                contact_table = Table(
                    table_data, colWidths=[col_width * 0.2, col_width * 1.8] * cols
                )
                contact_table.setStyle(TableStyle([("VALIGN", (0, 0), (-1, -1), "MIDDLE")]))
                header_elements.append(contact_table)

        content.append(KeepTogether(header_elements))
        content.append(Spacer(1, 0.1 * inch))

        # 2. Build Content Sections in Logical Order
        build_section("Education", content_data.get("education"), process_education)
        build_section(
            "Research Experience",
            content_data.get("research_experience"),
            process_experience,
            is_research=True,
        )
        build_section(
            "Publications",
            content_data.get("publications"),
            lambda p: [Paragraph(f"• {p.strip()}", bullet_style)],
        )
        build_section(
            "Professional Experience", content_data.get("work_experience"), process_experience
        )

        # 3. Skills Section (as a comma-separated list)
        skills = content_data.get("skills")
        if skills and isinstance(skills, list):
            content.append(Paragraph("SKILLS", section_heading_style))
            content.append(HorizontalLine(doc_width))
            content.append(Spacer(1, 0.1 * inch))
            skills_text = ", ".join(str(s).strip() for s in skills)
            content.append(Paragraph(skills_text, bullet_style))

        build_section(
            "Certifications",
            content_data.get("certifications"),
            lambda c: [Paragraph(f"• {c.strip()}", bullet_style)],
        )

        return content

    except Exception as e:
        logger.error(f"Error in _generate_academic_resume: {str(e)}", exc_info=True)
        styles = getSampleStyleSheet()
        return [
            Paragraph(f"Error generating beautiful academic resume. Error: {e}", styles["Normal"])
        ]
