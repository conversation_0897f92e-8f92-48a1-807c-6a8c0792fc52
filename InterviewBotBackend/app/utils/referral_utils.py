"""
Referral System Utilities

This module provides utility functions for generating and validating referral codes.
Generates cryptographically secure, unpredictable referral codes suitable for millions of users.
"""

import hashlib
import secrets
import time
from typing import Set
from uuid import uuid4


def generate_referral_code(entity_id: str, entity_name: str) -> str:
    """
    Generate a cryptographically secure, unpredictable referral code.

    Uses multiple entropy sources for maximum security and uniqueness:
    - Cryptographically secure random bytes (128 bits)
    - High-resolution timestamp
    - UUID4 entropy
    - Entity-specific data (ID and name)

    Args:
        entity_id: Unique ID of the entity (user or organization)
        entity_name: Name of the entity

    Returns:
        A 12-character cryptographically secure referral code
    """
    # Character set excluding visually similar characters
    # Excludes: 0, O, I, 1, L, 5, S, 2, Z to avoid confusion
    chars = "***************************"

    # Gather entropy from multiple sources
    entropy_parts = [
        secrets.token_bytes(16),  # 128 bits of cryptographic randomness
        str(time.time_ns()).encode(),  # High-resolution timestamp
        str(uuid4()).encode(),  # UUID4 entropy
        entity_id.encode(),  # Entity ID
        entity_name.encode(),  # Entity name
    ]

    # Combine all entropy sources
    combined_entropy = b"".join(entropy_parts)

    # Create a cryptographic hash of the combined entropy
    hash_digest = hashlib.sha256(combined_entropy).digest()

    # Convert hash to integer for indexing
    hash_int = int.from_bytes(hash_digest, byteorder='big')

    # Generate 12-character code using hash for character selection
    code_chars = []
    for i in range(12):
        # Use different parts of the hash for each character
        char_index = (hash_int >> (i * 5)) % len(chars)
        code_chars.append(chars[char_index])

    # Add additional randomness using secrets module for extra security
    for i in range(6):  # Randomize half the characters
        random_pos = secrets.randbelow(12)
        random_char = secrets.choice(chars)
        code_chars[random_pos] = random_char

    return "".join(code_chars)


def validate_referral_code_format(code: str) -> bool:
    """
    Validate the format of a referral code.

    Args:
        code: The referral code to validate

    Returns:
        True if the format is valid, False otherwise
    """
    if not code or not isinstance(code, str):
        return False

    # Remove whitespace and convert to uppercase
    code = code.strip().upper()

    # Check length (must be exactly 12 characters)
    if len(code) != 12:
        return False

    # Valid characters: excludes visually similar ones
    # Allowed: 3,4,6,7,8,9,A,B,C,D,E,F,G,H,J,K,M,N,P,Q,R,T,U,V,W,X,Y
    valid_chars = "***************************"

    # Check if all characters are valid
    for char in code:
        if char not in valid_chars:
            return False
    return True


def normalize_referral_code(code: str) -> str:
    """
    Normalize a referral code by removing whitespace and converting to uppercase.

    Args:
        code: The referral code to normalize

    Returns:
        Normalized referral code or None if invalid
    """
    if not code or not isinstance(code, str):
        return None

    normalized = code.strip().upper()

    if validate_referral_code_format(normalized):
        return normalized

    return None


def generate_unique_referral_code(
    entity_id: str,
    entity_name: str,
    existing_codes: Set[str],
    max_attempts: int = 50
) -> str:
    """
    Generate a unique referral code that doesn't conflict with existing codes.

    Args:
        entity_id: Unique ID of the entity (user or organization)
        entity_name: Name of the entity
        existing_codes: Set of existing referral codes to avoid conflicts
        max_attempts: Maximum number of attempts to generate a unique code

    Returns:
        A unique 12-character referral code

    Raises:
        ValueError: If unable to generate a unique code after max_attempts
    """
    for attempt in range(max_attempts):
        # Generate code with attempt number for uniqueness
        code = generate_referral_code(f"{entity_id}_{attempt}", entity_name)

        # Normalize and check availability
        normalized_code = normalize_referral_code(code)
        if normalized_code and normalized_code not in existing_codes:
            return normalized_code

    raise ValueError(
        f"Unable to generate a unique referral code after {max_attempts} attempts. "
        f"This is extremely unlikely with cryptographic generation."
    )
