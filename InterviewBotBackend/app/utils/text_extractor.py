import io
import pdf2image
import pytesseract


def extract_text_from_pdf(file_content: bytes) -> str:
    """
    Extract text from a PDF file using OCR with pytesseract.

    Args:
        file_content: Raw PDF file content as bytes

    Returns:
        Extracted text as string
    """
    try:
        # Convert PDF to images
        images = pdf2image.convert_from_bytes(file_content)

        # Extract text from each image using OCR
        extracted_text = ""
        for image in images:
            page_text = pytesseract.image_to_string(image)
            extracted_text += page_text + "\n\n"

        return extracted_text.strip()

    except Exception as e:
        raise e
        # return f"Error extracting text: {str(e)}"
    
