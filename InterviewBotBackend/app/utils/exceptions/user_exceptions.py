class UserServiceException(Exception):
    """Base exception class for UserService"""

    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class EmailAlreadyRegisteredException(UserServiceException):
    """Exception raised when email is already registered"""

    def __init__(self, email: str):
        super().__init__(
            message=f"Email {email} is already registered", status_code=409  # Conflict
        )

class UnAuthorizedUserException(UserServiceException):
    """Exception raised when email is already registered"""

    def __init__(self, detail: str):
        super().__init__(
            message=f"{detail}", status_code=401  # Conflict
        )


# Removed DatabaseOperationException - now using the common database exceptions from database_exceptions.py


class ProfileUpdateException(UserServiceException):
    """Exception raised when profile update fails"""

    def __init__(self, detail: str = "Failed to update user profile"):
        super().__init__(
            message=detail,
            status_code=400,  # Bad Request
        )


class GoogleOAuthException(UserServiceException):
    """Raised when Google OAuth authentication fails"""

    def __init__(self):
        super().__init__(
            message="Google OAuth authentication failed",
            status_code=401,  # Unauthorized
        )


class InvalidRegistrationTypeException(UserServiceException):
    """Exception raised when an invalid registration type is provided"""

    def __init__(self, detail: str):
        super().__init__(message=f"Invalid registration type : {detail}", status_code=400)  # Bad Request


class OrganizationNotFoundException(UserServiceException):
    """Raised when the organization is not found in the DB."""

    def __init__(self):
        super().__init__(message="Organization not found", status_code=404)


class MissingUpdateFieldsException(UserServiceException):
    """Raised when neither skill_sets nor job_roles are provided."""

    def __init__(self):
        super().__init__(
            message="Either 'skill_sets' or 'job_roles' must be provided", status_code=422
        )



class InvalidS3UrlException(UserServiceException):
    """Exception raised when S3 URL format is invalid"""

    def __init__(self, detail: str = "Invalid S3 URL format"):
        super().__init__(
            message=detail,
            status_code=400,  # Bad Request
        )


class S3ObjectNotAccessibleException(UserServiceException):
    """Exception raised when S3 object is not found or accessible"""

    def __init__(self, detail: str = "S3 object not found or accessible"):
        super().__init__(
            message=detail,
            status_code=404,  # Not Found
        )



class MissingParametersException(UserServiceException):
    """Raised when registration type is not provided."""

    def __init__(self):
        super().__init__(message="registration type must be provided", status_code=422)


class RegisteredWithDifferentTypeException(UserServiceException):
    """Raised when neither user is registered with different registration type."""

    def __init__(self):
        super().__init__(
            message="Use correct registration type", status_code=422
        )

