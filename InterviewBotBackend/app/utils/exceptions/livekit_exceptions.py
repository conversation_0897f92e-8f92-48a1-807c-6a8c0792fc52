"""
LiveKit exception classes for consistent error handling.
"""

class LiveKitServiceException(Exception):
    """Base exception class for LiveKitService"""

    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class LiveKitInitializationException(LiveKitServiceException):
    """Raised when LiveKit API initialization fails."""

    def __init__(self, detail: str = "Failed to initialize LiveKit API"):
        super().__init__(message=detail, status_code=500)


class InterviewNotFoundException(LiveKitServiceException):
    """Raised when an interview cannot be found."""

    def __init__(self, detail: str = "Interview not found"):
        super().__init__(message=detail, status_code=404)


class UnauthorizedAccessException(LiveKitServiceException):
    """Raised when a user is not authorized to access a resource."""

    def __init__(self, detail: str = "You are not authorized to join this interview"):
        super().__init__(message=detail, status_code=403)


class CompletedInterviewException(LiveKitServiceException):
    """Raised when attempting to access a completed interview."""

    def __init__(self, detail: str = "Interview has already been completed"):
        super().__init__(message=detail, status_code=403)


class InterviewTimingException(LiveKitServiceException):
    """Raised when attempting to access an interview outside its availability window."""

    def __init__(self, detail: str = "Interview is not currently available"):
        super().__init__(message=detail, status_code=400)


class MissingQuestionsException(LiveKitServiceException):
    """Raised when interview questions are missing."""

    def __init__(self, detail: str = "Interview questions are not present"):
        super().__init__(message=detail, status_code=400)


class MetadataSizeExceededException(LiveKitServiceException):
    """Raised when metadata exceeds size limit."""

    def __init__(self, detail: str = "Metadata exceeds 64 KiB limit"):
        super().__init__(message=detail, status_code=400)


class RoomCreationException(LiveKitServiceException):
    """Raised when room creation fails."""

    def __init__(self, detail: str = "Room creation failed"):
        super().__init__(message=detail, status_code=500)


class RoomNotFoundException(LiveKitServiceException):
    """Raised when a room cannot be found."""

    def __init__(self, room_name: str):
        super().__init__(message=f"Room '{room_name}' not found", status_code=404)


class TokenGenerationException(LiveKitServiceException):
    """Raised when token generation fails."""

    def __init__(self, detail: str = "Failed to generate token"):
        super().__init__(message=detail, status_code=500)
