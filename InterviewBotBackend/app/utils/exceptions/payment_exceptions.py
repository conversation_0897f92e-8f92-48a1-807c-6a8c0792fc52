"""
Payment Exception Classes

This module defines custom exception classes for payment-related errors
in the Razorpay integration system.
"""


class PaymentException(Exception):
    """Base exception class for payment-related errors."""
    
    def __init__(self, message: str = "An unknown payment error occurred.", error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class OrderCreationException(PaymentException):
    """Exception raised when order creation fails."""
    def __init__(self, message: str = "Failed to create order.", error_code: str = None):
        super().__init__(message, error_code)


class PaymentVerificationException(PaymentException):
    """Exception raised when payment verification fails."""
    def __init__(self, message: str = "Payment verification failed.", error_code: str = None):
        super().__init__(message, error_code)


class WebhookProcessingException(PaymentException):
    """Exception raised when webhook processing fails."""
    def __init__(self, message: str = "Failed to process webhook.", error_code: str = None):
        super().__init__(message, error_code)


class InvalidSignatureException(PaymentException):
    """Exception raised when signature verification fails."""
    def __init__(self, message: str = "Invalid signature.", error_code: str = None):
        super().__init__(message, error_code)


class SubscriptionPlanNotFoundException(PaymentException):
    """Exception raised when subscription plan is not found."""
    def __init__(self, message: str = "Subscription plan not found.", error_code: str = None):
        super().__init__(message, error_code)


class OrderNotFoundException(PaymentException):
    """Exception raised when order is not found."""
    def __init__(self, message: str = "Order not found.", error_code: str = None):
        super().__init__(message, error_code)


class TransactionNotFoundException(PaymentException):
    """Exception raised when transaction is not found."""
    def __init__(self, message: str = "Transaction not found.", error_code: str = None):
        super().__init__(message, error_code)


class RazorpayAPIException(PaymentException):
    """Exception raised when Razorpay API calls fail."""
    def __init__(self, message: str = "Razorpay API error.", error_code: str = None):
        super().__init__(message, error_code)


class PaymentGatewayException(PaymentException):
    """Exception raised for general payment gateway errors."""
    def __init__(self, message: str = "Payment gateway error.", error_code: str = None):
        super().__init__(message, error_code)


class DuplicateWebhookException(PaymentException):
    """Exception raised when duplicate webhook events are received."""
    def __init__(self, message: str = "Duplicate webhook event received.", error_code: str = None):
        super().__init__(message, error_code)


class InsufficientPermissionException(PaymentException):
    """Exception raised when user lacks permission for payment operations."""
    def __init__(self, message: str = "Insufficient permissions for payment operation.", error_code: str = None):
        super().__init__(message, error_code)
