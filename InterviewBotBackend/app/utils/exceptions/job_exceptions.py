class JobServiceException(Exception):
    """Base exception class for Job-related services"""

    def __init__(self, message: str = "Job service error occurred"):
        self.message = message
        super().__init__(self.message)


class JobPostingNotFoundException(JobServiceException):
    """Raised when a job posting cannot be found."""

    def __init__(self, job_id: str):
        self.message = f"Job posting with ID {job_id} not found"
        super().__init__(self.message)


class JobPostingCreationException(JobServiceException):
    """Raised when job posting creation fails."""

    def __init__(self, message: str = "Failed to create job posting"):
        self.message = message
        super().__init__(self.message)


class JobPostingUpdateException(JobServiceException):
    """Raised when job posting update fails."""

    def __init__(self, message: str = "Failed to update job posting"):
        self.message = message
        super().__init__(self.message)


class UnauthorizedJobAccessException(JobServiceException):
    """Raised when a user tries to access a job posting they don't own."""

    def __init__(self, message: str = "Unauthorized access to job posting"):
        self.message = message
        super().__init__(self.message)


class JobApplicationNotFoundException(JobServiceException):
    """Raised when a job application cannot be found."""

    def __init__(self, application_id: str):
        self.message = f"Job application with ID {application_id} not found"
        super().__init__(self.message)


class JobApplicationCreationException(JobServiceException):
    """Raised when job application creation fails."""

    def __init__(self, message: str = "Failed to create job application"):
        self.message = message
        super().__init__(self.message)


class JobApplicationUpdateException(JobServiceException):
    """Raised when job application update fails."""

    def __init__(self, message: str = "Failed to update job application"):
        self.message = message
        super().__init__(self.message)


class DuplicateJobApplicationException(JobServiceException):
    """Raised when a user tries to apply to the same job twice."""

    def __init__(self, message: str = "User has already applied to this job"):
        self.message = message
        super().__init__(self.message)


class ClosedJobApplicationException(JobServiceException):
    """Raised when trying to apply to a closed job posting."""

    def __init__(self, message: str = "Cannot apply to a closed job posting"):
        self.message = message
        super().__init__(self.message)


class UnauthorizedApplicationAccessException(JobServiceException):
    """Raised when a user tries to access an application they don't own."""

    def __init__(self, message: str = "Unauthorized access to job application"):
        self.message = message
        super().__init__(self.message)


class InvalidScreeningAnswersException(JobServiceException):
    """Raised when screening answers are invalid or incomplete."""

    def __init__(self, message: str = "Invalid or incomplete screening answers"):
        self.message = message
        super().__init__(self.message)


class JobPostingClosedException(JobServiceException):
    """Raised when trying to perform operations on a closed job posting."""

    def __init__(self, message: str = "Job posting is closed"):
        self.message = message
        super().__init__(self.message)


class InterviewSchedulingFromApplicationException(JobServiceException):
    """Raised when interview scheduling from application fails."""

    def __init__(self, message: str = "Failed to schedule interview from application"):
        self.message = message
        super().__init__(self.message)