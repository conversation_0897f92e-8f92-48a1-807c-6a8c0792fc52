class MCPServiceException(Exception):
    """Base exception class for MCPService"""

    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class MCPToolExecutionException(MCPServiceException):
    """Raised when tool execution via MCP fails."""

    def __init__(self, detail: str = "Tool execution failed via MCP"):
        super().__init__(message=detail, status_code=500)


class MCPInvalidToolResponse(MCPServiceException):
    """Raised when MCP tool returns invalid or empty response."""

    def __init__(self, detail: str = "MCP tool returned invalid or empty content"):
        super().__init__(message=detail, status_code=500)


class MCPToolResponseParsingError(MCPServiceException):
    """Raised when MCP tool response cannot be parsed."""

    def __init__(self, detail: str = "Failed to parse MCP tool response"):
        super().__init__(message=detail, status_code=500)


class MCPToolListFailed(MCPServiceException):
    """Raised when MCP tool list fails."""

    def __init__(self, detail: str = "Failed to list MCP tools"):
        super().__init__(message=detail, status_code=500)


class MCPToolInterviewNotFound(MCPServiceException):
    """Raised when interview is not found during MCP tool call."""

    def __init__(self, detail: str = "Interview not found"):
        super().__init__(message=detail, status_code=404)


class MCPToolNotFound(MCPServiceException):
    """Raised when mcp tool is not found."""

    def __init__(self, detail: str = "MCP tool not not found"):
        super().__init__(message=detail, status_code=404)


class MCPMissingRequiredField(MCPServiceException):
    """Raised when a required field is missing for an MCP tool execution."""

    def __init__(self, detail: str = "Required field missing for tool execution"):
        super().__init__(message=detail, status_code=400)


class MCPGenericException(MCPServiceException):
    """Raised when there's a generic error during MCP tool execution."""

    def __init__(self, tool_name: str, detail: str = "Tool execution failed"):
        super().__init__(message=f"Tool {tool_name} call failed: {detail}", status_code=500)
