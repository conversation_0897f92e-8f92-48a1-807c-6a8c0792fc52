"""
Authentication exception classes for consistent error handling.
"""

class AuthException(Exception):
    """Base exception class for authentication operations"""

    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class MissingBearerTokenException(AuthException):
    """Raised when the JWT Bearer token is missing."""

    def __init__(self, detail: str = "JWT Bearer is missing"):
        super().__init__(message=detail, status_code=403)


class InvalidBearerSchemeException(AuthException):
    """Raised when the authentication scheme is not Bearer."""

    def __init__(self, detail: str = "JWT Bearer is missing"):
        super().__init__(message=detail, status_code=403)


class ExpiredTokenException(AuthException):
    """Raised when the JWT token has expired."""

    def __init__(self, detail: str = "Expired token"):
        super().__init__(message=detail, status_code=401)


class InvalidTokenException(AuthException):
    """Raised when there is an error decoding the JWT token."""

    def __init__(self, detail: str = "Error decoding JWT"):
        super().__init__(message=detail, status_code=401)


class UnauthorizedException(AuthException):
    """Raised when the user is not authorized."""

    def __init__(self, detail: str = "Unauthorized"):
        super().__init__(message=detail, status_code=401)


class InsufficientRoleException(AuthException):
    """Raised when the user does not have the required role."""

    def __init__(self, roles_str: str):
        super().__init__(
            message=f"Access restricted to {roles_str} only",
            status_code=403
        )
