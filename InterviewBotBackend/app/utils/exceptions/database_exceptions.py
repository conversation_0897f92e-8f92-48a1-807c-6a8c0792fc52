"""
Database exception classes for consistent database error handling.
"""

class DatabaseException(Exception):
    """Base exception class for database operations"""

    def __init__(self, message: str = "A database error occurred", status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class DatabaseOperationException(DatabaseException):
    """Raised when a database operation fails"""

    def __init__(self, operation: str = "database operation"):
        # Use a generic message that doesn't expose sensitive information
        super().__init__(
            message=f"An error occurred during {operation}",
            status_code=500,  # Internal Server Error
        )


class DatabaseRecordNotFoundException(DatabaseException):
    """Raised when a database record is not found"""

    def __init__(self, entity_type: str = "record"):
        super().__init__(
            message=f"{entity_type.capitalize()} not found",
            status_code=404,  # Not Found
        )
