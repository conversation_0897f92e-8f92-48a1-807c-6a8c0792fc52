class InterviewServiceException(Exception):
    """Base exception class for InterviewService"""

    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class InterviewNotFoundException(InterviewServiceException):
    """Raised when an interview cannot be found."""

    def __init__(self, detail: str = "Interview not found"):
        super().__init__(message=detail, status_code=404)


class InterviewCreationException(InterviewServiceException):
    """Raised when interview creation fails."""

    def __init__(self, detail: str = "Failed to create interview"):
        super().__init__(message=detail, status_code=500)


class InterviewUpdateException(InterviewServiceException):
    """Raised when interview update fails."""

    def __init__(self, detail: str = "Failed to update interview"):
        super().__init__(message=detail, status_code=500)


class InterviewDeletionException(InterviewServiceException):
    """Raised when interview deletion fails."""

    def __init__(self, detail: str = "Failed to delete interview"):
        super().__init__(message=detail, status_code=500)


class PastInterviewModificationException(InterviewServiceException):
    """Raised when attempting to modify a past interview."""

    def __init__(self, detail: str = "Cannot modify an interview with a past availability window"):
        super().__init__(message=detail, status_code=400)


class CompletedInterviewModificationException(InterviewServiceException):
    """Raised when attempting to modify a completed interview."""

    def __init__(self, detail: str = "Cannot modify a completed interview"):
        super().__init__(message=detail, status_code=400)


class InvalidInterviewDataException(InterviewServiceException):
    """Raised when interview data is invalid."""

    def __init__(self, detail: str = "Invalid interview data provided"):
        super().__init__(message=detail, status_code=400)


class InvalidTimeWindowException(InterviewServiceException):
    """Raised when the interview time window is invalid."""

    def __init__(self, detail: str = "Invalid time window for interview"):
        super().__init__(message=detail, status_code=400)


class InvalidInterviewDurationException(InterviewServiceException):
    """Raised when the interview duration is invalid."""

    def __init__(self, detail: str = "Invalid interview duration"):
        super().__init__(message=detail, status_code=400)


class ResumeUploadException(InterviewServiceException):
    """Raised when resume upload fails."""

    def __init__(self, detail: str = "Failed to upload resume"):
        super().__init__(message=detail, status_code=500)


class JobDescriptionUploadException(InterviewServiceException):
    """Raised when job description upload fails."""

    def __init__(self, detail: str = "Failed to upload job description"):
        super().__init__(message=detail, status_code=500)


class OrgSetupIncomplete(InterviewServiceException):
    """Raised when organization is not setup"""

    def __init__(self, detail: str = "Organization setup is incomplete"):
        super().__init__(message=detail, status_code=400)


class InterviewStatisticsFetchError(InterviewServiceException):
    """Raised when fetching interview statistics fails"""

    def __init__(self, detail: str = "Failed to fetch interview statistics"):
        super().__init__(message=detail, status_code=500)


class NonIndividualUserAccessException(InterviewServiceException):
    """Raised when a user with non-individual registration type attempts to access functionality 
    restricted to individual users (candidates)."""

    def __init__(self, detail: str = "Access restricted to candidates with 'individual' registration type"):
        super().__init__(message=detail, status_code=403)


# class InterviewRetrievalException(InterviewServiceException):
#     """Raised when there is a failure in retrieving interviews due to a database error."""

#     def __init__(
#         self,
#         detail: str = "Failed to retrieve interviews due to a database error.",
#         interviews: list = None,
#         total_count: int = 0,
#     ):
#         self.interviews = interviews if interviews is not None else []
#         self.total_count = total_count
#         super().__init__(message=detail, status_code=500)


class UnauthorizedAccessException(InterviewServiceException):
    """Raised when a user is not authorised to access a resource."""

    def __init__(
        self, detail: str = "Access restricted.You do not have permission to access this resource."
    ):
        super().__init__(message=detail, status_code=403)


class InterviewNotCompletedException(InterviewServiceException):
    """Raised when a interview is not completed"""

    def __init__(
        self, detail: str = "Access restricted.Interview not completed yet."
    ):
        super().__init__(message=detail, status_code=403)


class NotAllowedToCreateInterview(InterviewServiceException):
    """Raised when a user is not authorised to create an interview."""

    def __init__(self, detail: str = "Access restricted.You do not have permission to create an interview."):
        super().__init__(message=detail, status_code=403)
