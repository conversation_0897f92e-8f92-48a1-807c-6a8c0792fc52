class S3ServiceException(Exception):
    """Base exception class for S3Service"""

    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class S3PresignedUrlException(S3ServiceException):
    """Raised when presigned URL generation fails."""

    def __init__(self, detail: str = "Failed to generate presigned URL"):
        super().__init__(message=detail, status_code=500)


class S3PresignedUrlMissingKey(S3ServiceException):
    """Raised when presigned URL generation fails."""

    def __init__(self, detail: str = "Failed to generate presigned URL"):
        super().__init__(message=detail, status_code=400)


class S3UploadException(S3ServiceException):
    """Raised when direct upload to S3 fails."""

    def __init__(self, detail: str = "Failed to upload file to S3"):
        super().__init__(message=detail, status_code=500)
