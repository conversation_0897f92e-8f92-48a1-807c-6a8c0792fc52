"""
Text extraction exception classes for consistent error handling.
"""

class TextExtractionException(Exception):
    """Base exception class for text extraction operations"""

    def __init__(self, message: str = "A text extraction error occurred", status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class PDFTextExtractionException(TextExtractionException):
    """Raised when text extraction from a PDF fails"""

    def __init__(self, detail: str = "Failed to extract text from PDF"):
        # Use a message that provides context about the error
        super().__init__(
            message=f"Text extraction error: {detail}",
            status_code=500,  # Internal Server Error
        )
