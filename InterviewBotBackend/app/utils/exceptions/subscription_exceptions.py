from fastapi import HTTPException, status


class SubscriptionLimitExceededException(HTTPException):
    def __init__(self, detail: str = "Interview limit exceeded for current subscription plan"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


class SubscriptionNotFoundException(HTTPException):
    def __init__(self, detail: str = "Subscription not found"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )


class InvalidSubscriptionPlanException(HTTPException):
    def __init__(self, detail: str = "Invalid subscription plan"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )


class SubscriptionAssignmentException(HTTPException):
    def __init__(self, detail: str = "Failed to assign subscription to user"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


class SubscriptionPlanNotFoundException(HTTPException):
    def __init__(self, detail: str = "Subscription plan not found"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )


class SubscriptionPlanCreationException(HTTPException):
    def __init__(self, detail: str = "Failed to create subscription plan"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


class SubscriptionPlanUpdateException(HTTPException):
    def __init__(self, detail: str = "Failed to update subscription plan"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


class SubscriptionUsageUpdateException(HTTPException):
    def __init__(self, detail: str = "Failed to update subscription usage"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )