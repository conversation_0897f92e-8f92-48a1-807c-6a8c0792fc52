# app/utils/s3_utils.py

import boto3
from botocore.exceptions import BotoCoreError, <PERSON>lientError
from uuid import uuid4
from fastapi import UploadFile
from typing import Optional, Union, Dict
import os
import io
from app.core.config import settings
from app.utils.exceptions.s3_exceptions import S3PresignedUrlException, S3UploadException
import re
import urllib.parse

# Dictionary to track files that should be auto-deleted
# Key: file path, Value: True if should be auto-deleted
AUTO_DELETE_FILES: Dict[str, bool] = {}

# Register a cleanup function to delete any remaining temporary files when the application exits
import atexit

def cleanup_temp_files():
    """Delete any remaining temporary files when the application exits."""
    for file_path in list(AUTO_DELETE_FILES.keys()):
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"Cleanup: Deleted temporary file {file_path}")
            except Exception as e:
                print(f"Cleanup: Failed to delete temporary file {file_path}: {str(e)}")

# Register the cleanup function to run when the application exits
atexit.register(cleanup_temp_files)

AWS_ACCESS_KEY_ID = settings.AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY = settings.AWS_SECRET_ACCESS_KEY
AWS_REGION = settings.AWS_REGION
S3_BUCKET_NAME = settings.S3_BUCKET_NAME

s3_client = boto3.client(
    "s3",
    region_name=AWS_REGION,
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
)

def generate_s3_key(filename: str, user_id: str) -> str:
    unique_id = str(uuid4())
    return f"uploads/{user_id}/{unique_id}_{filename}"


def generate_presigned_url(key: str, expiration: int = 3600, content_type: Optional[str] = None) -> Optional[str]:
    """
    Generate a presigned URL for uploading a file to S3.

    Args:
        key: The S3 key/path where the file will be stored
        expiration: The time in seconds that the presigned URL will be valid
        content_type: The MIME type of the file. If not provided, it will be determined from the file extension.
                     Supported types: PDF, PNG, JPEG/JPG

    Returns:
        A presigned URL that can be used to upload a file to S3

    Raises:
        S3PresignedUrlException: If there's an error generating the presigned URL
    """
    try:
        # Determine content type from file extension if not provided
        if not content_type:
            file_extension = key.split('.')[-1].lower() if '.' in key else ''
            content_type = {
                'pdf': 'application/pdf',
                'png': 'image/png',
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg'
            }.get(file_extension, 'application/octet-stream')

        return s3_client.generate_presigned_url(
            "put_object",
            Params={
                "Bucket": S3_BUCKET_NAME,
                "Key": key,
                "ContentType": content_type,
            },
            ExpiresIn=expiration,
            HttpMethod="PUT",
        )
    except ClientError as e:
        raise S3PresignedUrlException(detail=str(e))


def download_file_from_s3(
    s3_key: str, download_path: Optional[str] = None, file_type: Optional[str] = None,
    auto_delete: bool = False
) -> str:
    """
    Download a file from S3 given its S3 key, with file type validation.

    Args:
        s3_key: The S3 key/path of the file to download
        download_path: Optional local path where the file should be saved.
                      If not provided, a temporary path will be generated.
        file_type: The type of file to allow (default: None, which allows any supported type)
                  Supported types: pdf, png, jpg, jpeg
        auto_delete: If True, the local file will be automatically deleted after being uploaded to S3
                    using the upload_file_to_s3 function. Default is False.

    Returns:
        The local path where the file was downloaded

    Raises:
        S3PresignedUrlException: If there's an error downloading the file or if file type is not supported
    """
    try:
        # Clean up the S3 key if it includes the full S3 URI
        if s3_key.startswith("s3://"):
            # Remove the bucket prefix if present
            s3_uri_parts = s3_key.replace("s3://", "").split("/", 1)
            if len(s3_uri_parts) > 1 and s3_uri_parts[0] == S3_BUCKET_NAME:
                s3_key = s3_uri_parts[1]
            else:
                raise S3PresignedUrlException(f"Invalid S3 URI format or bucket mismatch: {s3_key}")

        # Check file type if specified
        file_extension = s3_key.split(".")[-1].lower() if "." in s3_key else ""
        supported_types = ["pdf", "png", "jpg", "jpeg"]

        if file_type and file_extension != file_type.lower():
            raise S3PresignedUrlException(
                f"Unsupported file type: '{file_extension}'. Only {file_type} files are supported."
            )

        if file_extension not in supported_types:
            raise S3PresignedUrlException(
                f"Unsupported file type: '{file_extension}'. Supported types are: {', '.join(supported_types)}"
            )


        # Check if file exists in S3 before downloading
        try:
            s3_client.head_object(Bucket=S3_BUCKET_NAME, Key=s3_key)
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                raise S3PresignedUrlException(f"File not found in S3: {s3_key}")
            else:
                raise


        # Generate a download path if not provided
        if not download_path:
            filename = os.path.basename(s3_key)
            download_path = os.path.join(os.getcwd(), f"{uuid4()}_{filename}")

        # Ensure the directory exists
        os.makedirs(os.path.dirname(download_path), exist_ok=True)

        # Download the file from S3
        s3_client.download_file(Bucket=S3_BUCKET_NAME, Key=s3_key, Filename=download_path)

        # If auto_delete is True, add the file path to the global dictionary
        if auto_delete:
            AUTO_DELETE_FILES[download_path] = True

        return download_path

    except (BotoCoreError, ClientError) as e:
        error_msg = f"Error downloading file from S3: {str(e)}"
        raise S3PresignedUrlException(error_msg)
    except Exception as e:
        error_msg = f"Unexpected error downloading file from S3: {str(e)}"
        raise S3PresignedUrlException(error_msg)


def upload_file_to_s3(file_content: Union[bytes, io.BytesIO], key: str, content_type: str = None, local_file_path: str = None) -> str:
    """
    Upload a file directly to S3.

    Args:
        file_content: The content of the file as bytes or BytesIO
        key: The S3 key/path where the file will be stored
        content_type: The MIME type of the file. If not provided, it will be determined from the file extension.
        local_file_path: Optional path to a local file that should be deleted after successful upload

    Returns:
        The S3 URL of the uploaded file

    Raises:
        S3UploadException: If there's an error uploading the file
    """
    try:
        # Determine content type from file extension if not provided
        if not content_type:
            file_extension = key.split('.')[-1].lower() if '.' in key else ''
            content_type = {
                'pdf': 'application/pdf',
                'png': 'image/png',
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg'
            }.get(file_extension, 'application/octet-stream')

        # Convert BytesIO to bytes if needed
        if isinstance(file_content, io.BytesIO):
            file_content.seek(0)
            file_content = file_content.read()

        # Upload the file to S3
        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=key,
            Body=file_content,
            ContentType=content_type
        )

        # Delete the local file if a path was provided or if it's in the auto-delete dictionary
        should_delete = False

        # Check if local_file_path was explicitly provided
        if local_file_path and os.path.exists(local_file_path):
            should_delete = True

        # Check if the file is in the auto-delete dictionary
        if not should_delete:
            for path in list(AUTO_DELETE_FILES.keys()):
                if os.path.exists(path):
                    should_delete = True
                    local_file_path = path
                    # Remove from dictionary to avoid trying to delete it again
                    del AUTO_DELETE_FILES[path]
                    break

        # Delete the file if needed
        if should_delete and local_file_path:
            try:
                os.remove(local_file_path)
                print(f"Successfully deleted local file: {local_file_path}")
            except Exception as e:
                print(f"Warning: Failed to delete local file {local_file_path}: {str(e)}")

        # Generate the S3 URL
        s3_url = f"https://{S3_BUCKET_NAME}.s3.{AWS_REGION}.amazonaws.com/{key}"
        return s3_url
    except (BotoCoreError, ClientError) as e:
        # If upload fails and we have a local file, don't delete it
        raise S3UploadException(f"Error uploading file to S3: {str(e)}")
    except Exception as e:
        # If any other error occurs and we have a local file, don't delete it
        raise S3UploadException(f"Unexpected error uploading file to S3: {str(e)}")


async def parse_s3_url(s3_url: str) -> str:
    """
    Extract the S3 key from an S3 URL.

    Args:
        s3_url: The S3 URL to parse

    Returns:
        The S3 key extracted from the URL

    Raises:
        ValueError: If the URL format is invalid
    """
    try:
        # Handle different S3 URL formats
        s3_key = None

        # Format: https://bucket-name.s3.region.amazonaws.com/key
        if "amazonaws.com" in s3_url:
            match = re.search(r"https?://[^/]+/(.+)", s3_url)
            if match:
                s3_key = match.group(1)
        # Format: s3://bucket-name/key
        elif s3_url.startswith("s3://"):
            parts = s3_url.replace("s3://", "").split("/", 1)
            if len(parts) > 1:
                s3_key = parts[1]

        if not s3_key:
            raise ValueError(f"Invalid S3 URL format: {s3_url}")

        decoded_key = urllib.parse.unquote(s3_key)
        return decoded_key
    except Exception as e:
        raise ValueError(f"Failed to parse S3 URL: {str(e)}")


