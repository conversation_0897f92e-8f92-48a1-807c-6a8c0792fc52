from typing import Dict, List, Optional, Any
from jose import jwt
import os
from fastapi import Security, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from functools import wraps
from app.schemas.user_schema import RegistrationType
from app.services.user_service import UserService
from app.core.config import settings
from app.db.session import SessionLocal
from app.utils.exceptions.auth_exceptions import (
    MissingBearerTokenException,
    InvalidBearerSchemeException,
    ExpiredTokenException,
    InvalidTokenException,
    UnauthorizedException,
    InsufficientRoleException
)


# Auth guard base class
class BaseAuthGuard:
    security = HTTPBearer()

    def __init__(self):
        self.user_service = UserService(SessionLocal())

    async def __call__(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer()),
    ) -> Dict[str, Any]:
        if not credentials:
            raise MissingBearerTokenException()
        if credentials.scheme != "Bearer":
            raise InvalidBearerSchemeException()
        user = await self.validate_token(credentials.credentials)
        return user

    async def validate_token(self, token: str) -> Dict[str, Any]:
        try:
            # Get JWT secret from settings
            jwt_secret = settings.JWT_SECRET_KEY
            token_info = jwt.decode(token, jwt_secret, algorithms=[settings.JWT_ALGORITHM])

            user_dict = {
                "user_id": token_info.get("user_id"),
                "email": token_info.get("email"),
                "registration_type": token_info.get("registration_type"),
            }
            return {**user_dict}

        except jwt.ExpiredSignatureError:
            raise ExpiredTokenException()
        except jwt.JWTError:
            raise InvalidTokenException()
        except Exception:
            raise UnauthorizedException()


def roles_required(allowed_roles: List[RegistrationType]):
    """
    Decorator factory that creates a dependency requiring user to have one of the specified roles.

    This function returns a dependency that can be used with FastAPI's Depends().
    It checks if the current user has one of the allowed roles and raises
    InsufficientRoleException if not.

    Args:
        allowed_roles: List of registration types that are allowed to access the endpoint

    Returns:
        A dependency function that validates user roles

    Raises:
        InsufficientRoleException: When the user's registration type is not in the allowed roles
    """

    def dependency(
        current_user: Dict[str, Any] = Depends(BaseAuthGuard()),
    ) -> Dict[str, Any]:
        user_type = current_user.get("registration_type")

        if not user_type:
            raise InsufficientRoleException("any role")

        if user_type not in allowed_roles:
            roles_str = ", ".join(
                [role.value for role in allowed_roles]
            )
            raise InsufficientRoleException(roles_str)

        return current_user

    return dependency
