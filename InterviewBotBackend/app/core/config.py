from typing import Any, Dict, Optional
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, validator


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "interview-backend"
    DEBUG: bool = False
    PORT: int = 5000

    API_V1_STR: str = ""

    # Database settings
    DB_HOST: str
    DB_PORT: str
    DB_USER: str
    DB_PASSWORD: str
    DB_NAME: str
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"DB_USER", "DB_PASSWORD", "DB_HOST", "DB_PORT", "DB_NAME"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required database configuration: {missing}")

        return PostgresDsn.build(
            scheme="postgresql",
            username=values.get("DB_USER"),
            password=values.get("DB_PASSWORD"),
            host=values.get("DB_HOST"),
            port=int(values.get("DB_PORT", 5432)),
            path=f"{values.get('DB_NAME')}",
        )

    # JWT settings
    JWT_SECRET_KEY: str = ""
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_REDIRECT_URI: str = ""
    
    # Frontend URLs for OAuth redirects
    FRONTEND_SUCCESS_REDIRECT_URL: str = "http://localhost:3000/dashboard"
    FRONTEND_ERROR_REDIRECT_URL: str = "http://localhost:3000/login"

    AWS_ACCESS_KEY_ID: str = ""
    AWS_SECRET_ACCESS_KEY: str = ""
    AWS_REGION: str = ""
    S3_BUCKET_NAME: str = ""

    MCP_SSE_URL: str = ""

    ADMIN_EMAIL: str = ""
    ADMIN_PASSWORD: str = ""

    LIVEKIT_URL:str=""
    LIVEKIT_API_SECRET:str=""
    LIVEKIT_API_KEY:str=""

    # Razorpay settings
    RAZORPAY_KEY_ID: str = ""
    RAZORPAY_KEY_SECRET: str = ""
    RAZORPAY_WEBHOOK_SECRET: str = ""

    # CORS settings for Next.js integration
    ALLOWED_HOSTS: list[str] = [
        "http://localhost:3000",  # Next.js dev server
        "https://localhost:3000",
        "http://127.0.0.1:3000",
        "https://127.0.0.1:3000",
    ]

    # Project settings for payment integration
    PROJECT_NAME: str = "Interview Backend API"
    ENVIRONMENT: str = "development"

    # Email Configuration
    MAIL_USERNAME: str = ""
    MAIL_PASSWORD: str = ""
    MAIL_FROM: str = ""
    MAIL_PORT: int = 587
    MAIL_SERVER: str = ""
    MAIL_FROM_NAME: str = "EvalFast"
    MAIL_STARTTLS: bool = True
    MAIL_SSL_TLS: bool = False
    USE_CREDENTIALS: bool = True
    VALIDATE_CERTS: bool = True

    # OTP Configuration
    OTP_EXPIRY_MINUTES: int = 10
    OTP_LENGTH: int = 6

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
