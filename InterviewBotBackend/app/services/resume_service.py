import uuid
import io
import json
import os
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.models.resume import Resume
from app.models.user import User
from app.schemas.resume_schema import ResumeCreate, ResumeResponse
from app.schemas.mcp_tool_schema import BuildResumeSchema
from app.utils.exceptions.user_exceptions import UnAuthorizedUserException
from app.utils.exceptions.database_exceptions import DatabaseOperationException
from app.utils.aws_s3 import upload_file_to_s3, S3_BUCKET_NAME
from app.utils.pdf_generator import generate_resume_pdf


class ResumeService:
    def __init__(self, db: Session):
        self.db = db

    async def create_resume(self, resume_data: ResumeCreate, user_id: str) -> Dict[str, Any]:
        """
        Create a new resume, convert to PDF, upload to S3, save locally, and save the record.
        
        Args:
            resume_data: Resume creation data
            user_id: ID of the authenticated user
            
        Returns:
            Dictionary with success status and resume data
        """
        # Verify user exists
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise UnAuthorizedUserException(detail="User not found")

        # Validate template requirements
        try:
            self._validate_template_requirements(
                resume_data.content, resume_data.content.template_id
            )
        except ValueError as e:
            raise DatabaseOperationException(f"Template validation failed: {str(e)}")

        # Generate unique resume ID
        resume_id = str(uuid.uuid4())

        try:
            # Convert structured content to dict for PDF generation
            content_dict = resume_data.content.model_dump()

            # Generate PDF from resume content with template
            pdf_buffer = generate_resume_pdf({
                'title': resume_data.title,
                'template': resume_data.content.template_id,
                'content': content_dict
            })

            # Upload PDF to S3
            s3_key = f"resumes/{user_id}/{resume_id}.pdf"
            pdf_s3_url = upload_file_to_s3(
                file_content=pdf_buffer.getvalue(),
                key=s3_key,
                content_type="application/pdf"
            )

            # Create resume record
            resume = Resume(
                id=resume_id,
                user_id=user_id,
                title=resume_data.title,
                content=json.dumps(content_dict),  # Convert structured content to JSON string
                template=resume_data.content.template_id,  # Convert enum to string
                pdf_s3_url=pdf_s3_url,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )

            self.db.add(resume)
            self.db.commit()
            self.db.refresh(resume)

            return {
                "success": True,
                "message": "Resume created, PDF uploaded to S3 and saved locally successfully",
                "data": {
                    "id": resume.id,
                    "user_id": resume.user_id,
                    "title": resume.title,
                    "content": BuildResumeSchema(**json.loads(resume.content)),  # Convert JSON string back to structured format
                    "template": resume.template,
                    "pdf_s3_url": resume.pdf_s3_url,
                    "created_at": resume.created_at.isoformat(),
                    "updated_at": resume.updated_at.isoformat()
                }
            }

        except Exception as e:
            self.db.rollback()
            raise DatabaseOperationException(f"creating resume: {str(e)}")

    def get_user_resumes(self, user_id: str) -> Dict[str, Any]:
        """
        Get all resumes for a specific user.
        
        Args:
            user_id: ID of the authenticated user
            
        Returns:
            Dictionary with success status and list of resumes
        """
        # Verify user exists
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise UnAuthorizedUserException(detail="User not found")

        try:
            resumes = self.db.query(Resume).filter(Resume.user_id == user_id).order_by(Resume.created_at.desc()).all()

            resume_list = []
            for resume in resumes:
                resume_list.append({
                    "id": resume.id,
                    "user_id": resume.user_id,
                    "title": resume.title,
                    "template": resume.template,
                    "pdf_s3_url": resume.pdf_s3_url,
                    "created_at": resume.created_at.isoformat(),
                    "updated_at": resume.updated_at.isoformat()
                })

            return {
                "success": True,
                "message": "Resumes retrieved successfully",
                "data": resume_list
            }

        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"retrieving resumes: {str(e)}")

    def get_resume_by_id(self, resume_id: str, user_id: str) -> Dict[str, Any]:
        """
        Get a specific resume by ID for the authenticated user.
        
        Args:
            resume_id: ID of the resume
            user_id: ID of the authenticated user
            
        Returns:
            Dictionary with success status and resume data
        """
        try:
            resume = self.db.query(Resume).filter(
                Resume.id == resume_id,
                Resume.user_id == user_id
            ).first()

            if not resume:
                raise UnAuthorizedUserException(detail="Resume not found or access denied")

            return {
                "success": True,
                "message": "Resume retrieved successfully",
                "data": {
                    "id": resume.id,
                    "user_id": resume.user_id,
                    "title": resume.title,
                    "content": BuildResumeSchema(**json.loads(resume.content)),  # Convert JSON string back to structured format
                    "template": resume.template,
                    "pdf_s3_url": resume.pdf_s3_url,
                    "created_at": resume.created_at.isoformat(),
                    "updated_at": resume.updated_at.isoformat()
                }
            }

        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"retrieving resume: {str(e)}")

    def delete_resume(self, resume_id: str, user_id: str) -> Dict[str, Any]:
        """
        Delete a specific resume for the authenticated user.
        
        Args:
            resume_id: ID of the resume
            user_id: ID of the authenticated user
            
        Returns:
            Dictionary with success status
        """
        try:
            resume = self.db.query(Resume).filter(
                Resume.id == resume_id,
                Resume.user_id == user_id
            ).first()

            if not resume:
                raise UnAuthorizedUserException(detail="Resume not found or access denied")

            self.db.delete(resume)
            self.db.commit()

            return {
                "success": True,
                "message": "Resume deleted successfully"
            }

        except SQLAlchemyError as e:
            self.db.rollback()
            raise DatabaseOperationException(f"deleting resume: {str(e)}")

    def _get_professional_template(self) -> Dict[str, Any]:
        """Professional resume template structure."""
        return {
            "template_name": "professional",
            "description": "A classic professional resume template suitable for most industries",
            "sections": [
                "contact_info",
                "professional_summary",
                "work_experience",
                "education",
                "skills",
                "projects"
                "certifications",
            ],
            "formatting": {
                "font": "Arial",
                "style": "classic",
                "color_scheme": "black_white",
            },
        }

    def _get_modern_template(self) -> Dict[str, Any]:
        """Modern resume template structure."""
        return {
            "template_name": "modern",
            "description": "A contemporary resume template with visual appeal",
            "sections": [
                "contact_info",
                "professional_summary",
                "work_experience",
                "education",
                "skills",
                "projects",
                "certifications",
            ],
            "formatting": {
                "font": "Calibri",
                "style": "contemporary",
                "color_scheme": "blue_accent",
            },
        }

    def _get_academic_template(self) -> Dict[str, Any]:
        """Academic resume template structure."""
        return {
            "template_name": "academic",
            "description": "A detailed resume template for academic and research positions",
            "sections": [
                "contact_info",
                "objective",
                "education",
                "research_experience",
                "publications",
                "skills",
                "certifications",
            ],
            "formatting": {
                "font": "Times New Roman",
                "style": "traditional",
                "color_scheme": "black_white",
            },
        }

    def _validate_template_requirements(
        self, resume_content: BuildResumeSchema, template_name: str
    ) -> None:
        """
        Validate that resume content has all required sections for the specified template.

        Args:
            resume_content: The resume content to validate
            template_name: The template name to validate against

        Raises:
            ValueError: If required sections are missing
        """
        # Get template requirements
        template_methods = {
            "professional": self._get_professional_template,
            "modern": self._get_modern_template,
            "academic": self._get_academic_template,
        }

        if template_name not in template_methods:
            raise ValueError(f"Unknown template: {template_name}")

        template_config = template_methods[template_name]()
        required_sections = template_config["sections"]

        # Convert resume content to dict for easier checking
        content_dict = resume_content.model_dump()

        # Check each required section
        missing_sections = []
        for section in required_sections:
            if section == "contact_info":
                if not content_dict.get("contact_info"):
                    missing_sections.append(section)
            elif section == "professional_summary":
                if not content_dict.get("professional_summary"):
                    missing_sections.append(section)
            elif section == "objective":
                if not content_dict.get("objective"):
                    missing_sections.append(section)
            elif section == "work_experience":
                work_exp = content_dict.get("work_experience", [])
                if not work_exp or len(work_exp) == 0:
                    missing_sections.append(section)
            elif section == "education":
                education = content_dict.get("education", [])
                if not education or len(education) == 0:
                    missing_sections.append(section)
            elif section == "skills":
                skills = content_dict.get("skills", [])
                if not skills or len(skills) == 0:
                    missing_sections.append(section)
            elif section == "projects":
                projects = content_dict.get("projects", [])
                if not projects or len(projects) == 0:
                    missing_sections.append(section)
            elif section == "certifications":
                certifications = content_dict.get("certifications", [])
                if not certifications or len(certifications) == 0:
                    missing_sections.append(section)
            elif section == "research_experience":
                research_exp = content_dict.get("research_experience", [])
                if not research_exp or len(research_exp) == 0:
                    missing_sections.append(section)
            elif section == "publications":
                publications = content_dict.get("publications", [])
                if not publications or len(publications) == 0:
                    missing_sections.append(section)

        if missing_sections:
            raise ValueError(
                f"Missing required sections for {template_name} template: {', '.join(missing_sections)}"
            )
