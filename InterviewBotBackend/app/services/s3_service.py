# app/services/s3_service.py
from fastapi import HTTPException
from app.utils.aws_s3 import generate_presigned_url
from app.utils.exceptions.s3_exceptions import S3PresignedUrlMissingKey

class S3Service:

    async def get_presigned_url(key: str, content_type: str = None):
        """
        Generate a presigned URL for uploading a file to S3.

        Args:
            key: The S3 key/path where the file will be stored
            content_type: Optional MIME type of the file. If not provided, it will be determined from the file extension.
                         Supported types: PDF, PNG, JPEG/JPG

        Returns:
            A dictionary containing the presigned URL and success status

        Raises:
            S3PresignedUrlMissingKey: If the key is not provided
            HTTPException: If there's an error generating the presigned URL
        """

        if not key:
            raise S3PresignedUrlMissingKey("Missing 'key' query param")
        try:
            url = generate_presigned_url(
                key=key,
                content_type=content_type
            )
            return {
                "success": True,
                "message":"PresignedURL generated successfully",
                "url": url,
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to generate presigned URL"
            )
