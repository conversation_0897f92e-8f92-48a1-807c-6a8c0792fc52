import uuid
import random
import string
from datetime import datetime, timedelta
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from sqlalchemy.orm import Session

from app.models.otp_verification import OTPVerification, OTPType
from app.core.config import settings
from app.core.security import get_password_hash, verify_password


class OTPService:
    def __init__(self, db: Session):
        self.db = db
        self.otp_length = getattr(settings, 'OTP_LENGTH', 6)
        self.otp_expiry_minutes = getattr(settings, 'OTP_EXPIRY_MINUTES', 10)
        self.resend_cooldown_minutes = 1.5  # 1.5 minutes cooldown for resend

    def generate_otp(self) -> str:
        """Generate a random OTP code"""
        return ''.join(random.choices(string.digits, k=self.otp_length))

    def can_send_otp(self, email: str, otp_type: OTPType) -> Tuple[bool, str]:
        """
        Check if OTP can be sent (respects cooldown period)

        Args:
            email: Email address
            otp_type: Type of OTP

        Returns:
            Tuple[bool, str]: (can_send, message)
        """
        # Check for recent OTP creation
        recent_otp = self.db.query(OTPVerification).filter(
            OTPVerification.email == email,
            OTPVerification.otp_type == otp_type,
            OTPVerification.created_at > datetime.utcnow() - timedelta(minutes=self.resend_cooldown_minutes)
        ).first()

        if recent_otp:
            time_remaining = (recent_otp.created_at + timedelta(minutes=self.resend_cooldown_minutes) - datetime.utcnow()).total_seconds()
            minutes_remaining = int(time_remaining // 60)
            seconds_remaining = int(time_remaining % 60)
            return False, f"Please wait {minutes_remaining}m {seconds_remaining}s before requesting a new OTP"

        return True, "OTP can be sent"

    def create_otp(self, email: str, otp_type: OTPType, force: bool = False) -> Tuple[str, str]:
        """
        Create a new OTP for the given email and type

        Args:
            email: Email address to create OTP for
            otp_type: Type of OTP (email_verification, password_reset)
            force: If True, bypass cooldown check (for initial signup)

        Returns:
            Tuple[str, str]: (otp_code, message) - OTP code and status message
        """
        # Check cooldown unless forced (initial signup)
        if not force:
            can_send, message = self.can_send_otp(email, otp_type)
            if not can_send:
                return "", message

        # Invalidate any existing OTPs for this email and type
        self.invalidate_existing_otps(email, otp_type)

        # Generate new OTP
        otp_code = self.generate_otp()
        expires_at = datetime.utcnow() + timedelta(minutes=self.otp_expiry_minutes)

        # Hash the OTP code before storing (same as password hashing)
        hashed_otp = get_password_hash(otp_code)

        # Create OTP record with hashed OTP
        otp_record = OTPVerification(
            id=str(uuid.uuid4()),
            email=email,
            otp_code=hashed_otp,  # Store encrypted OTP
            otp_type=otp_type,
            expires_at=expires_at
        )

        self.db.add(otp_record)
        self.db.commit()

        return otp_code, "OTP created successfully"  # Return plain text OTP for email sending

    def verify_otp(self, email: str, otp_code: str, otp_type: OTPType) -> Tuple[bool, str]:
        """
        Verify an OTP code

        Args:
            email: Email address
            otp_code: OTP code to verify (plain text)
            otp_type: Type of OTP

        Returns:
            Tuple[bool, str]: (is_valid, message)
        """
        # Find all unused OTP records for this email and type
        otp_records = self.db.query(OTPVerification).filter(
            OTPVerification.email == email,
            OTPVerification.otp_type == otp_type,
            OTPVerification.is_used == False
        ).all()

        if not otp_records:
            return False, "Invalid OTP code"

        # Check each OTP record to find a match (since OTPs are hashed)
        for otp_record in otp_records:
            if otp_record.is_expired():
                continue

            # Verify the plain text OTP against the hashed OTP
            if verify_password(otp_code, otp_record.otp_code):
                # Mark OTP as used
                otp_record.is_used = True
                otp_record.used_at = datetime.utcnow()
                self.db.commit()
                return True, "OTP verified successfully"

        return False, "Invalid or expired OTP code"

    def invalidate_existing_otps(self, email: str, otp_type: OTPType) -> None:
        """
        Invalidate all existing OTPs for the given email and type
        
        Args:
            email: Email address
            otp_type: Type of OTP
        """
        existing_otps = self.db.query(OTPVerification).filter(
            OTPVerification.email == email,
            OTPVerification.otp_type == otp_type,
            OTPVerification.is_used == False
        ).all()
        
        for otp in existing_otps:
            otp.is_used = True
            otp.used_at = datetime.utcnow()
        
        self.db.commit()

    def cleanup_expired_otps(self) -> int:
        """
        Clean up expired OTP records
        
        Returns:
            int: Number of records cleaned up
        """
        expired_otps = self.db.query(OTPVerification).filter(
            OTPVerification.expires_at < datetime.utcnow()
        ).all()
        
        count = len(expired_otps)
        
        for otp in expired_otps:
            self.db.delete(otp)
        
        self.db.commit()
        
        return count

    def get_otp_status(self, email: str, otp_type: OTPType) -> Optional[dict]:
        """
        Get the status of the latest OTP for an email and type
        
        Args:
            email: Email address
            otp_type: Type of OTP
            
        Returns:
            Optional[dict]: OTP status information or None if no OTP found
        """
        latest_otp = self.db.query(OTPVerification).filter(
            OTPVerification.email == email,
            OTPVerification.otp_type == otp_type
        ).order_by(OTPVerification.created_at.desc()).first()
        
        if not latest_otp:
            return None
        
        return {
            "id": latest_otp.id,
            "email": latest_otp.email,
            "otp_type": latest_otp.otp_type.value,
            "is_used": latest_otp.is_used,
            "is_expired": latest_otp.is_expired(),
            "is_valid": latest_otp.is_valid(),
            "expires_at": latest_otp.expires_at,
            "created_at": latest_otp.created_at,
            "used_at": latest_otp.used_at
        }
