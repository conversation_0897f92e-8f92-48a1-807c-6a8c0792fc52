import json
import logging
from typing import Dict, Any
from livekit import api
from livekit.api.access_token import AccessToken, VideoGrants
from sqlalchemy.orm import Session
from app.schemas.livekit_schema import RoomDetails
from app.db.session import SessionLocal
from app.services.interview_service import InterviewService
from datetime import datetime, timezone, timedelta
from app.core.config import settings
from app.utils.exceptions.livekit_exceptions import (
    LiveKitInitializationException,
    InterviewNotFoundException,
    UnauthorizedAccessException,
    CompletedInterviewException,
    InterviewTimingException,
    MissingQuestionsException,
    MetadataSizeExceededException,
    RoomCreationException,
    RoomNotFoundException,
    TokenGenerationException
)


logger = logging.getLogger(__name__)


class LiveKitService:
    def __init__(self, db: Session):
        self.db = db
        self.interview_service = InterviewService(SessionLocal())
        self.livekit_api = None

    async def initialize_livekit(self):
        if self.livekit_api is None:
            try:
                self.livekit_api = api.LiveKitAPI(
                    url=settings.LIVEKIT_URL,
                    api_key=settings.LIVEKIT_API_KEY,
                    api_secret=settings.LIVEKIT_API_SECRET,
                )
                logger.info("LiveKit API initialized.")
            except Exception as e:
                logger.error(f"Failed to initialize LiveKit API: {e}", exc_info=True)
                raise LiveKitInitializationException()

    async def create_room(self, details: RoomDetails, current_user: dict) -> Dict[str, Any]:
        await self.initialize_livekit()

        interview = self.interview_service.get_interview_by_id(details.interview_id)
        if not interview:
            raise InterviewNotFoundException()

        # Check if current user is the interview candidate
        if interview.candidate_email.lower() != current_user.get("email").lower():
            raise UnauthorizedAccessException()

        if interview.is_completed:
            raise CompletedInterviewException()

        now = datetime.now(timezone.utc)

        # Check if current time is within interview availability window
        if interview.available_from and now < interview.available_from:
            raise InterviewTimingException(detail="Interview has not started yet")
        if interview.available_until and now > interview.available_until:
            raise InterviewTimingException(detail="Interview availability has expired")

        if not interview.questions:
            raise MissingQuestionsException()

        # Prepare room metadata
        room_name = f"interview-{interview.id}-{datetime.now()}"
        metadata = {
            "interview_id": interview.id,
            "candidate_name": interview.candidate_name,
            "candidate_email": interview.candidate_email,
            "questions": interview.questions,
        }

        metadata_str = json.dumps(metadata)
        if len(metadata_str.encode("utf-8")) > 64 * 1024:
            raise MetadataSizeExceededException()

        room_created = False
        metadata_updated = False

        try:
            try:
                await self.livekit_api.room.create_room(
                    api.CreateRoomRequest(name=room_name, metadata=metadata_str)
                )
                room_created = True
            except Exception as e:
                if "room already exists" in str(e).lower():
                    await self.livekit_api.room.update_room_metadata(
                        api.UpdateRoomMetadataRequest(room=room_name, metadata=metadata_str)
                    )
                    metadata_updated = True
                else:
                    raise

            rooms_list = await self.livekit_api.room.list_rooms(
                api.ListRoomsRequest(names=[room_name])
            )
            if not rooms_list.rooms:
                raise ValueError(f"Failed to create or find room '{room_name}'")

            room_info = rooms_list.rooms[0]
            return {
                "room_name": room_name,
                "room_info": {
                    "sid": room_info.sid,
                    "name": room_info.name,
                    "empty_timeout": room_info.empty_timeout,
                    "max_participants": room_info.max_participants,
                    "created_at": room_info.creation_time,
                    "metadata": room_info.metadata,
                    "num_participants": room_info.num_participants,
                },
                "created": room_created,
                "metadata_updated": metadata_updated,
                "success": True,
            }

        except ValueError as e:
            logger.error(f"Failed to create or find room '{room_name}': {e}", exc_info=True)
            raise RoomCreationException(detail=str(e))
        except Exception as e:
            logger.error(f"Failed to create or update room '{room_name}': {e}", exc_info=True)
            raise RoomCreationException()

    async def generate_token(self, room_name: str, current_user: dict) -> str:
        await self.initialize_livekit()

        try:
            rooms_list = await self.livekit_api.room.list_rooms(
                api.ListRoomsRequest(names=[room_name])
            )
            if not rooms_list.rooms:
                raise RoomNotFoundException(room_name)

            metadata_str = "sss"
            video_grants = VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=True,
                can_subscribe=True,
                can_publish_data=True,
            )
            access_token = (
                AccessToken(settings.LIVEKIT_API_KEY, settings.LIVEKIT_API_SECRET)
                .with_identity(current_user["email"])
                .with_ttl(timedelta(seconds=3600))
                .with_metadata(metadata_str)
                .with_grants(video_grants)
            ).to_jwt()
            return access_token
        except RoomNotFoundException:
            raise
        except Exception as e:
            logger.error(f"Failed to generate token: {e}", exc_info=True)
            raise TokenGenerationException()
