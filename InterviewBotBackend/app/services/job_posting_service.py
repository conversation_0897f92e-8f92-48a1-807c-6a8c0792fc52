import uuid
import math
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from app.models.job_posting import JobPosting
from app.models.organization import Organization
from app.models.enums import JobStatus, WorkplaceType, EmploymentType
from app.schemas.job_posting_schema import (
    JobPostingCreateSchema,
    JobPostingUpdateSchema,
    JobAnalyticsSchema,
    JobSearchFiltersSchema,
)
from app.utils.exceptions.job_exceptions import (
    JobPostingNotFoundException,
    JobPostingCreationException,
    JobPostingUpdateException,
    UnauthorizedJobAccessException,
    JobPostingClosedException,
)
from app.utils.exceptions.user_exceptions import OrganizationNotFoundException
from app.services.subscription_service import SubscriptionService


class JobPostingService:
    def __init__(self, db: Session):
        self.db = db
        self.subscription_service = SubscriptionService(db)

    def create_job_posting(
        self, org_id: str, job_data: JobPostingCreateSchema
    ) -> JobPosting:
        """Create a new job posting for an organization."""
        try:
            # Check subscription limits for job postings
            usage = self.subscription_service.get_organization_subscription_usage(org_id)
            if not usage.can_apply_to_job:  # For organizations, this represents creating job postings
                raise JobPostingCreationException(
                    f"Job posting limit exceeded. Your organization has used {usage.jobs_used} out of {usage.jobs_limit} job postings in your {usage.plan_name}."
                )

            # Get organization details to auto-fill company information
            organization = self.db.query(Organization).filter(Organization.id == org_id).first()
            if not organization:
                raise OrganizationNotFoundException(org_id)

            # Validate organization setup is complete
            if not organization.is_setup_complete:
                raise JobPostingCreationException("Organization setup is incomplete. Please complete the setup by adding skill sets and job roles.")

            # Validate job_role against org's job_roles
            org_job_roles = organization.job_roles or []
            if job_data.job_role not in org_job_roles:
                raise JobPostingCreationException(f"Job role '{job_data.job_role}' not found in organization profile. Available roles: {org_job_roles}")
            
            # Validate required_skills against org's skill_sets
            org_skills = set(organization.skill_sets or [])
            for skill in job_data.required_skills:
                if skill not in org_skills:
                    raise JobPostingCreationException(f"Skill '{skill}' not found in organization profile. Available skills: {list(org_skills)}")
            
            # Validate JD reference if jd_id is provided
            if job_data.jd_id:
                standalone_jds = organization.standalone_job_descriptions or {}
                if job_data.jd_id not in standalone_jds:
                    raise JobPostingCreationException(f"Job description ID '{job_data.jd_id}' not found in organization profile")

            print("job_data", job_data)
            # Create job posting
            job_posting = JobPosting(
                id=str(uuid.uuid4()),
                organization_id=org_id,
                job_title=job_data.job_title,
                company_name=organization.organization_name,
                workplace_type=job_data.workplace_type,
                job_location=job_data.job_location,
                employment_type=job_data.employment_type,
                job_role=job_data.job_role,
                jd_link=job_data.jd_link,
                jd_id=job_data.jd_id,
                required_skills=job_data.required_skills,
                screening_questions=[
                    {
                        "question": q.question,
                        "type": q.type.value,
                        "required": q.required,
                        "options": q.options,
                    }
                    for q in job_data.screening_questions
                ] if job_data.screening_questions else None,
                company_location=organization.location,
                company_website=organization.website,
                status=JobStatus.ACTIVE,
            )

            self.db.add(job_posting)
            self.db.commit()
            self.db.refresh(job_posting)

            # Increment jobs usage for organizations
            try:
                self.subscription_service.increment_organization_jobs_usage(org_id)
            except Exception as e:
                # Log the error but don't fail the job posting creation
                print(f"Warning: Failed to increment jobs usage for organization {org_id}: {str(e)}")

            return job_posting

        except Exception as e:
            self.db.rollback()
            if isinstance(e, (OrganizationNotFoundException,)):
                raise e
            raise JobPostingCreationException(f"Failed to create job posting: {str(e)}")

    def update_job_posting(
        self, job_id: str, org_id: str, job_data: JobPostingUpdateSchema
    ) -> JobPosting:
        """Update an existing job posting. Only ACTIVE job postings can be updated."""
        try:
            job_posting = self.db.query(JobPosting).filter(
                and_(JobPosting.id == job_id, JobPosting.organization_id == org_id)
            ).first()

            if not job_posting:
                raise JobPostingNotFoundException(job_id)

            # Check if job posting is active - only active jobs can be updated
            if job_posting.status != JobStatus.ACTIVE:
                raise JobPostingClosedException(f"Cannot update closed job posting: {job_id}")

            update_data = job_data.dict(exclude_unset=True)
            if job_data.screening_questions:
                cleaned_questions = []
                for data in job_data.screening_questions:
                    cleaned_questions.append(
                        {
                            "question": data.question,
                            "type": data.type.value,
                            "required": data.required,
                            "options": data.options,
                        }
                    )

                update_data["screening_questions"] = cleaned_questions

            for field, value in update_data.items():
                setattr(job_posting, field, value)

            self.db.commit()
            self.db.refresh(job_posting)
            return job_posting

        except Exception as e:
            self.db.rollback()
            if isinstance(e, (JobPostingNotFoundException, JobPostingClosedException)):
                raise e
            raise JobPostingUpdateException(f"Failed to update job posting: {str(e)}")

    def toggle_job_posting_status(self, job_id: str, org_id: str) -> JobPosting:
        """Toggle job posting status between ACTIVE and CLOSED."""
        try:
            job_posting = self.db.query(JobPosting).filter(
                and_(JobPosting.id == job_id, JobPosting.organization_id == org_id)
            ).first()

            if not job_posting:
                raise JobPostingNotFoundException(job_id)

            # Toggle status
            if job_posting.status == JobStatus.ACTIVE:
                job_posting.status = JobStatus.CLOSED
            else:
                job_posting.status = JobStatus.ACTIVE

            self.db.commit()
            self.db.refresh(job_posting)
            return job_posting

        except Exception as e:
            self.db.rollback()
            if isinstance(e, JobPostingNotFoundException):
                raise e
            raise JobPostingUpdateException(f"Failed to toggle job posting status: {str(e)}")

    def get_job_posting(self, job_id: str, org_id: Optional[str] = None) -> dict:
        """Get a specific job posting with serialized data."""
        query = self.db.query(JobPosting).filter(JobPosting.id == job_id)

        if org_id:
            query = query.filter(JobPosting.organization_id == org_id)

        job_posting = query.first()
        if not job_posting:
            raise JobPostingNotFoundException(job_id)

        return self.serialize_job_posting(job_posting)

    def get_organization_jobs(
        self, org_id: str, status: Optional[JobStatus] = None, skip: int = 0, limit: int = 100, include_org_profile: bool = False
    ) -> dict:
        """Get all job postings for an organization with interview-style pagination."""
        query = self.db.query(JobPosting).filter(JobPosting.organization_id == org_id)

        if status:
            query = query.filter(JobPosting.status == status)

        # Order by creation date (newest first)
        query = query.order_by(JobPosting.created_at.desc())

        total = query.count()
        jobs = query.offset(skip).limit(limit).all()

        # Serialize the jobs data - use org profile serialization for public endpoints
        if include_org_profile:
            serialized_jobs = [self.serialize_job_posting_with_org_profile(job) for job in jobs]
        else:
            serialized_jobs = [self.serialize_job_posting(job) for job in jobs]

        # Calculate pagination metadata
        total_pages = math.ceil(total / limit) if limit > 0 else 0
        current_page = (skip // limit) + 1 if limit > 0 else 1
        current_page_length = len(jobs)

        return {
            "status": "success",
            "message": "Organization jobs fetched successfully",
            "data": serialized_jobs,
            "pagination": {
                "total_jobs": total,
                "total_pages": total_pages,
                "current_page": current_page,
                "current_page_length": current_page_length,
                "page_size": limit,
            },
        }

    def get_job_analytics(self, job_id: str, org_id: str) -> JobAnalyticsSchema:
        """Get analytics for a specific job posting."""
        job_posting = self.get_job_posting(job_id, org_id)

        return JobAnalyticsSchema(
            total_applications=job_posting.total_applications,
            interviews_scheduled=job_posting.interviews_scheduled,
            interviews_completed=job_posting.interviews_completed,
            pending_applications=job_posting.pending_applications,
        )

    def search_jobs(
        self, filters: JobSearchFiltersSchema, skip: int = 0, limit: int = 100
    ) -> dict:
        """Search for job postings with filters using interview-style pagination."""
        query = self.db.query(JobPosting).filter(JobPosting.status == JobStatus.ACTIVE)

        # Apply filters
        if filters.job_title:
            query = query.filter(JobPosting.job_title.ilike(f"%{filters.job_title}%"))

        if filters.location:
            query = query.filter(
                or_(
                    JobPosting.job_location.ilike(f"%{filters.location}%"),
                    JobPosting.company_location.ilike(f"%{filters.location}%")
                )
            )

        if filters.workplace_type:
            try:
                workplace_enum = WorkplaceType(filters.workplace_type.upper())
                query = query.filter(JobPosting.workplace_type == workplace_enum)
            except ValueError:
                pass  # Invalid enum value, skip filter

        if filters.employment_type:
            try:
                employment_enum = EmploymentType(filters.employment_type.upper())
                query = query.filter(JobPosting.employment_type == employment_enum)
            except ValueError:
                pass  # Invalid enum value, skip filter

        if filters.company_name:
            query = query.filter(JobPosting.company_name.ilike(f"%{filters.company_name}%"))

        if filters.skills:
            # Search for jobs that have any of the specified skills
            skill_conditions = []
            for skill in filters.skills:
                skill_conditions.append(JobPosting.required_skills.op('?')(skill))
            if skill_conditions:
                query = query.filter(or_(*skill_conditions))

        # Order by creation date (newest first)
        query = query.order_by(JobPosting.created_at.desc())

        total = query.count()
        jobs = query.offset(skip).limit(limit).all()

        # Serialize the jobs data with organization profile for browse endpoint
        serialized_jobs = [self.serialize_job_posting_with_org_profile(job) for job in jobs]

        # Calculate pagination metadata
        total_pages = math.ceil(total / limit) if limit > 0 else 0
        current_page = (skip // limit) + 1 if limit > 0 else 1
        current_page_length = len(jobs)

        return {
            "status": "success",
            "message": "Jobs search completed successfully",
            "data": serialized_jobs,
            "pagination": {
                "total_jobs": total,
                "total_pages": total_pages,
                "current_page": current_page,
                "current_page_length": current_page_length,
                "page_size": limit,
            },
        }

    def get_public_job_posting(self, job_id: str) -> dict:
        """Get a job posting for public viewing (only active jobs) with organization profile."""
        job_posting = self.db.query(JobPosting).filter(
            and_(JobPosting.id == job_id, JobPosting.status == JobStatus.ACTIVE)
        ).first()

        if not job_posting:
            raise JobPostingNotFoundException(job_id)

        return self.serialize_job_posting_with_org_profile(job_posting)

    def get_featured_jobs(self, limit: int = 10) -> List[dict]:
        """Get featured job postings (most recent active jobs) with organization profile."""
        jobs = (
            self.db.query(JobPosting)
            .filter(JobPosting.status == JobStatus.ACTIVE)
            .order_by(JobPosting.created_at.desc())
            .limit(limit)
            .all()
        )
        return [self.serialize_job_posting_with_org_profile(job) for job in jobs]

    def serialize_job_posting(self, job: JobPosting) -> dict:
        """Serialize job posting data including created_at and updated_at."""
        return {
            "id": job.id,
            "organization_id": job.organization_id,
            "job_title": job.job_title,
            "company_name": job.company_name,
            "workplace_type": job.workplace_type.value if job.workplace_type else None,
            "job_location": job.job_location,
            "employment_type": job.employment_type.value if job.employment_type else None,
            "job_role": job.job_role,
            "jd_link": job.jd_link,
            "jd_id": job.jd_id,
            "required_skills": job.required_skills or [],
            "status": job.status.value if job.status else None,
            "screening_questions": job.screening_questions,
            "company_location": job.company_location,
            "company_website": job.company_website,
            "created_at": job.created_at.isoformat() if job.created_at else None,
            "updated_at": job.updated_at.isoformat() if job.updated_at else None,
            "total_applications": job.total_applications,
            "pending_applications": job.pending_applications,
            "interviews_scheduled": job.interviews_scheduled,
            "interviews_completed": job.interviews_completed,
        }

    def serialize_job_posting_with_org_profile(self, job: JobPosting) -> dict:
        """Serialize job posting data with organization profile information for browse endpoint."""
        # Get organization profile information
        organization = self.db.query(Organization).filter(Organization.id == job.organization_id).first()
        
        base_data = {
            "id": job.id,
            "organization_id": job.organization_id,
            "job_title": job.job_title,
            "company_name": job.company_name,
            "workplace_type": job.workplace_type.value if job.workplace_type else None,
            "job_location": job.job_location,
            "employment_type": job.employment_type.value if job.employment_type else None,
            "job_role": job.job_role,
            "jd_link": job.jd_link,
            "jd_id": job.jd_id,
            "required_skills": job.required_skills,
            "status": job.status.value if job.status else None,
            "screening_questions": job.screening_questions,
            "company_location": job.company_location,
            "company_website": job.company_website,
            "created_at": job.created_at.isoformat() if job.created_at else None,
            "updated_at": job.updated_at.isoformat() if job.updated_at else None,
        }
        
        # Add organization profile information
        if organization:
            base_data["organization_profile"] = {
                "name": organization.organization_name,
                "profile_image": organization.profile_picture
            }
        else:
            base_data["organization_profile"] = {
                "name": job.company_name,
                "profile_image": None
            }
        
        return base_data
