import uuid
import math
from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, desc
from app.models.job_application import JobApplication
from app.models.job_posting import JobPosting
from app.models.user import User
from app.models.interview import Interview
from app.models.enums import ApplicationStatus, JobStatus
from app.models.interview import ScheduledBy
from app.schemas.job_application_schema import (
    JobApplicationCreateSchema,
    JobApplicationUpdateSchema,
    JobApplicationStatusUpdateSchema,
)
from app.schemas.interview_schema import Interview<PERSON><PERSON>, InterviewCreateFromApplication
from app.utils.exceptions.job_exceptions import (
    JobApplicationNotFoundException,
    JobApplicationCreationException,
    JobApplicationUpdateException,
    Duplicate<PERSON>obApplicationException,
    ClosedJobApplicationException,
    UnauthorizedApplicationAccessException,
    InvalidScreeningAnswersException,
    InterviewSchedulingFromApplicationException,
    JobPostingNotFoundException,
)
from app.utils.exceptions.user_exceptions import OrganizationNotFoundException
from app.services.interview_service import InterviewService
from app.services.subscription_service import SubscriptionService
from app.utils.aws_s3 import download_file_from_s3, parse_s3_url
from app.utils.text_extractor import extract_text_from_pdf
from app.utils.exceptions.text_extraction_exceptions import PDFTextExtractionException
from app.schemas.user_schema import RegistrationType
import os


class JobApplicationService:
    def __init__(self, db: Session):
        self.db = db
        self.subscription_service = SubscriptionService(db)

    def apply_to_job(
        self, user_id: str, job_id: str, application_data: JobApplicationCreateSchema
    ) -> dict:
        """Apply to a job posting."""
        try:
            # Check subscription limits for job applications
            usage = self.subscription_service.get_subscription_usage(user_id)
            if not usage.can_apply_to_job:
                raise JobApplicationCreationException(
                    f"Job application limit exceeded. You have used {usage.jobs_used} out of {usage.jobs_limit} job applications in your {usage.plan_name}."
                )

            # Check if job posting exists and is active
            job_posting = self.db.query(JobPosting).filter(JobPosting.id == job_id).first()
            if not job_posting:
                raise JobPostingNotFoundException(job_id)

            if job_posting.status != JobStatus.ACTIVE:
                raise ClosedJobApplicationException()

            # Check if user has already applied to this job
            existing_application = self.db.query(JobApplication).filter(
                and_(
                    JobApplication.job_posting_id == job_id,
                    JobApplication.user_id == user_id
                )
            ).first()

            if existing_application:
                raise DuplicateJobApplicationException()

            # Validate screening answers if required
            if job_posting.screening_questions and application_data.screening_answers:
                self._validate_screening_answers(
                    job_posting.screening_questions,
                    application_data.screening_answers
                )

            # Create job application
            job_application = JobApplication(
                id=str(uuid.uuid4()),
                job_posting_id=job_id,
                user_id=user_id,
                cover_letter=application_data.cover_letter,
                resume_link=application_data.resume_link,
                screening_answers=[answer.dict() for answer in application_data.screening_answers] if application_data.screening_answers else None,
                status=ApplicationStatus.PENDING,
            )

            self.db.add(job_application)
            self.db.commit()
            self.db.refresh(job_application)

            # Increment jobs usage for individual users
            try:
                self.subscription_service.increment_jobs_usage(user_id)
            except Exception as e:
                # Log the error but don't fail the job application
                print(f"Warning: Failed to increment jobs usage for user {user_id}: {str(e)}")

            # Load related entities for serialization
            job_application = self.db.query(JobApplication).filter(JobApplication.id == job_application.id).options(
                joinedload(JobApplication.user), joinedload(JobApplication.job_posting)
            ).first()

            return self.serialize_job_application(job_application)

        except Exception as e:
            self.db.rollback()
            if isinstance(e, (
                JobPostingNotFoundException,
                ClosedJobApplicationException,
                DuplicateJobApplicationException,
                InvalidScreeningAnswersException,
            )):
                raise e
            raise JobApplicationCreationException(f"Failed to create job application: {str(e)}")

    def get_job_applications(
        self, job_id: str, org_id: str, skip: int = 0, limit: int = 100
    ) -> dict:
        """Get all applications for a job posting (organization access) with interview-style pagination."""
        # Verify organization owns the job posting
        job_posting = self.db.query(JobPosting).filter(
            and_(JobPosting.id == job_id, JobPosting.organization_id == org_id)
        ).first()

        if not job_posting:
            raise JobPostingNotFoundException(job_id)

        query = self.db.query(JobApplication).filter(JobApplication.job_posting_id == job_id)
        query = query.options(joinedload(JobApplication.user), joinedload(JobApplication.job_posting))
        query = query.order_by(desc(JobApplication.applied_at))

        total = query.count()
        applications = query.offset(skip).limit(limit).all()

        # Serialize the applications data
        serialized_applications = [self.serialize_job_application(application) for application in applications]

        # Calculate pagination metadata
        total_pages = math.ceil(total / limit) if limit > 0 else 0
        current_page = (skip // limit) + 1 if limit > 0 else 1
        current_page_length = len(applications)

        return {
            "status": "success",
            "message": "Applications fetched successfully",
            "data": serialized_applications,
            "pagination": {
                "total_applications": total,
                "total_pages": total_pages,
                "current_page": current_page,
                "current_page_length": current_page_length,
                "page_size": limit,
            },
        }

    def update_application_status(
        self, application_id: str, status_data: JobApplicationStatusUpdateSchema, org_id: str
    ) -> dict:
        """Update application status (organization access)."""
        try:
            # Get application and verify organization access
            application = self.db.query(JobApplication).join(JobPosting).filter(
                and_(
                    JobApplication.id == application_id,
                    JobPosting.organization_id == org_id
                )
            ).options(joinedload(JobApplication.user), joinedload(JobApplication.job_posting)).first()

            if not application:
                raise JobApplicationNotFoundException(application_id)

            application.status = status_data.status
            self.db.commit()
            self.db.refresh(application)
            return self.serialize_job_application(application)

        except Exception as e:
            self.db.rollback()
            if isinstance(e, JobApplicationNotFoundException):
                raise e
            raise JobApplicationUpdateException(f"Failed to update application status: {str(e)}")

    def get_user_applications(
        self, user_id: str, skip: int = 0, limit: int = 100
    ) -> dict:
        """Get all applications for a user with interview-style pagination."""
        query = self.db.query(JobApplication).filter(JobApplication.user_id == user_id)
        query = query.options(joinedload(JobApplication.user), joinedload(JobApplication.job_posting))
        query = query.order_by(desc(JobApplication.applied_at))

        total = query.count()
        applications = query.offset(skip).limit(limit).all()

        # Serialize the applications data
        serialized_applications = [self.serialize_job_application(application) for application in applications]

        # Calculate pagination metadata
        total_pages = math.ceil(total / limit) if limit > 0 else 0
        current_page = (skip // limit) + 1 if limit > 0 else 1
        current_page_length = len(applications)

        return {
            "status": "success",
            "message": "User applications fetched successfully",
            "data": serialized_applications,
            "pagination": {
                "total_applications": total,
                "total_pages": total_pages,
                "current_page": current_page,
                "current_page_length": current_page_length,
                "page_size": limit,
            },
        }

    def get_application(self, application_id: str, user_id: Optional[str] = None) -> JobApplication:
        """Get a specific application."""
        query = self.db.query(JobApplication).filter(JobApplication.id == application_id)

        if user_id:
            query = query.filter(JobApplication.user_id == user_id)

        application = query.first()
        if not application:
            raise JobApplicationNotFoundException(application_id)

        return application

    def get_application_details(
        self, job_id: str, application_id: str, org_id: str
    ) -> dict:
        """Get application details and auto-update status to REVIEWED if PENDING (organization access)."""
        try:
            # Verify organization owns the job posting and get application
            application = self.db.query(JobApplication).join(JobPosting).filter(
                and_(
                    JobApplication.id == application_id,
                    JobApplication.job_posting_id == job_id,
                    JobPosting.organization_id == org_id
                )
            ).options(joinedload(JobApplication.user), joinedload(JobApplication.job_posting)).first()

            if not application:
                raise JobApplicationNotFoundException(application_id)

            # Auto-update status from PENDING to REVIEWED when org views the application
            if application.status == ApplicationStatus.PENDING:
                application.status = ApplicationStatus.REVIEWED
                self.db.commit()
                self.db.refresh(application)

            return self.serialize_job_application(application)

        except Exception as e:
            self.db.rollback()
            if isinstance(e, JobApplicationNotFoundException):
                raise e
            raise JobApplicationUpdateException(f"Failed to get application details: {str(e)}")

    def update_user_application(
        self, application_id: str, user_id: str, application_data: JobApplicationUpdateSchema
    ) -> dict:
        """Update user's own application."""
        try:
            application = self.get_application(application_id, user_id)

            # Only allow updates if application is still pending (not reviewed or higher)
            if application.status != ApplicationStatus.PENDING:
                raise JobApplicationUpdateException("Cannot update application once it has been reviewed by the organization")

            # Update fields if provided
            update_data = application_data.dict(exclude_unset=True)
            if "screening_answers" in update_data and update_data["screening_answers"]:
                update_data["screening_answers"] = [answer.dict() for answer in application_data.screening_answers]

            for field, value in update_data.items():
                setattr(application, field, value)

            self.db.commit()
            self.db.refresh(application)
            
            # Load related entities for serialization
            application = self.db.query(JobApplication).filter(JobApplication.id == application.id).options(
                joinedload(JobApplication.user), joinedload(JobApplication.job_posting)
            ).first()
            
            return self.serialize_job_application(application)

        except Exception as e:
            self.db.rollback()
            if isinstance(e, (JobApplicationNotFoundException, JobApplicationUpdateException)):
                raise e
            raise JobApplicationUpdateException(f"Failed to update application: {str(e)}")

    async def schedule_interview_from_application(
        self, application_id: str, interview_data: InterviewCreateFromApplication, org_id: str
    ) -> Interview:
        """Schedule an interview from a job application.
        
        All candidate details, skill sets, job role, resume link, and JD details
        are automatically populated from the application and job posting.
        """
        try:
            # Get application and verify organization access
            application = self.db.query(JobApplication).join(JobPosting).filter(
                and_(
                    JobApplication.id == application_id,
                    JobPosting.organization_id == org_id
                )
            ).first()

            if not application:
                raise JobApplicationNotFoundException(application_id)

            # Get job posting details
            job_posting = application.job_posting
            
            # Validate that required fields exist in job posting
            if not job_posting.required_skills:
                raise InterviewSchedulingFromApplicationException("Job posting must have required skills defined")
            
            if not job_posting.job_role:
                raise InterviewSchedulingFromApplicationException("Job posting must have job role defined")
            
            if not application.resume_link:
                raise InterviewSchedulingFromApplicationException("Application must have resume link")

            # Process resume text extraction
            resume_document_text = None
            resume_local_path = None
            
            try:
                # Download and extract text from resume
                resume_s3_key = await parse_s3_url(application.resume_link)
                resume_local_path = download_file_from_s3(resume_s3_key, file_type="pdf")
                
                with open(resume_local_path, "rb") as file:
                    file_content = file.read()
                
                resume_document_text = extract_text_from_pdf(file_content)
            except Exception as e:
                if resume_local_path and os.path.exists(resume_local_path):
                    try:
                        os.remove(resume_local_path)
                    except Exception:
                        pass
                raise PDFTextExtractionException(f"Failed to extract text from resume: {str(e)}")
            finally:
                if resume_local_path and os.path.exists(resume_local_path):
                    try:
                        os.remove(resume_local_path)
                    except Exception:
                        pass

            # Process JD details and text extraction
            jd_link = None
            jd_id = None
            jd_document_text = None
            
            # Determine JD source (prefer jd_id over jd_link)
            if job_posting.jd_id:
                jd_id = job_posting.jd_id
                # Get JD text from user service (for standalone JDs)
                from app.services.user_service import UserService
                from app.db.session import SessionLocal
                user_service = UserService(SessionLocal())
                standalone_jd = user_service.get_job_description_by_entity(
                    entity_id=org_id,
                    jd_id=job_posting.jd_id,
                    entity_type=RegistrationType.organization
                )
                if standalone_jd:
                    jd_document_text = standalone_jd.get("description")
                    jd_link = standalone_jd.get("link")
            elif job_posting.jd_link:
                jd_link = job_posting.jd_link
                jd_local_path = None
                
                try:
                    # Download and extract text from JD
                    jd_s3_key = await parse_s3_url(job_posting.jd_link)
                    jd_local_path = download_file_from_s3(jd_s3_key, file_type="pdf")
                    
                    with open(jd_local_path, "rb") as file:
                        file_content = file.read()
                    
                    jd_document_text = extract_text_from_pdf(file_content)
                except Exception as e:
                    if jd_local_path and os.path.exists(jd_local_path):
                        try:
                            os.remove(jd_local_path)
                        except Exception:
                            pass
                    raise PDFTextExtractionException(f"Failed to extract text from job description: {str(e)}")
                finally:
                    if jd_local_path and os.path.exists(jd_local_path):
                        try:
                            os.remove(jd_local_path)
                        except Exception:
                            pass
            else:
                raise InterviewSchedulingFromApplicationException("Job posting must have either JD link or JD ID")

            # Create interview with all details auto-populated from application and job posting
            interview = Interview(
                id=str(uuid.uuid4()),
                organization_id=org_id,
                job_application_id=application_id,
                candidate_name=application.applicant_name,
                candidate_email=application.applicant_email,
                skill_sets=job_posting.required_skills,  # Auto-populated from job posting
                job_role=job_posting.job_role,  # Auto-populated from job posting
                experience_level=interview_data.experience_level,
                available_from=interview_data.available_from,
                available_until=interview_data.available_until,
                interview_duration=interview_data.interview_duration,
                resume_link=application.resume_link,  # Auto-populated from application
                resume_details=resume_document_text,  # Extracted text from resume
                jd_link=jd_link,  # Auto-populated from job posting
                jd_id=jd_id,  # Auto-populated from job posting
                jd_details=jd_document_text,  # Extracted text from JD
                scheduled_by_type=ScheduledBy.ORGANIZATION,
            )

            self.db.add(interview)

            # Update application status
            application.status = ApplicationStatus.INTERVIEW_SCHEDULED

            self.db.commit()
            self.db.refresh(interview)
            self.db.refresh(application)

            return interview

        except Exception as e:
            self.db.rollback()
            if isinstance(e, (JobApplicationNotFoundException, InterviewSchedulingFromApplicationException, PDFTextExtractionException)):
                raise e
            raise InterviewSchedulingFromApplicationException(f"Failed to schedule interview: {str(e)}")

    def _validate_screening_answers(self, screening_questions: List[dict], screening_answers: List[dict]) -> None:
        """Validate screening answers against questions."""
        required_questions = [q for q in screening_questions if q.get("required", False)]
        answered_questions = {answer.question for answer in screening_answers}

        # Check if all required questions are answered
        for question in required_questions:
            if question["question"] not in answered_questions:
                raise InvalidScreeningAnswersException(
                    f"Required question not answered: {question['question']}"
                )

        # Validate multiple choice answers
        for answer in screening_answers:
            question_data = next(
                (q for q in screening_questions if q["question"] == answer.question), 
                None
            )

            if question_data and question_data.get("type") == "multiple_choice":
                valid_options = question_data.get("options", [])
                if answer.answer not in valid_options:
                    raise InvalidScreeningAnswersException(
                        f"Invalid answer for question '{answer.question}'. "
                        f"Valid options: {valid_options}"
                    )

    def serialize_job_application(self, application: JobApplication) -> dict:
        """Serialize job application data including related user and job posting information."""
        return {
            "id": application.id,
            "job_posting_id": application.job_posting_id,
            "user_id": application.user_id,
            "cover_letter": application.cover_letter,
            "resume_link": application.resume_link,
            "screening_answers": application.screening_answers,
            "status": application.status.value if application.status else None,
            "applied_at": application.applied_at.isoformat() if application.applied_at else None,
            "updated_at": application.updated_at.isoformat() if application.updated_at else None,
            # Applicant information
            "applicant_name": application.applicant_name,
            "applicant_email": application.applicant_email,
            # Job information
            "job_title": application.job_title,
            "company_name": application.company_name,
            "workplace_type": application.job_posting.workplace_type.value if application.job_posting and application.job_posting.workplace_type else None,
            "job_location": application.job_posting.job_location if application.job_posting else None,
            "employment_type": application.job_posting.employment_type.value if application.job_posting and application.job_posting.employment_type else None,
        }

    def check_application_status(self, user_id: str, job_id: str) -> dict:
        """Check if a user has applied for a specific job."""
        try:
            # Check if job posting exists and is active
            job_posting = self.db.query(JobPosting).filter(JobPosting.id == job_id).first()
            if not job_posting:
                raise JobPostingNotFoundException(job_id)

            # Check if user has applied to this job
            existing_application = self.db.query(JobApplication).filter(
                and_(
                    JobApplication.job_posting_id == job_id,
                    JobApplication.user_id == user_id
                )
            ).first()

            return {
                "status": "success",
                "message": "Application status checked successfully",
                "data": {
                    "job_id": job_id,
                    "user_id": user_id,
                    "has_applied": existing_application is not None,
                    "application_id": existing_application.id if existing_application else None,
                    "application_status": existing_application.status.value if existing_application else None,
                    "applied_at": existing_application.applied_at.isoformat() if existing_application and existing_application.applied_at else None,
                    "job_title": job_posting.job_title,
                    "company_name": job_posting.company_name,
                    "job_status": job_posting.status.value
                }
            }

        except Exception as e:
            if isinstance(e, JobPostingNotFoundException):
                raise e
            raise JobApplicationCreationException(f"Failed to check application status: {str(e)}")
