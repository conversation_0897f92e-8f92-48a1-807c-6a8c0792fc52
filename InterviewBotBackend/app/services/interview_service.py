# app/services/interview_service.py
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timezone, timedelta
from uuid import uuid4
import io

from app.models.interview import Interview
from app.models.organization import Organization
from app.schemas.interview_schema import <PERSON><PERSON><PERSON>
from app.utils.exceptions.interview_exceptions import (
    InterviewNotFoundException,
    InvalidInterviewDataException,
    PastInterviewModificationException,
    InterviewCreationException,
    InterviewUpdateException,
    InterviewDeletionException,
    OrgSetupIncomplete,
    CompletedInterviewModificationException,
    InterviewStatisticsFetchError,
    NonIndividualUserAccessException,
    UnauthorizedAccessException,
    Interview<PERSON>ot<PERSON>ompletedException,
    NotAllowedToCreateInterview,
)
from app.utils.exceptions.database_exceptions import (
    DatabaseOperationException,
    DatabaseRecordNotFoundException,
)
from app.utils.exceptions.text_extraction_exceptions import PDFTextExtractionException
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import case, desc, func
from app.services.user_service import UserService

# Create an instance of UserService for getting job descriptions
from app.db.session import Session<PERSON>ocal
from math import ceil
from app.utils.aws_s3 import download_file_from_s3, parse_s3_url
from app.utils.text_extractor import extract_text_from_pdf
from app.utils.pdf_generator import generate_interview_report_pdf
import os  # Needed for file operations
from app.schemas.user_schema import RegistrationType
from app.models.interview import ScheduledBy
from app.utils.exceptions.subscription_exceptions import SubscriptionLimitExceededException
from app.services.subscription_service import SubscriptionService

class InterviewService:

    def __init__(self, db: Session):
        self.db = db
        self.user_service = UserService(SessionLocal())
        self.subscription_service = SubscriptionService(db)

    async def create_interview(self, interview_create: InterviewCreate, current_user: dict) -> dict:
        scheduler = None
        if current_user["registration_type"] == RegistrationType.individual:
            user_detail = self.user_service.get_user_by_id(current_user["user_id"])
            scheduler = ScheduledBy.USER
            interview_create.candidate_name = user_detail["full_name"]
            interview_create.candidate_email = user_detail["email"]

            # Check subscription limits for individual users
            if not self.subscription_service.check_interview_limit(current_user["user_id"]):
                usage = self.subscription_service.get_subscription_usage(current_user["user_id"])
                raise SubscriptionLimitExceededException(
                    f"Interview limit exceeded. You have used {usage.interviews_used} out of {usage.interview_limit} interviews in your {usage.plan_name}."
                )
        elif current_user["registration_type"] == RegistrationType.organization:

            if not interview_create.candidate_name or not interview_create.candidate_email:
                raise InvalidInterviewDataException("Candidate name and email are required")

            # Check subscription limits for organizations
            if not self.subscription_service.check_organization_interview_limit(current_user["user_id"]):
                usage = self.subscription_service.get_organization_subscription_usage(current_user["user_id"])
                raise SubscriptionLimitExceededException(
                    f"Interview limit exceeded. Your organization has used {usage.interviews_used} out of {usage.interview_limit} interviews in your {usage.plan_name}."
                )

            # Fetch organization details
            organization_detail = self.user_service.get_org_by_id(current_user["user_id"])

            # Check if organization setup is complete
            if not organization_detail.get("is_setup_complete", False):
                raise OrgSetupIncomplete(
                    "Organization setup is incomplete. Please complete the setup by adding skill sets and job roles."
                )

            # Handle None values for skill_sets and job_roles
            skill_sets = (
                organization_detail["skill_sets"]
                if organization_detail["skill_sets"] is not None
                else []
            )
            job_roles = (
                organization_detail["job_roles"]
                if organization_detail["job_roles"] is not None
                else []
            )

            # Validate skill sets and job role selections
            for skill in interview_create.skill_sets:
                if skill not in skill_sets:
                    raise InvalidInterviewDataException(f"Invalid skill set selection: {skill}")

            if interview_create.job_role not in job_roles:
                raise InvalidInterviewDataException("Invalid job role selection")

            # Set scheduler type
            scheduler = ScheduledBy.ORGANIZATION

        else:
            raise NotAllowedToCreateInterview()

        now = datetime.now(timezone.utc)

        # Validate available_from and available_until
        if interview_create.available_from and interview_create.available_from < now:
            raise InvalidInterviewDataException("Available from time must be in the future")

        if interview_create.available_until and interview_create.available_until < now:
            raise InvalidInterviewDataException("Available until time must be in the future")

        if (
            interview_create.available_from
            and interview_create.available_until
            and interview_create.available_from >= interview_create.available_until
        ):
            raise InvalidInterviewDataException("Available from must be before available until")

        try:
            # Process resume
            resume_s3_key = await parse_s3_url(interview_create.resume_link)
            resume_local_path = None

            try:
                # Download the file from S3 (ensuring it's a PDF)
                resume_local_path = download_file_from_s3(resume_s3_key, file_type="pdf")

                # Read the file content
                with open(resume_local_path, "rb") as file:
                    file_content = file.read()

                # Extract text from the file
                resume_document_text = extract_text_from_pdf(file_content)
            except Exception as e:
                # If there's an error, re-raise it after cleaning up
                if resume_local_path and os.path.exists(resume_local_path):
                    try:
                        os.remove(resume_local_path)
                        print(f"Successfully deleted local file after error: {resume_local_path}")
                    except Exception as delete_error:
                        print(f"Warning: Failed to delete local file {resume_local_path}: {str(delete_error)}")
                raise PDFTextExtractionException(f"Failed to extract text from resume: {str(e)}")
            finally:
                # Always delete the local file, whether extraction succeeded or failed
                if resume_local_path and os.path.exists(resume_local_path):
                    try:
                        os.remove(resume_local_path)
                        print(f"Successfully deleted local file: {resume_local_path}")
                    except Exception as e:
                        print(f"Warning: Failed to delete local file {resume_local_path}: {str(e)}")

            # Process JD - for organizations, use JD from organization profile; for individuals, use provided JD link
            jd_document_text = None
            jd_link = None

            # Get entity ID and type based on scheduler
            entity_id = organization_detail["organization_id"] if scheduler == ScheduledBy.ORGANIZATION else user_detail["user_id"]
            entity_type = RegistrationType.organization if scheduler == ScheduledBy.ORGANIZATION else RegistrationType.individual

            # Check if a standalone JD ID is provided
            if interview_create.jd_id:
                # Get the standalone job description by ID using the generic method
                standalone_jd = self.user_service.get_job_description_by_entity(
                    entity_id=entity_id,
                    jd_id=interview_create.jd_id,
                    entity_type=entity_type
                )

                if standalone_jd:
                    # Use the pre-extracted JD text from the standalone job description
                    jd_document_text = standalone_jd.get("description")
                    jd_link = standalone_jd.get("link")
                    # No need to download and extract text again as it was already extracted when setting up the JD

            # If no JD found from job description ID, use the provided JD link if available
            if not jd_document_text and interview_create.jd_link:
                jd_link = interview_create.jd_link
                jd_local_path = None

                try:
                    # Extract text from the JD file
                    jd_s3_key = await parse_s3_url(jd_link)
                    jd_local_path = download_file_from_s3(jd_s3_key, file_type="pdf")

                    with open(jd_local_path, "rb") as file:
                        file_content = file.read()

                    jd_document_text = extract_text_from_pdf(file_content)
                except Exception as e:
                    # If there's an error, re-raise it after cleaning up
                    if jd_local_path and os.path.exists(jd_local_path):
                        try:
                            os.remove(jd_local_path)
                            print(f"Successfully deleted local file after error: {jd_local_path}")
                        except Exception as delete_error:
                            print(f"Warning: Failed to delete local file {jd_local_path}: {str(delete_error)}")
                    raise PDFTextExtractionException(f"Failed to extract text from job description: {str(e)}")
                finally:
                    # Always delete the local file, whether extraction succeeded or failed
                    if jd_local_path and os.path.exists(jd_local_path):
                        try:
                            os.remove(jd_local_path)
                            print(f"Successfully deleted local file: {jd_local_path}")
                        except Exception as e:
                            print(f"Warning: Failed to delete local file {jd_local_path}: {str(e)}")

            # If neither JD ID nor JD link is provided, raise an error
            if not jd_document_text and not interview_create.jd_link and not interview_create.jd_id:
                raise InvalidInterviewDataException("Either JD link or JD ID is required")

            if scheduler == ScheduledBy.ORGANIZATION:
                new_interview = Interview(
                    id=str(uuid4()),
                    organization_id=organization_detail["organization_id"],
                    candidate_name=interview_create.candidate_name,
                    candidate_email=interview_create.candidate_email,
                    skill_sets=interview_create.skill_sets,
                    job_role=interview_create.job_role,
                    experience_level=interview_create.experience_level,
                    available_from=interview_create.available_from,
                    available_until=interview_create.available_until,
                    interview_duration=interview_create.interview_duration,
                    resume_link=interview_create.resume_link,
                    jd_link=jd_link,  # Use the potentially updated JD link
                    jd_id=interview_create.jd_id,  # Store the standalone JD ID if provided
                    resume_details=resume_document_text,
                    jd_details=jd_document_text,
                    is_completed=False,
                    scheduled_by_type=scheduler,
                )
            else:
                new_interview = Interview(
                    id=str(uuid4()),
                    scheduled_by_user_id=user_detail["user_id"],
                    candidate_name=interview_create.candidate_name,
                    candidate_email=interview_create.candidate_email,
                    skill_sets=interview_create.skill_sets,
                    job_role=interview_create.job_role,
                    experience_level=interview_create.experience_level,
                    available_from=interview_create.available_from,
                    available_until=interview_create.available_until,
                    interview_duration=interview_create.interview_duration,
                    resume_link=interview_create.resume_link,
                    jd_link=jd_link,  # Use the potentially updated JD link
                    jd_id=interview_create.jd_id,  # Store the standalone JD ID if provided
                    resume_details=resume_document_text,
                    jd_details=jd_document_text,
                    is_completed=False,
                    scheduled_by_type=scheduler,
                )

            self.db.add(new_interview)
            self.db.commit()
            self.db.refresh(new_interview)

            # Increment subscription usage when interview is scheduled
            if scheduler == ScheduledBy.ORGANIZATION:
                try:
                    self.subscription_service.increment_organization_interview_usage(current_user["user_id"])
                except Exception as e:
                    # Log the error but don't fail the interview creation
                    print(f"Warning: Failed to increment subscription usage for organization {current_user['user_id']}: {str(e)}")
            elif scheduler == ScheduledBy.USER:
                try:
                    self.subscription_service.increment_interview_usage(current_user["user_id"])
                except Exception as e:
                    # Log the error but don't fail the interview creation
                    print(f"Warning: Failed to increment subscription usage for user {current_user['user_id']}: {str(e)}")

            return {
                "status": "success",
                "message": "Interview scheduled successfully",
                "interview_id": new_interview.id,
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("creating interview")

        except InvalidInterviewDataException as e:
            raise e
        except Exception:
            raise InterviewCreationException("Failed to create interview")

    def get_interview(self, interview_id: str, current_user: dict) -> Interview:
        retrieved_by_candidate = False
        if current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
            if not interview:
                interview = self.get_interview_by_id_and_candidate(interview_id, current_user)
                retrieved_by_candidate = True
        else:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)

        if not interview:
            raise UnauthorizedAccessException(
                "Either interview does not exist or you don't have permission to access it."
            )

        return {
            "status": "success",
            "message": "Interview scheduled successfully",
            "interview": self.serialize_interview(interview, retrieved_by_candidate),
        }

    def get_interview_report(self, interview_id: str, current_user) -> Interview:

        if current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
            if not interview:
                interview = self.get_interview_by_id_and_candidate(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)

        else:
            interview = None

        if not interview:
            raise UnauthorizedAccessException(
                "Either interview does not exist or you don't have permission to access it."
            )

        if interview.is_completed == False:
            raise InterviewNotCompletedException()

        return {
            "status": "success",
            "message": "Interview report fetched successfully",
            "interview": self.serialize_interview_report(interview),
        }

    async def update_interview(
        self, interview_id: str, update_data: dict, current_user: dict
    ) -> dict:

        if current_user["registration_type"] == RegistrationType.individual:

            # Fetch the interview
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)
        else:
            raise UnauthorizedAccessException()

        # Check if the interview is completed
        now = datetime.now(timezone.utc)
        if interview.is_completed:
            raise CompletedInterviewModificationException("Cannot update completed interviews")

        # Validate available_from and available_until if provided
        if (
            "available_from" in update_data
            and update_data["available_from"] is not None
            and update_data["available_from"] < now
        ):
            raise InvalidInterviewDataException("Available from time must be in the future")

        if (
            "available_until" in update_data
            and update_data["available_until"] is not None
            and update_data["available_until"] < now
        ):
            raise InvalidInterviewDataException("Available until time must be in the future")

        # Check if both are provided or if one is being updated while the other exists
        available_from = update_data.get("available_from", interview.available_from)
        available_until = update_data.get("available_until", interview.available_until)

        if available_from and available_until and available_from >= available_until:
            raise InvalidInterviewDataException("Available from must be before available until")

        # Restrict updates to only allowed fields
        allowed_fields = {
            "available_from",
            "available_until",
            "interview_duration",
            "interview_agenda",
            "interview_questions",
        }
        update_data = {
            k: v for k, v in update_data.items() if k in allowed_fields and v is not None
        }

        if not update_data:
            raise InvalidInterviewDataException("No valid fields provided for update")

        try:
            # Map schema field names to model field names
            field_mapping = {
                "interview_agenda": "agenda",
                "interview_questions": "questions"
            }
            
            # Update fields with proper mapping
            for key, value in update_data.items():
                # Use mapped field name if it exists, otherwise use original key
                model_field = field_mapping.get(key, key)
                setattr(interview, model_field, value)

            # Update the updated_at timestamp
            interview.updated_at = datetime.now(timezone.utc)
            self.db.commit()
            self.db.refresh(interview)

            return {
                "status": "success",
                "message": "Interview updated successfully",
                **self.serialize_interview(interview),
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("updating interview")
        except Exception:
            raise InterviewUpdateException("Failed to update interview")

    def delete_interview(self, interview_id: str, current_user: dict) -> dict:
        if current_user["registration_type"] == RegistrationType.individual:

            # Fetch the interview
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)
        else:
            raise UnauthorizedAccessException()

        now = datetime.now(timezone.utc)

        if interview.is_completed:
            raise CompletedInterviewModificationException("Cannot delete a completed interview")

        # Check if the interview's availability window has passed
        if interview.available_until and interview.available_until < now:
            raise PastInterviewModificationException(
                "Cannot delete an interview with a past availability window"
            )

        try:
            self.db.delete(interview)
            self.db.commit()
            return {
                "status": "success",
                "message": "Interview deleted successfully",
                "interview_id": interview_id,
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("deleting interview")
        except Exception:
            raise InterviewDeletionException("Failed to delete interview")

    def get_all_interviews(
        self, skip: int, limit: int, completed: Optional[bool], current_user: dict
    ) -> dict:
        """
        Get all interviews sorted in the following order:
        1. Upcoming interviews first (sorted by available_from in ascending order - soonest first)
        2. Past interviews next (sorted by available_until in descending order - most recent first)

        For example, if today is May 11 and there are:
        - Upcoming interviews on May 13, 15, 16, and 20
        - Past interviews from May 1, 5, 3, and 8
        They will be returned in order: May 13, 15, 16, 20, 8, 5, 3, 1

        Args:
            skip: Number of records to skip for pagination
            limit: Maximum number of records to return
            completed: Optional filter for completion status
            current_user: Current authenticated user or organization

        Returns:
            Dictionary with interview data and pagination metadata
        """
        if current_user["registration_type"] == RegistrationType.individual:
            query = self.db.query(Interview).filter(
                Interview.scheduled_by_user_id == current_user["user_id"]
            )
        elif current_user["registration_type"] == RegistrationType.organization:
            query = self.db.query(Interview).filter(
                Interview.organization_id == current_user["user_id"]
            )
        else:
            raise UnauthorizedAccessException()

        # Apply the completed filter if provided
        if completed is not None:
            query = query.filter(Interview.is_completed == completed)

        # Get the total number of matching interviews (total items)
        total_interviews = query.count()

        # Fetch all interviews and sort them in memory
        # This approach avoids complex SQL sorting that might not be compatible with all database backends
        all_interviews = query.all()

        # Get current time for comparison
        now = datetime.now(timezone.utc)

        # Separate upcoming and past interviews
        upcoming_interviews = []
        past_interviews = []

        for interview in all_interviews:
            if interview.available_until and interview.available_until >= now:
                upcoming_interviews.append(interview)
            else:
                past_interviews.append(interview)

        # Sort upcoming interviews by available_from (soonest first)
        upcoming_interviews.sort(key=lambda x: x.available_from if x.available_from else datetime.max.replace(tzinfo=timezone.utc))

        # Sort past interviews by available_until (most recent first)
        past_interviews.sort(key=lambda x: x.available_until if x.available_until else datetime.min.replace(tzinfo=timezone.utc), reverse=True)

        # Combine the sorted lists: upcoming first, then past
        sorted_interviews = upcoming_interviews + past_interviews

        # Apply pagination
        paginated_interviews = sorted_interviews[skip:skip+limit] if limit else sorted_interviews[skip:]

        # Calculate pagination metadata
        total_pages = (
            ceil(total_interviews / limit) if limit > 0 else 0
        )  # Total pages based on limit
        current_page = (
            (skip // limit) + 1 if limit > 0 else 1
        )  # Current page number (1-based indexing)
        current_page_length = len(paginated_interviews)  # Number of items in the current page

        # Return the response with pagination metadata
        return {
            "status": "success",
            "message": "Interviews fetched successfully",
            "data": [self.serialize_interview(i) for i in paginated_interviews],
            "pagination": {
                "total_interviews": total_interviews,
                "total_pages": total_pages,
                "current_page": current_page,
                "current_page_length": current_page_length,
                "page_size": limit,
            },
        }

    def get_upcoming_interviews(
        self, skip: int, limit: int, completed: Optional[bool], current_org: Organization
    ) -> dict:
        """
        Get upcoming interviews sorted by the soonest date first.

        This method retrieves interviews where:
        1. The available_until time is in the future
        2. Sorted by available_from in ascending order (soonest first)

        For example, if today is May 11 and there are interviews on May 15, 16, 13, and 20,
        they will be returned in order: May 13, 15, 16, 20.

        Args:
            skip: Number of records to skip for pagination
            limit: Maximum number of records to return
            completed: Optional filter for completion status
            current_org: Current authenticated user or organization

        Returns:
            Dictionary with interview data and pagination metadata
        """
        now = datetime.now(timezone.utc)

        if current_org["registration_type"] == RegistrationType.individual:
            query = self.db.query(Interview).filter(
                Interview.scheduled_by_user_id == current_org["user_id"],
                Interview.available_until >= now,
            )
        elif current_org["registration_type"] == RegistrationType.organization:
            query = self.db.query(Interview).filter(
                Interview.organization_id == current_org["user_id"],
                Interview.available_until >= now,
            )
        else:
            raise UnauthorizedAccessException()

        # Apply the completed filter if provided
        if completed is not None:
            query = query.filter(Interview.is_completed == completed)

        # Get the total number of matching interviews (total items)
        total_interviews = query.count()

        # Fetch the paginated interviews
        # Sort by available_from in ascending order to get soonest interviews first
        interviews = query.order_by(Interview.available_from.asc()).offset(skip).limit(limit).all()

        # Calculate pagination metadata
        total_pages = (
            ceil(total_interviews / limit) if limit > 0 else 0
        )  # Total pages based on limit
        current_page = (
            (skip // limit) + 1 if limit > 0 else 1
        )  # Current page number (1-based indexing)
        current_page_length = len(interviews)  # Number of items in the current page

        # Return the response with pagination metadata
        return {
            "status": "success",
            "message": "Upcoming Interviews fetched successfully",
            "data": [self.serialize_interview(i) for i in interviews],
            "pagination": {
                "total_interviews": total_interviews,
                "total_pages": total_pages,
                "current_page": current_page,
                "current_page_length": current_page_length,
                "page_size": limit,
            },
        }

    def get_past_interviews(
        self, skip: int, limit: int, completed: Optional[bool], current_user: Organization
    ) -> dict:
        """
        Get past interviews sorted by the most recent date first.

        This method retrieves interviews where:
        1. The available_until time is in the past
        2. Sorted by available_until in descending order (most recent first)

        For example, if today is May 11 and there are past interviews from May 1, 5, 3, and 8,
        they will be returned in order: May 8, 5, 3, 1.

        Args:
            skip: Number of records to skip for pagination
            limit: Maximum number of records to return
            completed: Optional filter for completion status
            current_user: Current authenticated user or organization

        Returns:
            Dictionary with interview data and pagination metadata
        """
        now = datetime.now(timezone.utc)

        if current_user["registration_type"] == RegistrationType.individual:
            query = self.db.query(Interview).filter(
                Interview.scheduled_by_user_id == current_user["user_id"],
                Interview.available_until < now,
            )
        elif current_user["registration_type"] == RegistrationType.organization:
            query = self.db.query(Interview).filter(
                Interview.organization_id == current_user["user_id"],
                Interview.available_until < now,
            )
        else:
            raise UnauthorizedAccessException()

        # Apply the completed filter if provided
        if completed is not None:
            query = query.filter(Interview.is_completed == completed)

        # Get the total number of matching interviews (total items)
        total_interviews = query.count()

        # Fetch the paginated interviews
        # Sort by available_until in descending order to get most recent interviews first
        interviews = (
            query.order_by(Interview.available_until.desc()).offset(skip).limit(limit).all()
        )

        # Calculate pagination metadata
        total_pages = (
            ceil(total_interviews / limit) if limit > 0 else 0
        )  # Total pages based on limit
        current_page = (
            (skip // limit) + 1 if limit > 0 else 1
        )  # Current page number (1-based indexing)
        current_page_length = len(interviews)  # Number of items in the current page

        # Return the response with pagination metadata
        return {
            "status": "success",
            "message": "Past Interviews fetched successfully",
            "data": [self.serialize_interview(i) for i in interviews],
            "pagination": {
                "total_interviews": total_interviews,
                "total_pages": total_pages,
                "current_page": current_page,
                "current_page_length": current_page_length,
                "page_size": limit,
            },
        }

    def mark_interview_completed(self, interview_id: str, current_user: dict) -> dict:

        if current_user["registration_type"] == RegistrationType.admin:
            interview = self.get_interview_by_id(interview_id)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)

        elif current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)

        if not interview:
            raise UnauthorizedAccessException(
                "Either interview does not exist or you don't have permission to access it."
            )

        interview.is_completed = True
        interview.updated_at = datetime.now(timezone.utc)

        try:
            self.db.commit()
            self.db.refresh(interview)
            # Increment subscription usage for individual users who scheduled the interview
            # Note: For organizations, usage is incremented at scheduling time, not completion time
            if (interview.scheduled_by_type == ScheduledBy.USER and
                interview.scheduled_by_user_id):
                try:
                    self.subscription_service.increment_interview_usage(interview.scheduled_by_user_id)
                except Exception as e:
                    # Log the error but don't fail the completion
                    print(f"Warning: Failed to increment subscription usage for user {interview.scheduled_by_user_id}: {str(e)}")
            return {
                "status": "success",
                "message": "Interview marked completed successfully",
                **self.serialize_interview(interview),
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("marking interview as completed")
        except Exception:
            raise InterviewUpdateException("Failed to mark interview as completed")

    def update_interview_transcript_and_violation(
        self, interview_id: str, transcript: str, violation: str, current_user: dict
    ):
        """
        Update only the transcript of an existing interview.

        Args:
            interview_id: The ID of the interview to update
            transcript: The new transcript text
            violation: The new violation text
            organization: The current authenticated organization

        Returns:
            dict: Response with updated interview details

        Raises:
            HTTPException: If interview doesn't exist or other validation errors
        """
        # Verify the interview exists and belongs to this organization
        if current_user["registration_type"] == RegistrationType.admin:
            interview = self.get_interview_by_id(interview_id)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)

        elif current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)

        if not interview:
            raise UnauthorizedAccessException(
                "Either interview does not exist or you don't have permission to access it."
            )

        # Update only the transcript field
        interview.interview_transcript = transcript
        interview.violations = violation
        try:
            self.db.commit()
            self.db.refresh(interview)
            # Return a simplified response
            return {
                "status": "success",
                "message": "Interview transcript and violation updated successfully",
                "interview_id": interview_id,
                "interview_transcript": transcript,
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("updating interview transcript and violation")
        except Exception:
            raise InterviewUpdateException("Failed to update transcript and violation")

    def get_interview_by_id_and_org(self, interview_id, current_org):
        interview = (
            self.db.query(Interview)
            .filter(
                Interview.id == interview_id, Interview.organization_id == current_org["user_id"]
            )
            .first()
        )
        return interview

    def get_interview_by_id_and_individual(self, interview_id, current_user):
        interview = (
            self.db.query(Interview)
            .filter(
                Interview.id == interview_id,
                Interview.scheduled_by_user_id == current_user["user_id"],
            )
            .first()
        )
        return interview

    def get_interview_by_id_and_candidate(self, interview_id, current_user):
        interview = (
            self.db.query(Interview)
            .filter(
                Interview.id == interview_id, Interview.candidate_email == current_user["email"]
            )
            .first()
        )
        return interview

    def get_interview_by_id(self, interview_id: str):
        """
        Fetch an interview by its ID, regardless of organization.
        """
        interview = self.db.query(Interview).filter(Interview.id == interview_id).first()
        return interview

    def update_candidate_suitability(
        self, interview_id: str, suitability_analysis: str, current_user: dict
    ) -> dict:
        """
        Updates the candidate_suitability field of an interview.

        Args:
            interview_id: ID of the interview to update
            suitability_analysis: The text to set in candidate_suitability
            current_org: The organization making the update request

        Returns:
            dict: A success response with updated interview data
        """

        if current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id)
        else:
            interview = None

        if not interview:
            raise UnauthorizedAccessException(
                "Either interview does not exist or you don't have permission to access it."
            )
        try:
            interview.candidate_suitability = suitability_analysis
            interview.updated_at = datetime.now(timezone.utc)

            self.db.commit()
            self.db.refresh(interview)

            return {
                "status": "success",
                "message": "Candidate suitability updated successfully",
                **self.serialize_interview(interview),
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("updating candidate suitability")
        except Exception:
            raise InterviewUpdateException("Failed to update candidate suitability")

    def update_interview_agenda(self, interview_id: str, agenda: list, current_user: dict) -> dict:
        """
        Updates the agenda for a given interview.

        Args:
            interview_id: ID of the interview to update
            agenda: A list of agenda items (e.g., ["Intro", "Tech", "Behavioral"])
            current_org: The organization making the update request

        Returns:
            dict: A success response with updated interview data
        """

        if current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)
        else:
            interview = None

        if not interview:
            raise UnauthorizedAccessException(
                "Either interview does not exist or you don't have permission to access it."
            )
        try:
            interview.agenda = agenda
            interview.updated_at = datetime.now(timezone.utc)

            self.db.commit()
            self.db.refresh(interview)

            return {
                "status": "success",
                "message": "Interview agenda updated successfully",
                **self.serialize_interview(interview),
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("updating interview agenda")
        except Exception:
            raise InterviewUpdateException("Failed to update interview agenda")

    def update_interview_questions(
        self, interview_id: str, questions: list, current_user: dict
    ) -> dict:
        """
        Updates the structured questions for a given interview.

        Args:
            interview_id: ID of the interview to update
            questions: A list of structured questions (e.g., [{"agenda": "Tech", "questions": ["Q1", "Q2"]}, ...])
            current_org: The organization making the update request

        Returns:
            dict: A success response with updated interview data
        """

        if current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)
        else:
            interview = None

        if not interview:
            raise UnauthorizedAccessException(
                "Either interview does not exist or you don't have permission to access it."
            )
        try:
            interview.questions = questions
            interview.updated_at = datetime.now(timezone.utc)

            self.db.commit()
            self.db.refresh(interview)

            return {
                "status": "success",
                "message": "Interview questions updated successfully",
                **self.serialize_interview(interview),
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("updating interview questions")
        except Exception:
            raise InterviewUpdateException("Failed to update interview questions")

    def update_interview_analysis_results(
        self,
        current_user: dict,
        interview_id: str,
        verification_result: dict = None,
        feedback_result: dict = None,
        technical_evaluation_result: dict = None,
        recommendation_result: dict = None,
        interview_summary_result: dict = None,
        violation_result: dict = None,
    ) -> dict:
        """
        Updates the analysis result fields of an interview with AI agent outputs.

        Args:
            interview_id: ID of the interview to update
            verification_result: The VerificationResponse data to store
            feedback_result: The FeedbackResponse data to store
            technical_evaluation_result: The TechnicalScoreResponse data to store
            recommendation_result: The RecommendationResponse data to store
            interview_summary_result: The InterviewSummaryResponse data to store
            current_org: The organization making the update request

        Returns:
            dict: A success response with updated interview data
        """

        if current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.admin:
            interview = self.get_interview_by_id(interview_id)
        else:
            interview = None

        if not interview:
            raise InterviewNotFoundException("Interview not found")

        try:

            if verification_result is not None:
                interview.verification_result = verification_result

                # Calculate verification statistics per agenda
                verification_stats = []

                if "agendas" in verification_result:
                    for agenda in verification_result["agendas"]:
                        agenda_name = agenda.get("agenda", "")
                        questions = agenda.get("questions", [])
                        total_questions = len(questions)
                        answered_questions = sum(
                            1 for q in questions if q.get("status") == "answered"
                        )
                        unanswered_questions = total_questions - answered_questions

                        # Create a stats object for this agenda
                        agenda_stats = {
                            "agenda": agenda_name,
                            "total_questions": total_questions,
                            "answered_questions": answered_questions,
                            "unanswered_questions": unanswered_questions,
                            "completion_percentage": (
                                round((answered_questions / total_questions * 100), 2)
                                if total_questions > 0
                                else 0
                            ),
                        }
                        verification_stats.append(agenda_stats)

                    # Add overall statistics
                    all_questions = [
                        q
                        for agenda in verification_result["agendas"]
                        for q in agenda.get("questions", [])
                    ]
                    total_all = len(all_questions)
                    answered_all = sum(1 for q in all_questions if q.get("status") == "answered")
                    unanswered_all = total_all - answered_all

                    overall_stats = {
                        "agenda": "Overall",
                        "total_questions": total_all,
                        "answered_questions": answered_all,
                        "unanswered_questions": unanswered_all,
                        "completion_percentage": (
                            round((answered_all / total_all * 100), 2) if total_all > 0 else 0
                        ),
                    }
                    verification_stats.append(overall_stats)

                    # Store the statistics
                    interview.verification_stats = verification_stats

            if feedback_result is not None:
                interview.feedback_result = feedback_result

            if technical_evaluation_result is not None:
                interview.technical_evaluation_result = technical_evaluation_result

            if recommendation_result is not None:
                interview.recommendation_result = recommendation_result

            if interview_summary_result is not None:
                interview.interview_summary_result = interview_summary_result

            if violation_result is not None:
                interview.interview_violation_result = violation_result

            interview.updated_at = datetime.now(timezone.utc)

            self.db.commit()
            self.db.refresh(interview)

            return {
                "status": "success",
                "message": "Interview analysis results updated successfully",
                **self.serialize_interview(interview),
            }
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException("updating interview analysis results")
        except Exception:
            raise InterviewUpdateException("Failed to update interview analysis results")

    async def get_candidate_all_interviews(self, current_user: dict, skip, limit) -> dict:
        """
        Get a list of all interviews associated with a specific candidate user.

        Interviews are sorted in the following order:
        1. Upcoming interviews first (sorted by available_from in ascending order - soonest first)
        2. Past interviews next (sorted by available_until in descending order - most recent first)

        For example, if today is May 11 and there are:
        - Upcoming interviews on May 13, 15, 16, and 20
        - Past interviews from May 1, 5, 3, and 8
        They will be returned in order: May 13, 15, 16, 20, 8, 5, 3, 1

        Args:
            current_user: Dictionary containing user information (must include 'email' and 'registration_type').
            skip: Number of records to skip for pagination
            limit: Maximum number of records to return

        Returns:
            dict: A dictionary containing the status, a message, the total count,
                  and a list of all interviews with their details.
        """
        # 1) Validate user type
        if current_user.get("registration_type") != "individual":
            raise NonIndividualUserAccessException()

        candidate_email = current_user["email"]
        now = datetime.now(timezone.utc)

        # 2) Create base query
        base_q = self.db.query(Interview).filter(func.lower(Interview.candidate_email) == func.lower(candidate_email))

        # 3) Total count
        total_count = base_q.count()

        # 4) Fetch all interviews and sort them in memory
        # This approach avoids complex SQL sorting that might not be compatible with all database backends
        all_interviews = base_q.all()

        # Separate upcoming and past interviews
        upcoming_interviews = []
        past_interviews = []

        for interview in all_interviews:
            if interview.available_until and interview.available_until >= now:
                upcoming_interviews.append(interview)
            else:
                past_interviews.append(interview)

        # Sort upcoming interviews by available_from (soonest first)
        upcoming_interviews.sort(key=lambda x: x.available_from if x.available_from else datetime.max.replace(tzinfo=timezone.utc))

        # Sort past interviews by available_until (most recent first)
        past_interviews.sort(key=lambda x: x.available_until if x.available_until else datetime.min.replace(tzinfo=timezone.utc), reverse=True)

        # Combine the sorted lists: upcoming first, then past
        sorted_interviews = upcoming_interviews + past_interviews

        # Apply pagination
        paginated_interviews = sorted_interviews[skip:skip+limit] if limit else sorted_interviews[skip:]

        # 5) Build details list
        all_interviews_list = []
        for interview in paginated_interviews:
            try:
                if interview.scheduled_by_type == ScheduledBy.ORGANIZATION:
                    org = self.user_service.get_org_by_id(interview.organization_id)
                    org_name = org.get("organization_name", "Unknown Organization")
                elif interview.scheduled_by_type == ScheduledBy.USER:
                    org = self.user_service.get_user_by_id(interview.scheduled_by_user_id)
                    org_name = org.get("full_name", "Unknown User")
            except Exception:
                # Handle errors gracefully without exposing details
                org_name = "Unknown Organization"

            all_interviews_list.append(
                {
                    "interview_id": interview.id,
                    "organization_id": interview.organization_id,
                    "organization_name": org_name,
                    "skill_sets": interview.skill_sets,
                    "job_role": interview.job_role,
                    "available_from": (
                        interview.available_from.isoformat() if interview.available_from else None
                    ),
                    "available_until": (
                        interview.available_until.isoformat() if interview.available_until else None
                    ),
                    "interview_duration": interview.interview_duration,
                    "is_completed": interview.is_completed,
                    "created_at": (
                        interview.created_at.isoformat() if interview.created_at else None
                    ),
                }
            )

        # 6) Pagination metadata
        total_pages = ceil(total_count / limit) if limit else 0
        current_page = (skip // limit) + 1 if limit else 1
        current_page_length = len(all_interviews_list)

        # 7) Final response
        return {
            "status": "success",
            "message": "Candidate interviews fetched successfully.",
            "total_count": total_count,
            "interviews": all_interviews_list,
            "pagination": {
                "total_pages": total_pages,
                "current_page": current_page,
                "current_page_length": current_page_length,
                "page_size": limit,
            },
        }

    async def get_interview_statistics(self, current_user: dict) -> dict:
        """
        Get statistics about interviews scheduled by skills, job roles, and time periods.
        Optimized to reduce database queries.

        Args:
            current_org: The organization for which to get statistics

        Returns:
            A dictionary containing:
            - interviews_by_job_role: Count of interviews for each job role
            - interviews_by_skill: Percentage of total interviews for each skill
            - time_based_statistics: Interview counts by different time periods
        """

        if current_user["registration_type"] != RegistrationType.organization:
            skill_sets = []
            job_roles = []
            # Fetch ALL interviews in a single query then process in memory
            # This is more efficient than making many small queries
            all_interviews = (
                    self.db.query(Interview)
                    .filter(Interview.scheduled_by_user_id == current_user["user_id"])
                    .all()
                )
        elif current_user["registration_type"] == RegistrationType.organization:
            # Get organization skill sets and job roles (single query)
            organization_detail = self.user_service.get_org_by_id(current_user["user_id"])
            skill_sets = organization_detail.get("skill_sets", []) or []
            job_roles = organization_detail.get("job_roles", []) or []

            # Fetch ALL interviews in a single query then process in memory
            # This is more efficient than making many small queries
            all_interviews = (
                    self.db.query(Interview)
                    .filter(Interview.organization_id == current_user["user_id"])
                    .all()
                )
        else:
            raise UnauthorizedAccessException()
        try:
            # Reference date (today)
            now = datetime.now(timezone.utc)
            today_start = datetime(now.year, now.month, now.day, tzinfo=timezone.utc)

            total_interviews = len(all_interviews)

            # PROCESSING SECTION: Do all calculations in memory with the fetched data

            # 1. Process skill and job role statistics
            job_role_counts = {job_role: 0 for job_role in job_roles}
            skill_counts = {skill: 0 for skill in skill_sets}

            for interview in all_interviews:
                # Update job role counts
                if interview.job_role in job_role_counts:
                    job_role_counts[interview.job_role] += 1

                # Update skill counts - handle multiple skills per interview
                if interview.skill_sets:
                    # skill_sets is stored as JSON array, so iterate through each skill
                    interview_skills = interview.skill_sets if isinstance(interview.skill_sets, list) else [interview.skill_sets]
                    for skill in interview_skills:
                        if skill in skill_counts:
                            skill_counts[skill] += 1

            # Calculate percentages for skills
            skill_percentages = {}
            for skill, count in skill_counts.items():
                percentage = (count / total_interviews * 100) if total_interviews > 0 else 0
                skill_percentages[skill] = round(percentage, 2)

            # 2. Process time-based statistics
            # Initialize dictionaries for different time periods
            daily_interviews = {
                (today_start - timedelta(days=i)).strftime("%Y-%m-%d"): 0 for i in range(7)
            }

            # Weekly stats
            current_week_start = today_start - timedelta(days=now.weekday())
            weekly_interviews = {}
            for i in range(7):
                week_start = current_week_start - timedelta(weeks=i)
                week_number = week_start.isocalendar()[1]
                weekly_interviews[f"Week {week_number}, {week_start.year}"] = 0

            # Monthly stats
            monthly_interviews = {}
            for i in range(12):
                current_month = (now.month - i - 1) % 12 + 1
                current_year = now.year - ((i + now.month) // 12)
                month_start = datetime(current_year, current_month, 1, tzinfo=timezone.utc)
                month_name = month_start.strftime("%B %Y")
                monthly_interviews[month_name] = 0

            # Yearly stats
            current_year = now.year
            yearly_interviews = {str(year): 0 for year in range(current_year, current_year - 7, -1)}
            # Process each interview exactly once to update all statistics
            for interview in all_interviews:
                if not interview.created_at:
                    continue

                interview_date = interview.created_at

                # Update daily stats (last 7 days)
                days_ago = (
                    today_start - interview_date.replace(hour=0, minute=0, second=0, microsecond=0)
                ).days
                if 0 <= days_ago < 7:
                    day_key = interview_date.strftime("%Y-%m-%d")
                    if day_key in daily_interviews:
                        daily_interviews[day_key] += 1

                # Update weekly stats
                for i in range(7):
                    week_start = current_week_start - timedelta(weeks=i)
                    week_end = week_start + timedelta(weeks=1)
                    if week_start <= interview_date < week_end:
                        week_number = week_start.isocalendar()[1]
                        week_key = f"Week {week_number}, {week_start.year}"
                        if week_key in weekly_interviews:
                            weekly_interviews[week_key] += 1

                # Update monthly stats
                interview_month = interview_date.strftime("%B %Y")
                if interview_month in monthly_interviews:
                    monthly_interviews[interview_month] += 1

                # Update yearly stats
                interview_year = str(interview_date.year)
                if interview_year in yearly_interviews:

                    yearly_interviews[interview_year] += 1
            return {
                "status": "success",
                "message": "Interview statistics fetched successfully",
                "total_interviews": total_interviews,
                "interviews_by_job_role": job_role_counts,
                "interviews_by_skill": skill_percentages,
                "time_based_statistics": {
                    "daily": daily_interviews,
                    "weekly": weekly_interviews,
                    "monthly": monthly_interviews,
                    "yearly": yearly_interviews,
                },
            }
        except SQLAlchemyError:
            raise DatabaseOperationException("fetching interview statistics")
        except Exception:
            raise InterviewStatisticsFetchError("Failed to fetch interview statistics")

    async def get_candidate_interview_statistics(self, current_user: dict) -> dict:
        """
        Get interview statistics for a specific candidate user.

        Args:
            current_user: Dictionary containing user information
            db: Database session

        Returns:
            dict: A dictionary with interview statistics and details categorized by status
        """
        # Extract email from current_user
        candidate_email = current_user.get("email")
        registration_type = current_user.get("registration_type")

        # Validate the user is of type "individual"
        if registration_type != "individual":
            raise NonIndividualUserAccessException()

        now = datetime.now(timezone.utc)
        today_start = datetime(now.year, now.month, now.day, tzinfo=timezone.utc)

        # Initialize the response structure
        response = {
            "status": "success",
            "message": "Candidate interview statistics fetched successfully",
            "total_interviews": 0,
            "completed_interviews": {"count": 0, "interviews": []},
            "upcoming_interviews": {"count": 0, "interviews": []},
            "passed_without_completion": {"count": 0, "interviews": []},
            "interviews_by_skill_set": {},  # New section for skill set statistics
            "interviews_by_job_role": {},  # New section for job role statistics
            "last_seven_days": {
                (today_start - timedelta(days=i)).strftime("%Y-%m-%d"): {
                    "count": 0,
                    "interviews": [],
                }
                for i in range(7)
            },
        }

        # Get the current time for comparing interview dates
        now = datetime.now(timezone.utc)

        # Query all interviews for this candidate by email
        interviews = (
            self.db.query(Interview).filter(Interview.candidate_email == candidate_email).all()
        )

        # Update total count
        response["total_interviews"] = len(interviews)

        # Process each interview
        for interview in interviews:
            # Get organization details (assuming there's a way to get organization name)
            # This might need to be adjusted based on your actual data model
            try:
                # Ensure get_org_by_id handles cases where the org might not be found gracefully
                org_detail = self.user_service.get_org_by_id(interview.organization_id)
                org_name = org_detail.get("organization_name", "Unknown Organization")
            except Exception:
                # Handle errors gracefully without exposing details
                org_name = "Unknown Organization"

            # Prepare the common interview details
            interview_details = {
                "interview_id": interview.id,
                "organization_id": interview.organization_id,
                "organization_name": org_name,
                "skill_sets": interview.skill_sets,  # Keep as array to show all skills
                "job_role": interview.job_role,
                "available_from": (
                    interview.available_from.isoformat() if interview.available_from else None
                ),
                "available_until": (
                    interview.available_until.isoformat() if interview.available_until else None
                ),
                "interview_duration": interview.interview_duration,
            }

            interview_summary = {
                "interview_id": interview.id,
                "organization_name": org_name,
                "skill_sets": interview.skill_sets,  # Keep as array to show all skills
                "job_role": interview.job_role,
            }

            # Handle multiple skill sets per interview
            if interview.skill_sets:
                interview_skills = interview.skill_sets if isinstance(interview.skill_sets, list) else [interview.skill_sets]
                for skill_set in interview_skills:
                    if skill_set not in response["interviews_by_skill_set"]:
                        response["interviews_by_skill_set"][skill_set] = {"count": 0, "interviews": []}
                    response["interviews_by_skill_set"][skill_set]["count"] += 1
                    response["interviews_by_skill_set"][skill_set]["interviews"].append(
                        interview_summary
                    )

            # Update job role statistics
            job_role = interview.job_role
            if job_role:
                if job_role not in response["interviews_by_job_role"]:
                    response["interviews_by_job_role"][job_role] = {"count": 0, "interviews": []}
                response["interviews_by_job_role"][job_role]["count"] += 1
                response["interviews_by_job_role"][job_role]["interviews"].append(interview_summary)

            # Categorize the interview based on its status
            if interview.is_completed:
                # Completed interviews
                response["completed_interviews"]["count"] += 1
                response["completed_interviews"]["interviews"].append(interview_details)
            elif interview.available_from and interview.available_until:
                if now < interview.available_from:
                    # Upcoming interviews (future)
                    response["upcoming_interviews"]["count"] += 1
                    response["upcoming_interviews"]["interviews"].append(interview_details)
                elif now > interview.available_until and not interview.is_completed:
                    # Passed without completion
                    response["passed_without_completion"]["count"] += 1
                    response["passed_without_completion"]["interviews"].append(interview_details)
                elif interview.available_from <= now <= interview.available_until:
                    # These are "live" interviews - include in upcoming
                    response["upcoming_interviews"]["count"] += 1
                    response["upcoming_interviews"]["interviews"].append(interview_details)

            # Process for last 7 days statistics - using created_at to determine when the interview was scheduled
            if interview.created_at:
                interview_date = interview.created_at.replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                days_ago = (today_start - interview_date).days

            if 0 <= days_ago < 7:
                date_key = interview_date.strftime("%Y-%m-%d")
                response["last_seven_days"][date_key]["count"] += 1
                response["last_seven_days"][date_key]["interviews"].append(interview_summary)

        return response

    # def serialize_interview(self, interview: Interview) -> dict:
    #     now = datetime.now(timezone.utc)

    #     # Determine interview status
    #     status = "unknown"
    #     if interview.is_completed:
    #         status = "completed"
    #     elif interview.available_from and interview.available_until:
    #         if now < interview.available_from:
    #             status = "upcoming"
    #         elif interview.available_from <= now <= interview.available_until:
    #             status = "live"
    #         elif now > interview.available_until:
    #             status = "passedWithoutJoining"

    #     return {
    #         "interview_id": interview.id,
    #         "candidate_name": interview.candidate_name,
    #         "candidate_email": interview.candidate_email,
    #         "skill_set": interview.skill_sets,
    #         "job_role": interview.job_role,
    #         "experience_level": interview.experience_level,
    #         "available_from": (
    #             interview.available_from.isoformat() if interview.available_from else None
    #         ),
    #         "available_until": (
    #             interview.available_until.isoformat() if interview.available_until else None
    #         ),
    #         "interview_duration": interview.interview_duration,
    #         "candidate_suitability": interview.candidate_suitability,
    #         "agenda": interview.agenda,
    #         "questions": interview.questions,
    #         "is_completed": interview.is_completed,
    #         "status": status,
    #         "scheduled_by_type": interview.scheduled_by_type.value,
    #         "created_at": interview.created_at.isoformat() if interview.created_at else None,
    #         "updated_at": interview.updated_at.isoformat() if interview.updated_at else None,
    #     }

    def serialize_interview(
        self, interview: Interview, retrieved_by_candidate: bool = False
    ) -> dict:
        now = datetime.now(timezone.utc)

        # Determine interview status
        status = "unknown"
        if interview.is_completed:
            status = "completed"
        elif interview.available_from and interview.available_until:
            if now < interview.available_from:
                status = "upcoming"
            elif interview.available_from <= now <= interview.available_until:
                status = "live"
            elif now > interview.available_until:
                status = "passedWithoutJoining"

        candidate_profile = self.user_service.get_user_profile_by_email(interview.candidate_email)

        result = {
            "interview_id": interview.id,
            "candidate_name": interview.candidate_name,
            "candidate_email": interview.candidate_email,
            "skill_sets": interview.skill_sets,
            "job_role": interview.job_role,
            "experience_level": interview.experience_level,
            "available_from": (
                interview.available_from.isoformat() if interview.available_from else None
            ),
            "available_until": (
                interview.available_until.isoformat() if interview.available_until else None
            ),
            "interview_duration": interview.interview_duration,
            "candidate_suitability": interview.candidate_suitability,
            "is_completed": interview.is_completed,
            "status": status,
            "scheduled_by_type": interview.scheduled_by_type.value,
            "created_at": interview.created_at.isoformat() if interview.created_at else None,
            "updated_at": interview.updated_at.isoformat() if interview.updated_at else None,
            "candidate_profile": candidate_profile,
        }

        # Only include agenda and questions if not retrieved by candidate
        if not retrieved_by_candidate:
            result["agenda"] = interview.agenda
            result["questions"] = interview.questions

        return result

    def serialize_interview_report(self, interview: Interview) -> dict:
        now = datetime.now(timezone.utc)

        # Determine interview status
        status = "unknown"
        if interview.is_completed:
            status = "completed"
        elif interview.available_from and interview.available_until:
            if now < interview.available_from:
                status = "upcoming"
            elif interview.available_from <= now <= interview.available_until:
                status = "live"
            elif now > interview.available_until:
                status = "passedWithoutJoining"

        return {
            "interview_id": interview.id,
            "candidate_name": interview.candidate_name,
            "candidate_email": interview.candidate_email,
            "agenda": interview.agenda,
            "questions": interview.questions,
            "is_completed": interview.is_completed,
            "status": status,
            "skill_sets": interview.skill_sets,
            "job_role": interview.job_role,
            "experience_level": interview.experience_level,
            "interview_duration": interview.interview_duration,
            "available_from": interview.available_from.isoformat(),
            "available_until": interview.available_until.isoformat(),
            "interview_transcript": interview.interview_transcript,
            "verification_result": interview.verification_result,
            "feedback_result": interview.feedback_result,
            "technical_evaluation_result": interview.technical_evaluation_result,
            "recommendation_result": interview.recommendation_result,
            "interview_summary_result": interview.interview_summary_result,
            "verification_stats": interview.verification_stats,
            "violation_result": interview.interview_violation_result,
            "created_at": interview.created_at.isoformat(),
            "updated_at": interview.updated_at.isoformat(),
        }

    def get_interview_report_pdf(self, interview_id: str, current_user) -> io.BytesIO:
        """
        Generate a PDF report for an interview.

        Args:
            interview_id: The ID of the interview
            current_user: The authenticated user

        Returns:
            BytesIO object containing the PDF

        Raises:
            UnauthorizedAccessException: If the user doesn't have permission to access the interview
            InterviewNotCompletedException: If the interview is not completed
        """
        # Use the same authorization logic as get_interview_report
        if current_user["registration_type"] == RegistrationType.individual:
            interview = self.get_interview_by_id_and_individual(interview_id, current_user)
            if not interview:
                interview = self.get_interview_by_id_and_candidate(interview_id, current_user)
        elif current_user["registration_type"] == RegistrationType.organization:
            interview = self.get_interview_by_id_and_org(interview_id, current_user)
        else:
            interview = None

        if not interview:
            raise UnauthorizedAccessException(
                "Either interview does not exist or you don't have permission to access it."
            )

        if interview.is_completed == False:
            raise InterviewNotCompletedException()

        # Serialize the interview data
        interview_data = self.serialize_interview_report(interview)

        # Generate the PDF
        pdf_buffer = generate_interview_report_pdf(interview_data)

        return pdf_buffer
