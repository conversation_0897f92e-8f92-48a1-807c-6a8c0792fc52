"""
Email Service for Email Verification
Handles sending OTP verification emails and welcome emails
"""

from typing import Optional, <PERSON><PERSON>
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from sqlalchemy.orm import Session
from datetime import datetime, timezone

from app.core.config import settings
from app.services.otp_service import OTPService
from app.models.otp_verification import OTPType
from app.utils.email_templates import get_otp_verification_email_template, get_welcome_email_template
from app.models.user import User
from app.models.organization import Organization

class EmailService:
    def __init__(self, db: Session):
        self.db = db
        self.otp_service = OTPService(db)

        # Email configuration
        self.mail_config = ConnectionConfig(
            MAIL_USERNAME=getattr(settings, 'MAIL_USERNAME', ''),
            MAIL_PASSWORD=getattr(settings, 'MAIL_PASSWORD', ''),
            MAIL_FROM=getattr(settings, 'MAIL_FROM', ''),
            MAIL_PORT=getattr(settings, 'MAIL_PORT', 587),
            MAIL_SERVER=getattr(settings, 'MAIL_SERVER', ''),
            MAIL_FROM_NAME=getattr(settings, 'MAIL_FROM_NAME', 'EvalFast'),
            MAIL_STARTTLS=getattr(settings, 'MAIL_STARTTLS', True),
            MAIL_SSL_TLS=getattr(settings, 'MAIL_SSL_TLS', False),
            USE_CREDENTIALS=getattr(settings, 'USE_CREDENTIALS', True),
            VALIDATE_CERTS=getattr(settings, 'VALIDATE_CERTS', True)
        )
        self.fast_mail = FastMail(self.mail_config)

    async def send_verification_otp_on_signup(self, email: str, user_name: str, user_type: str = "candidate") -> dict:
        """
        Send OTP verification email during signup (automatic)
        
        Args:
            email: Email address to send OTP to
            user_name: Name of the user
            user_type: Type of user (candidate/organization)
            
        Returns:
            dict: Result of the email sending operation
        """
        try:
            # Generate OTP (force=True for signup, no cooldown)
            otp_code, message = self.otp_service.create_otp(
                email, 
                OTPType.EMAIL_VERIFICATION, 
                force=True
            )

            if not otp_code:
                raise Exception(str(message))

            # Get email template with OTP
            email_html = get_otp_verification_email_template(user_name, otp_code, user_type)

            # Create message
            message_obj = MessageSchema(
                subject="🔐 Verify Your Email - EvalFast Registration",
                recipients=[email],
                body=email_html,
                subtype="html"
            )

            # Send email
            await self.fast_mail.send_message(message_obj)

            return {
                "success": True,
                "message": "Verification email sent successfully",
                "email": email,
                "expires_in_minutes": self.otp_service.otp_expiry_minutes
            }

        except Exception as e:
            raise e

    async def resend_verification_otp(self, email: str, user_name: str, user_type: str = "candidate") -> dict:
        """
        Resend OTP verification email (with cooldown)
        
        Args:
            email: Email address
            user_name: Name of the user
            user_type: Type of user
            
        Returns:
            dict: Result of the resend operation
        """
        try:
            # Generate OTP (force=False for resend, respect cooldown)
            otp_code, message = self.otp_service.create_otp(
                email, 
                OTPType.EMAIL_VERIFICATION, 
                force=False
            )

            if not otp_code:
                raise Exception(str(message))
            
            # Get email template with OTP
            email_html = get_otp_verification_email_template(user_name, otp_code, user_type)

            # Create message
            message_obj = MessageSchema(
                subject="🔐 Resend: Verify Your Email - EvalFast",
                recipients=[email],
                body=email_html,
                subtype="html"
            )

            # Send email
            await self.fast_mail.send_message(message_obj)

            return {
                "success": True,
                "message": "Verification email resent successfully",
                "email": email,
                "expires_in_minutes": self.otp_service.otp_expiry_minutes
            }

        except Exception as e:
            raise e

    def verify_email_otp(self, email: str, otp_code: str) -> dict:
        """
        Verify email OTP code and update user verification status
        
        Args:
            email: Email address
            otp_code: OTP code to verify
            
        Returns:
            dict: Verification result
        """
        try:
            # Verify OTP
            is_valid, message = self.otp_service.verify_otp(email, otp_code, OTPType.EMAIL_VERIFICATION)

            if is_valid:
                return True
            else:
                raise Exception(str(message))

        except Exception as e:
            raise Exception(str(
                f"Verification failed: {str(e)}",
            ))

    async def send_welcome_email(self, email: str, user_name: str, user_type: str = "candidate") -> dict:
        """
        Send welcome email after successful verification
        
        Args:
            email: Email address
            user_name: Name of the user
            user_type: Type of user
            
        Returns:
            dict: Result of the email sending operation
        """
        try:
            # Get welcome email template
            email_html = get_welcome_email_template(user_name, user_type)

            # Create message
            message_obj = MessageSchema(
                subject="🎉 Welcome to EvalFast! Your Account is Ready",
                recipients=[email],
                body=email_html,
                subtype="html"
            )

            # Send email
            await self.fast_mail.send_message(message_obj)

            return {
                "success": True,
                "message": "Welcome email sent successfully",
                "email": email
            }

        except Exception as e:
            raise Exception(str(f"Failed to send welcome email: {str(e)}"))

    def get_verification_status(self, email: str) -> dict:
        """
        Get email verification status for a given email address
        
        Args:
            email: Email address to check
            
        Returns:
            dict: Verification status information
        """
        try:
            # Check user verification status
            user = self.db.query(User).filter(User.email == email).first()
            if user:
                return {
                    "email": email,
                    "is_verified": user.is_email_verified,
                    "verified_at": user.email_verified_at,
                    "user_type": "candidate"
                }

            # Check organization verification status
            org = self.db.query(Organization).filter(Organization.email == email).first()
            if org:
                return {
                    "email": email,
                    "is_verified": org.is_email_verified,
                    "verified_at": org.email_verified_at,
                    "user_type": "organization"
                }

            return {
                "email": email,
                "is_verified": False,
                "verified_at": None,
                "user_type": None,
                "error": "Email not found in system"
            }

        except Exception as e:
            raise Exception(str(f"Failed to get verification status: {str(e)}"))
