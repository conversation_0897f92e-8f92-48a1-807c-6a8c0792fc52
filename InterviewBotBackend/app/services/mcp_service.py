# app/services/mcp_service.py
import json
from fastapi import HTT<PERSON>Ex<PERSON>, status
from app.utils.mcp_connector import with_mcp_session
from app.utils.helper import serialize_tool
from app.schemas.mcp_tool_schema import (
    ToolCallRequest,
    CandidateSuitabilityPreSchema,
    JobDescriptionSchema,
    EditJobDescriptionSchema,
    BuildResumeSchema,
)
import io
from datetime import datetime
from uuid import uuid4
from app.models.organization import Organization
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.services.interview_service import InterviewService
from app.services.subscription_service import SubscriptionService
from app.utils.pdf_generator import generate_job_description_pdf
from app.utils.aws_s3 import upload_file_to_s3
from app.utils.exceptions.mcp_exceptions import (
    MCPInvalidToolResponse,
    MCPToolExecutionException,
    MCPToolInterviewNotFound,
    MCPToolResponseParsing<PERSON>rror,
    MCP<PERSON>oolNotFound,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    MCPGeneric<PERSON>x<PERSON>,
    <PERSON>P<PERSON><PERSON><PERSON>istFailed,
)
from app.utils.exceptions.interview_exceptions import UnauthorizedAccessException

# Get organization details from the database
from app.services.user_service import UserService
from app.schemas.tools import Tools
from app.schemas.user_schema import RegistrationType


class MCPService:

    def __init__(self, db: Session):
        self.db = db
        self.interview_service = InterviewService(SessionLocal())
        self.user_service = UserService(self.db)
        self.subscription_service = SubscriptionService(self.db)

    async def get_tools(self):
        try:
            tool_details = await with_mcp_session(lambda session: session.list_tools())
            # Manually serialize each Tool object to a dictionary
            serialized_tools = [serialize_tool(tool) for tool in tool_details.tools]
            return {
                "status": "success",
                "message": "Tools retrieved successfully",
                "data": serialized_tools,
            }
        except Exception as e:
            raise MCPToolListFailed("Failed to list tools")
            # raise HTTPException(
            #     status_code=500,
            #     detail={"status": "error", "message": f"Failed to list tools: {str(e)}"},
            # )

    async def execute_tool(self, request: ToolCallRequest, current_user: dict):
        """
        Executes a tool after verifying the requesting organization owns the interview.

        Args:
            request: The ToolCallRequest containing interview_id, tool_name, and arguments.
            current_user: The authenticated user.

        Raises:
            HTTPException:
                - 404 Not Found if the interview_id does not exist.
                - 403 Forbidden if current_user does not own the interview.
                - 500 Internal Server Error if the underlying tool call fails.

        Returns:
            A dictionary representing the successful execution result.
        """
        # Check if this is a tool that doesn't require an interview
        if request.tool_name in [
            Tools.GENERATE_JOB_DESCRIPTION,
            Tools.EDIT_JOB_DESCRIPTION,
            Tools.LIST_RESUME_TEMPLATES,
            Tools.BUILD_RESUME,
        ]:
            # For job description tools, we don't need an interview
            # Both organizations and individuals can use job description tools

            # The interview will be None, but that's okay for these tools
            interview = None
        else:
            # For all other tools, we need to verify the interview exists and belongs to the user
            if current_user["registration_type"] == RegistrationType.individual:
                interview = self.interview_service.get_interview_by_id_and_individual(
                    request.interview_id, current_user
                )
            elif current_user["registration_type"] == RegistrationType.organization:
                interview = self.interview_service.get_interview_by_id_and_org(
                    request.interview_id, current_user
                )
            elif current_user["registration_type"] == RegistrationType.admin:
                interview = self.interview_service.get_interview_by_id(request.interview_id)
            else:
                interview = None

            # Handle Interview Not Found for tools that require an interview
            if not interview:
                raise MCPToolInterviewNotFound("Interview not found")

        try:
            # For tools that don't require interview details, we don't need to check interview details
            if request.tool_name not in [
                Tools.GENERATE_JOB_DESCRIPTION,
                Tools.EDIT_JOB_DESCRIPTION,
                Tools.LIST_RESUME_TEMPLATES,
                Tools.BUILD_RESUME,
            ]:
                # Check required fields for CANDIDATE_SUITABILITY
                if not interview.resume_details:
                    raise MCPMissingRequiredField(
                        "resume_details is required for Candidate Suitability analysis"
                    )
                if not interview.jd_details:
                    raise MCPMissingRequiredField(
                        "jd_details is required for Candidate Suitability analysis"
                    )

            if request.tool_name == Tools.CANDIDATE_SUITABILITY:

                arguments = {
                    "resume_details": interview.resume_details,
                    "jd_details": interview.jd_details,
                }

                # Assuming with_mcp_session handles the actual tool call asynchronously
                tool_response = await with_mcp_session(
                    lambda session: session.call_tool(request.tool_name, arguments=arguments)
                )

                # Check if tool_response content is valid before parsing
                if not tool_response.content or not hasattr(tool_response.content[0], "text"):

                    raise MCPInvalidToolResponse(
                        f"Tool '{request.tool_name}' returned invalid or empty content."
                    )

                # Parse the content from the tool response
                response_data = json.loads(tool_response.content[0].text)

                if response_data.get("is_error", False):
                    error_message = response_data.get(
                        "message", f"Tool '{request.tool_name}' returned an error"
                    )
                    raise MCPToolExecutionException(error_message)

                self.interview_service.update_candidate_suitability(
                    interview.id, response_data["suitability_analysis"], current_user
                )
            elif request.tool_name == Tools.GENERATE_INTERVIEW_AGENDA:
                if request.arguments :
                    prompt=request.arguments.get("prompt","")
                else:
                    prompt=" "
                arguments = {
                    "resume_details": interview.resume_details,
                    "jd_details": interview.jd_details,
                    "prompt": prompt,
                }

                # Assuming with_mcp_session handles the actual tool call asynchronously
                tool_response = await with_mcp_session(
                    lambda session: session.call_tool(request.tool_name, arguments=arguments)
                )

                # Check if tool_response content is valid before parsing
                if not tool_response.content or not hasattr(tool_response.content[0], "text"):

                    raise MCPInvalidToolResponse(
                        f"Tool '{request.tool_name}' returned invalid or empty content."
                    )

                # Parse the content from the tool response
                response_data = json.loads(tool_response.content[0].text)

                if response_data.get("is_error", False):
                    error_message = response_data.get(
                        "message", f"Tool '{request.tool_name}' returned an error"
                    )
                    raise MCPToolExecutionException(error_message)

                self.interview_service.update_interview_agenda(
                    interview.id, response_data["interview_agenda"], current_user
                )

            elif request.tool_name == Tools.GENERATE_QUESTIONS:

                if not interview.agenda:
                    raise MCPMissingRequiredField("agenda is required for Question generation")
                if request.arguments :
                    prompt=request.arguments.get("prompt","")
                else:
                    prompt=" "
                arguments = {
                    "resume_details": interview.resume_details,
                    "jd_details": interview.jd_details,
                    "agenda": interview.agenda,
                    "prompt": prompt ,
                }

                # Assuming with_mcp_session handles the actual tool call asynchronously
                tool_response = await with_mcp_session(
                    lambda session: session.call_tool(request.tool_name, arguments=arguments)
                )

                # Check if tool_response content is valid before parsing
                if not tool_response.content or not hasattr(tool_response.content[0], "text"):

                    raise MCPInvalidToolResponse(
                        f"Tool '{request.tool_name}' returned invalid or empty content."
                    )

                # Parse the content from the tool response
                response_data = json.loads(tool_response.content[0].text)

                if response_data.get("is_error", False):
                    error_message = response_data.get(
                        "message", f"Tool '{request.tool_name}' returned an error"
                    )
                    raise MCPToolExecutionException(error_message)

                self.interview_service.update_interview_questions(
                    interview.id, response_data["interview_questions"], current_user
                )

            elif request.tool_name == Tools.EVALUATE_CANDIDATE:

                if not interview.questions:
                    raise MCPMissingRequiredField("questions is required for feedback generation")

                if not interview.interview_transcript:
                    raise MCPMissingRequiredField(
                        "interview transcript is required for feedback generation"
                    )

                if interview.interview_summary_result:
                    raise MCPToolExecutionException("Report already generated")

                arguments = {
                    "interview_questions": interview.questions,
                    "interview_transcript": interview.interview_transcript,
                    "interview_violations": interview.violations,
                }

                # Assuming with_mcp_session handles the actual tool call asynchronously
                tool_response = await with_mcp_session(
                    lambda session: session.call_tool(request.tool_name, arguments=arguments)
                )

                # Check if tool_response content is valid before parsing
                if not tool_response.content or not hasattr(tool_response.content[0], "text"):

                    raise MCPInvalidToolResponse(
                        f"Tool '{request.tool_name}' returned invalid or empty content."
                    )

                # Parse the content from the tool response
                response_data = json.loads(tool_response.content[0].text)

                if response_data.get("is_error", False):
                    error_message = response_data.get(
                        "message", f"Tool '{request.tool_name}' returned an error"
                    )
                    raise MCPToolExecutionException(error_message)

                candidate_feedback = response_data["candidate_feedback"]

                verification_result = candidate_feedback.get("verification_result", {})
                feedback_result = candidate_feedback.get("feedback_result", {})
                technical_evaluation_result = candidate_feedback.get(
                    "technical_evaluation_result", {}
                )
                recommendation_result = candidate_feedback.get("recommendation_result", {})
                interview_summary_result = candidate_feedback.get("interview_summary_result", {})
                violation_result = candidate_feedback.get("violation_result", {})

                self.interview_service.update_interview_analysis_results(
                    current_user,
                    interview.id,
                    verification_result,
                    feedback_result,
                    technical_evaluation_result,
                    recommendation_result,
                    interview_summary_result,
                    violation_result,
                )

            elif request.tool_name == Tools.GENERATE_JOB_DESCRIPTION:
                # For job description generation, we need to extract the arguments from the request
                # This tool doesn't require an interview, so we'll use the arguments directly
                if not hasattr(request, "arguments") or not request.arguments:
                    raise MCPMissingRequiredField(
                        "Arguments are required for job description generation"
                    )

                # Extract the required fields from the arguments
                if "experience_level" not in request.arguments:
                    raise MCPMissingRequiredField(
                        "experience_level is required for job description generation"
                    )
                if "job_role" not in request.arguments:
                    raise MCPMissingRequiredField(
                        "job_role is required for job description generation"
                    )

                # Get organization details from the database
                from app.services.user_service import UserService

                user_service = UserService(self.db)
                org_details = user_service.get_org_by_id(current_user["user_id"])

                # Extract relevant organization information
                org_name = org_details.get("organization_name", "")
                org_email = org_details.get("email", "")
                org_website = org_details.get("website", "")
                org_location = org_details.get("location", "")
                org_bio = org_details.get("bio", "")

                # Create organization info string
                org_info = f"Organization Name: {org_name}\n"
                if org_email:
                    org_info += f"Email: {org_email}\n"
                if org_website:
                    org_info += f"Website: {org_website}\n"
                if org_location:
                    org_info += f"Location: {org_location}\n"
                if org_bio:
                    org_info += f"About: {org_bio}\n"

                # Social media links if available
                social_media = []
                if org_details.get("linkedin"):
                    social_media.append(f"LinkedIn: {org_details.get('linkedin')}")
                if org_details.get("twitter"):
                    social_media.append(f"Twitter: {org_details.get('twitter')}")
                if org_details.get("instagram"):
                    social_media.append(f"Instagram: {org_details.get('instagram')}")
                if org_details.get("youtube"):
                    social_media.append(f"YouTube: {org_details.get('youtube')}")
                if org_details.get("tiktok"):
                    social_media.append(f"TikTok: {org_details.get('tiktok')}")

                if social_media:
                    org_info += "Social Media:\n" + "\n".join(social_media)

                # Prepare the arguments for the tool
                arguments = {
                    "experience_level": request.arguments["experience_level"],
                    "job_role": request.arguments["job_role"],
                    "organization_info": org_info,
                }

                # Add custom_prompt if provided
                if "custom_prompt" in request.arguments and request.arguments["custom_prompt"]:
                    arguments["custom_prompt"] = request.arguments["custom_prompt"]

                # Call the tool
                tool_response = await with_mcp_session(
                    lambda session: session.call_tool(request.tool_name, arguments=arguments)
                )

                # Check if tool_response content is valid before parsing
                if not tool_response.content or not hasattr(tool_response.content[0], "text"):
                    raise MCPInvalidToolResponse(
                        f"Tool '{request.tool_name}' returned invalid or empty content."
                    )

                # Parse the content from the tool response
                response_data = json.loads(tool_response.content[0].text)

                if response_data.get("is_error", False):
                    error_message = response_data.get(
                        "message", f"Tool '{request.tool_name}' returned an error"
                    )
                    raise MCPToolExecutionException(error_message)

                # Get the job description data
                job_description_text = response_data.get("job_description", "")

                # Generate a PDF from the job description
                job_data = {
                    "job_title": request.arguments["job_role"],
                    "company_name": org_details.get("organization_name", "Company"),
                    "job_description": job_description_text,
                }

                # Generate the PDF
                pdf_buffer = generate_job_description_pdf(job_data)

                # Create a unique S3 key for the PDF
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                user_id = current_user["user_id"]
                filename = f"{request.arguments['job_role'].replace(' ', '_')}_{timestamp}.pdf"
                s3_key = f"job_descriptions/{user_id}/{timestamp}_{uuid4().hex[:8]}_{filename}"

                # Upload the PDF to S3 (no local file to delete in this case since we're using a buffer)
                s3_url = upload_file_to_s3(pdf_buffer, s3_key, content_type="application/pdf")

                # Return the job description and PDF URL
                result = {
                    "job_description": response_data.get("job_description", ""),
                    "pdf_url": s3_url,
                }
                response_data = result

            elif request.tool_name == Tools.EDIT_JOB_DESCRIPTION:
                # For job description editing, we need to extract the arguments from the request
                # This tool doesn't require an interview, so we'll use the arguments directly
                if not hasattr(request, "arguments") or not request.arguments:
                    raise MCPMissingRequiredField(
                        "Arguments are required for job description editing"
                    )

                # Extract the required fields from the arguments
                if "existing_job_description" not in request.arguments:
                    raise MCPMissingRequiredField(
                        "existing_job_description is required for job description editing"
                    )
                if "custom_prompt" not in request.arguments:
                    raise MCPMissingRequiredField(
                        "custom_prompt is required for job description editing"
                    )

                # Get organization details from the database
                from app.services.user_service import UserService

                user_service = UserService(self.db)
                org_details = user_service.get_org_by_id(current_user["user_id"])

                # Extract relevant organization information
                org_name = org_details.get("organization_name", "")
                org_email = org_details.get("email", "")
                org_website = org_details.get("website", "")
                org_location = org_details.get("location", "")
                org_bio = org_details.get("bio", "")

                # Create organization info string
                org_info = f"Organization Name: {org_name}\n"
                if org_email:
                    org_info += f"Email: {org_email}\n"
                if org_website:
                    org_info += f"Website: {org_website}\n"
                if org_location:
                    org_info += f"Location: {org_location}\n"
                if org_bio:
                    org_info += f"About: {org_bio}\n"

                # Social media links if available
                social_media = []
                if org_details.get("linkedin"):
                    social_media.append(f"LinkedIn: {org_details.get('linkedin')}")
                if org_details.get("twitter"):
                    social_media.append(f"Twitter: {org_details.get('twitter')}")
                if org_details.get("instagram"):
                    social_media.append(f"Instagram: {org_details.get('instagram')}")
                if org_details.get("youtube"):
                    social_media.append(f"YouTube: {org_details.get('youtube')}")
                if org_details.get("tiktok"):
                    social_media.append(f"TikTok: {org_details.get('tiktok')}")

                if social_media:
                    org_info += "Social Media:\n" + "\n".join(social_media)

                # Prepare the arguments for the tool
                arguments = {
                    "existing_job_description": request.arguments["existing_job_description"],
                    "custom_prompt": request.arguments["custom_prompt"],
                    "organization_info": org_info,
                }

                # Call the tool
                tool_response = await with_mcp_session(
                    lambda session: session.call_tool(request.tool_name, arguments=arguments)
                )

                # Check if tool_response content is valid before parsing
                if not tool_response.content or not hasattr(tool_response.content[0], "text"):
                    raise MCPInvalidToolResponse(
                        f"Tool '{request.tool_name}' returned invalid or empty content."
                    )

                # Parse the content from the tool response
                response_data = json.loads(tool_response.content[0].text)

                if response_data.get("is_error", False):
                    error_message = response_data.get(
                        "message", f"Tool '{request.tool_name}' returned an error"
                    )
                    raise MCPToolExecutionException(error_message)

                # Get the updated job description data
                updated_job_description_data = response_data.get("updated_job_description", "")

                # Create a structured data object for the PDF generator
                job_data = {
                    "job_title": "Updated Job Description",
                    "company_name": org_details.get("organization_name", "Company"),
                    "job_description": updated_job_description_data,
                }

                # If updated_job_description_data is a dictionary, extract job_title and company_name if available
                if isinstance(updated_job_description_data, dict):
                    if "job_title" in updated_job_description_data:
                        job_data["job_title"] = updated_job_description_data["job_title"]
                    if "company_name" in updated_job_description_data:
                        job_data["company_name"] = updated_job_description_data["company_name"]
                # If it's a string that might be JSON, try to parse it
                elif isinstance(
                    updated_job_description_data, str
                ) and updated_job_description_data.strip().startswith("{"):
                    try:
                        parsed_jd = json.loads(updated_job_description_data)
                        if isinstance(parsed_jd, dict):
                            # Use the parsed dictionary as the job description
                            job_data["job_description"] = parsed_jd

                            # Extract job_title and company_name if available
                            if "job_title" in parsed_jd:
                                job_data["job_title"] = parsed_jd["job_title"]
                            if "company_name" in parsed_jd:
                                job_data["company_name"] = parsed_jd["company_name"]
                    except Exception as e:
                        # If parsing fails, log the error and continue with the text version
                        print(f"Error parsing updated job description JSON: {str(e)}")
                        # Keep the original updated_job_description_data in job_data

                # Generate the PDF
                pdf_buffer = generate_job_description_pdf(job_data)

                # Create a unique S3 key for the PDF
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                user_id = current_user["user_id"]
                filename = f"{job_data['job_title'].replace(' ', '_')}_{timestamp}.pdf"
                s3_key = f"job_descriptions/{user_id}/{timestamp}_{uuid4().hex[:8]}_{filename}"

                # Upload the PDF to S3 (no local file to delete in this case since we're using a buffer)
                s3_url = upload_file_to_s3(pdf_buffer, s3_key, content_type="application/pdf")

                # Return the updated job description and PDF URL
                result = {
                    "updated_job_description": updated_job_description_data,
                    "pdf_url": s3_url,
                }
                response_data = result

            return {
                "status": "success",
                "message": f"Tool '{request.tool_name}' executed successfully",
                "data": response_data,
            }

        except json.JSONDecodeError as json_err:

            raise MCPToolResponseParsingError(
                f"Failed to parse response from tool '{request.tool_name}'."
            )

        except MCPToolExecutionException as e:
            raise MCPToolExecutionException(str(e))

        except Exception as e:
            raise MCPGenericException(request.tool_name, str(e))

    async def execute_pre_candidate_suitability_tool(
        self, request: CandidateSuitabilityPreSchema, current_org: Organization
    ):
        """
        Executes the Candidate Suitability tool for a specific interview.

        This function validates the interview ownership, checks for required fields,
        calls the Candidate Suitability tool, and updates the interview with the results.

        Args:
            request:CandidateSuitabilityPreSchema(resume_s3_link,job_description_s3_link)
            current_org: The Organization object of the authenticated user.

        Raises:
            MCPInvalidToolResponse: If the tool returns invalid or empty content.
            MCPToolExecutionException: If the tool execution fails.
            MCPToolResponseParsingError: If the response cannot be parsed as JSON.

        Returns:
            A dictionary with the status, message, and data from the tool execution.
        """

        try:
            # Check subscription limits for candidate suitability (organizations only)
            usage = self.subscription_service.get_organization_subscription_usage(current_org.id)
            if not usage.can_perform_candidate_suitability:
                raise MCPToolExecutionException(
                    f"Candidate suitability limit exceeded. Your organization has used {usage.candidate_suitability_used} out of {usage.candidate_suitability_limit} candidate suitability checks in your {usage.plan_name}."
                )
            # 5. Prepare arguments for the tool
            arguments = {
                "resume_s3_link": request.resume_s3_link,
                "job_description_s3_link": request.job_description_s3_link,
            }

            # 6. Call the tool
            tool_response = await with_mcp_session(
                lambda session: session.call_tool(
                    Tools.CANDIDATE_SUITABILITY_PRE, arguments=arguments
                )
            )

            # 7. Validate tool response
            if not tool_response.content or not hasattr(tool_response.content[0], "text"):
                raise MCPInvalidToolResponse(
                    f"Tool '{Tools.CANDIDATE_SUITABILITY_PRE}' returned invalid or empty content."
                )

            # 8. Parse the content from the tool response
            response_data = json.loads(tool_response.content[0].text)

            # 9. Check for errors in the response
            if response_data.get("is_error", False):
                error_message = response_data.get(
                    "message", f"Tool '{Tools.CANDIDATE_SUITABILITY_PRE}' returned an error"
                )
                raise MCPToolExecutionException(error_message)

            # Increment candidate suitability usage for organizations
            try:
                self.subscription_service.increment_organization_candidate_suitability_usage(current_org.id)
            except Exception as e:
                # Log the error but don't fail the candidate suitability check
                print(f"Warning: Failed to increment candidate suitability usage for organization {current_org.id}: {str(e)}")

            # 11. Return success response
            return {
                "status": "success",
                "message": f"Tool '{Tools.CANDIDATE_SUITABILITY_PRE}' executed successfully",
                "data": response_data,
            }

        except json.JSONDecodeError:
            raise MCPToolResponseParsingError(
                f"Failed to parse response from tool '{Tools.CANDIDATE_SUITABILITY_PRE}'."
            )
        except MCPToolExecutionException as e:
            raise MCPToolExecutionException(str(e))
        except Exception as e:
            raise MCPGenericException(Tools.CANDIDATE_SUITABILITY_PRE, str(e))

    async def generate_job_description(self, request: JobDescriptionSchema, current_user: dict):
        """
        Executes the Generate Job Description tool.

        This function takes experience level, job role, and optional custom prompt
        to generate a well-structured job description.

        Args:
            request: JobDescriptionSchema containing experience_level, job_role, and optional custom_prompt
            current_user: The authenticated user (can be an organization or individual)

        Raises:
            MCPInvalidToolResponse: If the tool returns invalid or empty content.
            MCPToolExecutionException: If the tool execution fails.
            MCPToolResponseParsingError: If the response cannot be parsed as JSON.

        Returns:
            A dictionary with the status, message, and the generated job description.
        """
        try:
            # Check subscription limits for job description creation
            user_type = current_user.get("registration_type")
            if user_type == "organization":
                usage = self.subscription_service.get_organization_subscription_usage(current_user["user_id"])
                if not usage.can_create_jd:
                    raise MCPToolExecutionException(
                        f"Job description limit exceeded. Your organization has used {usage.jd_used} out of {usage.jd_limit} job descriptions in your {usage.plan_name}."
                    )
            elif user_type == "individual":
                usage = self.subscription_service.get_subscription_usage(current_user["user_id"])
                if not usage.can_create_jd:
                    raise MCPToolExecutionException(
                        f"Job description limit exceeded. You have used {usage.jd_used} out of {usage.jd_limit} job descriptions in your {usage.plan_name}."
                    )
            # Prepare arguments for the tool
            arguments = {
                "experience_level": request.experience_level,
                "job_role": request.job_role,
            }

            # Add custom_prompt
            custom_prompt = request.custom_prompt if request.custom_prompt else ""

            # If user is an organization, include organization info
            if current_user["registration_type"] == RegistrationType.organization:
                org_details = self.user_service.get_org_by_id(current_user["user_id"])

                # Extract relevant organization information
                org_name = org_details.get("organization_name", "")
                org_email = org_details.get("email", "")
                org_website = org_details.get("website", "")
                org_location = org_details.get("location", "")
                org_bio = org_details.get("bio", "")

                # Create organization info string
                org_info = f"Organization Name: {org_name}\n"
                if org_email:
                    org_info += f"Email: {org_email}\n"
                if org_website:
                    org_info += f"Website: {org_website}\n"
                if org_location:
                    org_info += f"Location: {org_location}\n"
                if org_bio:
                    org_info += f"About: {org_bio}\n"

                # Add organization info to custom prompt
                arguments["custom_prompt"] = custom_prompt + "\n" + org_info
            else:
                # For individual users, just use the custom prompt as is
                arguments["custom_prompt"] = custom_prompt

            # Call the tool
            tool_response = await with_mcp_session(
                lambda session: session.call_tool(
                    Tools.GENERATE_JOB_DESCRIPTION, arguments=arguments
                )
            )

            # Validate tool response
            if not tool_response.content or not hasattr(tool_response.content[0], "text"):
                raise MCPInvalidToolResponse(
                    f"Tool '{Tools.GENERATE_JOB_DESCRIPTION}' returned invalid or empty content."
                )

            # Parse the content from the tool response
            response_data = json.loads(tool_response.content[0].text)

            # Check for errors in the response
            if response_data.get("is_error", False):
                error_message = response_data.get(
                    "message", f"Tool '{Tools.GENERATE_JOB_DESCRIPTION}' returned an error"
                )
                raise MCPToolExecutionException(error_message)

            # Get the job description data
            job_description_data = response_data.get("job_description", "")

            # Create a structured data object for the PDF generator
            job_data = {
                "job_title": request.job_role,
                "company_name": current_user.get("organization_name", "Company"),
                "job_description": job_description_data,
            }

            # If job_description_data is a dictionary, extract job_title and company_name if available
            if isinstance(job_description_data, dict):
                if "job_title" in job_description_data:
                    job_data["job_title"] = job_description_data["job_title"]
                if "company_name" in job_description_data:
                    job_data["company_name"] = job_description_data["company_name"]
            # If it's a string that might be JSON, try to parse it
            elif isinstance(job_description_data, str) and job_description_data.strip().startswith(
                "{"
            ):
                try:
                    parsed_jd = json.loads(job_description_data)
                    if isinstance(parsed_jd, dict):
                        # Use the parsed dictionary as the job description
                        job_data["job_description"] = parsed_jd

                        # Extract job_title and company_name if available
                        if "job_title" in parsed_jd:
                            job_data["job_title"] = parsed_jd["job_title"]
                        if "company_name" in parsed_jd:
                            job_data["company_name"] = parsed_jd["company_name"]
                except Exception as e:
                    # If parsing fails, log the error and continue with the text version
                    print(f"Error parsing job description JSON: {str(e)}")
                    # Keep the original job_description_data in job_data

            # Generate a PDF from the job description
            # Note: We're using the job_data that was already prepared above

            # Generate the PDF
            pdf_buffer = generate_job_description_pdf(job_data)

            # Create a unique S3 key for the PDF
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            user_id = current_user["user_id"]
            filename = f"{request.job_role.replace(' ', '_')}_{timestamp}.pdf"
            s3_key = f"job_descriptions/{user_id}/{timestamp}_{uuid4().hex[:8]}_{filename}"

            # Upload the PDF to S3 (no local file to delete in this case since we're using a buffer)
            s3_url = upload_file_to_s3(pdf_buffer, s3_key, content_type="application/pdf")

            # Increment JD usage based on user type
            try:
                user_type = current_user.get("registration_type")
                if user_type == "organization":
                    self.subscription_service.increment_organization_jd_usage(current_user["user_id"])
                elif user_type == "individual":
                    self.subscription_service.increment_jd_usage(current_user["user_id"])
            except Exception as e:
                # Log the error but don't fail the job description generation
                print(f"Warning: Failed to increment JD usage for user {current_user['user_id']}: {str(e)}")

            # Return success response with both the job description data and S3 link
            return {
                "status": "success",
                "message": "Job description generated successfully",
                "job_description": job_description_data,
                "pdf_url": s3_url,
            }

        except json.JSONDecodeError:
            raise MCPToolResponseParsingError(
                f"Failed to parse response from tool '{Tools.GENERATE_JOB_DESCRIPTION}'."
            )
        except MCPToolExecutionException as e:
            raise MCPToolExecutionException(str(e))
        except Exception as e:
            raise MCPGenericException(Tools.GENERATE_JOB_DESCRIPTION, str(e))

    async def edit_job_description(self, request: EditJobDescriptionSchema, current_user: dict):
        """
        Executes the Edit Job Description tool.

        This function takes an existing job description and custom instructions
        to edit and improve the job description.

        Args:
            request: EditJobDescriptionSchema containing existing_job_description and custom_prompt
            current_user: The authenticated user (can be an organization or individual)

        Raises:
            MCPInvalidToolResponse: If the tool returns invalid or empty content.
            MCPToolExecutionException: If the tool execution fails.
            MCPToolResponseParsingError: If the response cannot be parsed as JSON.

        Returns:
            A dictionary with the status, message, and the updated job description.
        """
        try:
            # Prepare base arguments for the tool
            arguments = {
                "existing_job_description": request.existing_job_description,
                "custom_prompt": request.custom_prompt,
            }

            # If user is an organization, include organization info
            if current_user["registration_type"] == RegistrationType.organization:
                # Get organization details from the database
                org_details = self.user_service.get_org_by_id(current_user["user_id"])

                # Extract relevant organization information
                org_name = org_details.get("organization_name", "")
                org_email = org_details.get("email", "")
                org_website = org_details.get("website", "")
                org_location = org_details.get("location", "")
                org_bio = org_details.get("bio", "")

                # Create organization info string
                org_info = f"Organization Name: {org_name}\n"
                if org_email:
                    org_info += f"Email: {org_email}\n"
                if org_website:
                    org_info += f"Website: {org_website}\n"
                if org_location:
                    org_info += f"Location: {org_location}\n"
                if org_bio:
                    org_info += f"About: {org_bio}\n"

                # Add organization info to custom prompt
                arguments["custom_prompt"] = request.custom_prompt + "\n" + org_info

            # Call the tool
            tool_response = await with_mcp_session(
                lambda session: session.call_tool(Tools.EDIT_JOB_DESCRIPTION, arguments=arguments)
            )

            # Validate tool response
            if not tool_response.content or not hasattr(tool_response.content[0], "text"):
                raise MCPInvalidToolResponse(
                    f"Tool '{Tools.EDIT_JOB_DESCRIPTION}' returned invalid or empty content."
                )

            # Parse the content from the tool response
            response_data = json.loads(tool_response.content[0].text)

            # Check for errors in the response
            if response_data.get("is_error", False):
                error_message = response_data.get(
                    "message", f"Tool '{Tools.EDIT_JOB_DESCRIPTION}' returned an error"
                )
                raise MCPToolExecutionException(error_message)

            # Get the updated job description data
            updated_job_description_data = response_data.get("updated_job_description", "")

            # Create a structured data object for the PDF generator
            job_data = {
                "job_title": "Updated Job Description",
                "company_name": current_user.get("organization_name", "Company"),
                "job_description": updated_job_description_data,
            }

            # If updated_job_description_data is a dictionary, extract job_title and company_name if available
            if isinstance(updated_job_description_data, dict):
                if "job_title" in updated_job_description_data:
                    job_data["job_title"] = updated_job_description_data["job_title"]
                if "company_name" in updated_job_description_data:
                    job_data["company_name"] = updated_job_description_data["company_name"]
            # If it's a string that might be JSON, try to parse it
            elif isinstance(
                updated_job_description_data, str
            ) and updated_job_description_data.strip().startswith("{"):
                try:
                    parsed_jd = json.loads(updated_job_description_data)
                    if isinstance(parsed_jd, dict):
                        # Use the parsed dictionary as the job description
                        job_data["job_description"] = parsed_jd

                        # Extract job_title and company_name if available
                        if "job_title" in parsed_jd:
                            job_data["job_title"] = parsed_jd["job_title"]
                        if "company_name" in parsed_jd:
                            job_data["company_name"] = parsed_jd["company_name"]
                except Exception as e:
                    # If parsing fails, log the error and continue with the text version
                    print(f"Error parsing updated job description JSON: {str(e)}")
                    # Keep the original updated_job_description_data in job_data

            # Generate a PDF from the updated job description
            # Note: We're using the job_data that was already prepared above

            # Generate the PDF
            pdf_buffer = generate_job_description_pdf(job_data)

            # Create a unique S3 key for the PDF
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            user_id = current_user["user_id"]
            filename = f"{job_data['job_title'].replace(' ', '_')}_{timestamp}.pdf"
            s3_key = f"job_descriptions/{user_id}/{timestamp}_{uuid4().hex[:8]}_{filename}"

            # Upload the PDF to S3 (no local file to delete in this case since we're using a buffer)
            s3_url = upload_file_to_s3(pdf_buffer, s3_key, content_type="application/pdf")

            # Return success response with both the updated job description data and S3 link
            return {
                "status": "success",
                "message": "Job description updated successfully",
                "updated_job_description": updated_job_description_data,
                "pdf_url": s3_url,
            }

        except json.JSONDecodeError:
            raise MCPToolResponseParsingError(
                f"Failed to parse response from tool '{Tools.EDIT_JOB_DESCRIPTION}'."
            )
        except MCPToolExecutionException as e:
            raise MCPToolExecutionException(str(e))
        except Exception as e:
            raise MCPGenericException(Tools.EDIT_JOB_DESCRIPTION, str(e))

    async def list_resume_templates(self, current_user: dict):
        """
        Executes the List Resume Templates tool.

        This function retrieves all available resume templates with their descriptions and sections.

        Args:
            current_user: The authenticated user (can be an organization or individual)

        Raises:
            MCPInvalidToolResponse: If the tool returns invalid or empty content.
            MCPToolExecutionException: If the tool execution fails.
            MCPToolResponseParsingError: If the response cannot be parsed as JSON.

        Returns:
            A dictionary with the status, message, and list of available resume templates.
        """
        try:
            # No arguments needed for listing templates
            arguments = {}

            # Call the tool
            tool_response = await with_mcp_session(
                lambda session: session.call_tool(Tools.LIST_RESUME_TEMPLATES, arguments=arguments)
            )

            # Validate tool response
            if not tool_response.content or not hasattr(tool_response.content[0], "text"):
                raise MCPInvalidToolResponse(
                    f"Tool '{Tools.LIST_RESUME_TEMPLATES}' returned invalid or empty content."
                )

            # Parse the content from the tool response
            response_data = json.loads(tool_response.content[0].text)

            # Check for errors in the response
            if response_data.get("is_error", False):
                error_message = response_data.get(
                    "message", f"Tool '{Tools.LIST_RESUME_TEMPLATES}' returned an error"
                )
                raise MCPToolExecutionException(error_message)

            # Return success response with the templates data
            return {
                "status": "success",
                "message": "Resume templates retrieved successfully",
                "data": response_data,
            }

        except json.JSONDecodeError:
            raise MCPToolResponseParsingError(
                f"Failed to parse response from tool '{Tools.LIST_RESUME_TEMPLATES}'."
            )
        except MCPToolExecutionException as e:
            raise MCPToolExecutionException(str(e))
        except Exception as e:
            raise MCPGenericException(Tools.LIST_RESUME_TEMPLATES, str(e))

    async def build_resume(self, request: BuildResumeSchema, current_user: dict):
        """
        Executes the Build Resume tool.

        This function takes resume data and builds a professional resume using the selected template.

        Args:
            request: BuildResumeSchema containing all resume data including template_id, contact_info, etc.
            current_user: The authenticated user (can be an organization or individual)

        Raises:
            MCPInvalidToolResponse: If the tool returns invalid or empty content.
            MCPToolExecutionException: If the tool execution fails.
            MCPToolResponseParsingError: If the response cannot be parsed as JSON.

        Returns:
            A dictionary with the status, message, and the generated resume data.
        """
        try:
            # Check subscription limits for resume creation (individual users only)
            user_type = current_user.get("registration_type")
            if user_type == "individual":
                usage = self.subscription_service.get_subscription_usage(current_user["user_id"])
                if not usage.can_create_resume:
                    raise MCPToolExecutionException(
                        f"Resume limit exceeded. You have used {usage.resume_used} out of {usage.resume_limit} resumes in your {usage.plan_name}."
                    )
            elif user_type == "organization":
                # Organizations cannot create resumes
                raise MCPToolExecutionException("Resume creation is only available for individual users.")
            # Convert the Pydantic model to a dictionary for the tool arguments
            arguments = request.model_dump()

            # Call the tool
            tool_response = await with_mcp_session(
                lambda session: session.call_tool(Tools.BUILD_RESUME, arguments=arguments)
            )

            # Validate tool response
            if not tool_response.content or not hasattr(tool_response.content[0], "text"):
                raise MCPInvalidToolResponse(
                    f"Tool '{Tools.BUILD_RESUME}' returned invalid or empty content."
                )

            # Parse the content from the tool response
            response_data = json.loads(tool_response.content[0].text)

            # Check for errors in the response
            if response_data.get("is_error", False):
                error_message = response_data.get(
                    "message", f"Tool '{Tools.BUILD_RESUME}' returned an error"
                )
                raise MCPToolExecutionException(error_message)

            resume_data = response_data["resume"]
            output = {
                "content": {"template_id": resume_data["template_id"], **resume_data["content"]}
            }

            # Increment resume usage for individual users only
            try:
                user_type = current_user.get("registration_type")
                if user_type == "individual":
                    self.subscription_service.increment_resume_usage(current_user["user_id"])
            except Exception as e:
                # Log the error but don't fail the resume creation
                print(f"Warning: Failed to increment resume usage for user {current_user['user_id']}: {str(e)}")

            # Return success response with the resume data
            return {
                "status": "success",
                "message": "Resume built successfully",
                "data": output,
            }

        except json.JSONDecodeError:
            raise MCPToolResponseParsingError(
                f"Failed to parse response from tool '{Tools.BUILD_RESUME}'."
            )
        except MCPToolExecutionException as e:
            raise MCPToolExecutionException(str(e))
        except Exception as e:
            raise MCPGenericException(Tools.BUILD_RESUME, str(e))
