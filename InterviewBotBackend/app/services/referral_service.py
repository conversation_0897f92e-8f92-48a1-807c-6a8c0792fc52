"""
Referral Service

This service handles all referral-related operations including:
- Generating and managing referral codes
- Processing referrals during signup
- Awarding tokens
- Tracking referral statistics
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, Set
from uuid import uuid4
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, func

from app.models.referral import (
    ReferralCode,
    ReferralRelationship,
    TokenTransaction,
    ReferralStats,
    TokenTransactionType
)
from app.models.user import User
from app.models.organization import Organization
from app.utils.referral_utils import (
    generate_unique_referral_code,
    normalize_referral_code,
    validate_referral_code_format
)
from app.utils.exceptions.database_exceptions import DatabaseOperationException


class ReferralService:
    """Service class for handling referral system operations."""
    
    # Token amounts for referral bonuses
    REFERRER_BONUS = 300  # Tokens for the person who referred
    REFEREE_BONUS = 100   # Tokens for the person who signed up with referral code
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_or_create_referral_code(self, registration_type: str, entity_id: str, entity_name: str) -> str:
        """
        Get existing referral code or create a new one for an entity.

        Args:
            registration_type: "individual" or "organization"
            entity_id: The entity's ID
            entity_name: The entity's name (for generating personalized codes)

        Returns:
            The referral code string
        """
        try:
            # Check if referral code already exists
            existing_code = self.db.query(ReferralCode).filter(
                and_(
                    ReferralCode.entity_type == registration_type,
                    ReferralCode.entity_id == entity_id,
                    ReferralCode.is_active == True
                )
            ).first()

            if existing_code:
                return existing_code.code

            # Get all existing codes to ensure uniqueness
            existing_codes: Set[str] = set(
                code[0] for code in self.db.query(ReferralCode.code).filter(
                    ReferralCode.is_active == True
                ).all()
            )

            # Generate unique referral code
            new_code = generate_unique_referral_code(
                entity_id=entity_id,
                entity_name=entity_name,
                existing_codes=existing_codes
            )

            # Create referral code record
            referral_code = ReferralCode(
                id=str(uuid4()),
                code=new_code,
                entity_type=registration_type,
                entity_id=entity_id,
                is_active=True
            )

            self.db.add(referral_code)

            # Update the entity's referral_code field
            if registration_type == "individual":
                user = self.db.query(User).filter(User.id == entity_id).first()
                if user:
                    user.referral_code = new_code
            else:
                org = self.db.query(Organization).filter(Organization.id == entity_id).first()
                if org:
                    org.referral_code = new_code

            # Initialize referral stats
            self._initialize_referral_stats(registration_type, entity_id)
            
            self.db.commit()
            return new_code
            
        except SQLAlchemyError as e:
            self.db.rollback()
            raise DatabaseOperationException(f"referral code creation: {str(e)}")
    
    def validate_referral_code(self, code: str) -> Optional[ReferralCode]:
        """
        Validate and return referral code if it exists and is active.
        
        Args:
            code: The referral code to validate
        
        Returns:
            ReferralCode object if valid, None otherwise
        """
        if not validate_referral_code_format(code):
            return None
        
        normalized_code = normalize_referral_code(code)
        if not normalized_code:
            return None
        
        return self.db.query(ReferralCode).filter(
            and_(
                ReferralCode.code == normalized_code,
                ReferralCode.is_active == True
            )
        ).first()
    
    def process_referral_signup(
        self,
        referral_code: str,
        referee_registration_type: str,
        referee_entity_id: str
    ) -> bool:
        """
        Process a referral when someone signs up with a referral code.
        Creates the referral relationship but does NOT award tokens yet.
        Tokens are awarded only when the referee purchases a paid plan.

        Args:
            referral_code: The referral code used during signup
            referee_registration_type: Type of entity that signed up ("individual" or "organization")
            referee_entity_id: ID of the entity that signed up

        Returns:
            True if referral was processed successfully, False otherwise
        """
        try:
            # Validate referral code
            referrer_code = self.validate_referral_code(referral_code)
            if not referrer_code:
                return False

            # Check if this entity has already been referred
            existing_referral = self.db.query(ReferralRelationship).filter(
                and_(
                    ReferralRelationship.referee_entity_type == referee_registration_type,
                    ReferralRelationship.referee_entity_id == referee_entity_id
                )
            ).first()

            if existing_referral:
                return False  # Already referred

            # Prevent self-referral
            if (referrer_code.entity_type == referee_registration_type and
                referrer_code.entity_id == referee_entity_id):
                return False

            # Create referral relationship (without awarding tokens)
            referral_relationship = ReferralRelationship(
                id=str(uuid4()),
                referrer_code_id=referrer_code.id,
                referrer_entity_type=referrer_code.entity_type,
                referrer_entity_id=referrer_code.entity_id,
                referee_entity_type=referee_registration_type,
                referee_entity_id=referee_entity_id,
                referral_date=datetime.now(timezone.utc),
                referee_purchased_paid_plan=False,
                tokens_awarded=False
            )

            self.db.add(referral_relationship)
            self.db.commit()

            return True

        except SQLAlchemyError as e:
            self.db.rollback()
            return False

    def process_paid_plan_purchase(
        self,
        registration_type: str,
        entity_id: str,
        plan_id: str
    ) -> bool:
        """
        Process token awards when a user/organization purchases a paid plan.
        This is called after successful payment confirmation.

        Args:
            registration_type: "individual" or "organization"
            entity_id: The entity's ID who purchased the plan
            plan_id: The subscription plan ID that was purchased

        Returns:
            True if tokens were awarded successfully
        """
        try:
            # Check if this entity was referred by someone
            referral = self.db.query(ReferralRelationship).filter(
                and_(
                    ReferralRelationship.referee_entity_type == registration_type,
                    ReferralRelationship.referee_entity_id == entity_id,
                    ReferralRelationship.referee_purchased_paid_plan == False
                )
            ).first()

            if not referral:
                return False  # No referral found or already processed

            # Mark that referee purchased a paid plan
            referral.referee_purchased_paid_plan = True
            referral.paid_plan_purchase_date = datetime.now(timezone.utc)

            # Award tokens to both referrer and referee
            success = self._award_referral_tokens(referral.id)

            if success:
                self.db.commit()
                return True
            else:
                self.db.rollback()
                return False

        except SQLAlchemyError as e:
            self.db.rollback()
            return False
    
    def _award_referral_tokens(self, referral_relationship_id: str) -> bool:
        """
        Award tokens for a successful referral when referee purchases a paid plan.

        Args:
            referral_relationship_id: ID of the referral relationship

        Returns:
            True if tokens were awarded successfully
        """
        try:
            # Get referral relationship
            referral = self.db.query(ReferralRelationship).filter(
                ReferralRelationship.id == referral_relationship_id
            ).first()

            if not referral or referral.tokens_awarded or not referral.referee_purchased_paid_plan:
                return False

            # Award tokens to referrer (300 tokens)
            self._add_token_transaction(
                entity_type=referral.referrer_entity_type,
                entity_id=referral.referrer_entity_id,
                transaction_type=TokenTransactionType.REFERRAL_BONUS_REFERRER,
                amount=self.REFERRER_BONUS,
                reference_id=referral_relationship_id,
                reference_type="referral",
                description=f"Referral bonus: {referral.referee_entity_type.value} purchased paid plan"
            )

            # Award tokens to referee (100 tokens)
            self._add_token_transaction(
                entity_type=referral.referee_entity_type,
                entity_id=referral.referee_entity_id,
                transaction_type=TokenTransactionType.REFERRAL_BONUS_REFEREE,
                amount=self.REFEREE_BONUS,
                reference_id=referral_relationship_id,
                reference_type="referral",
                description=f"Welcome bonus for purchasing paid plan with referral code"
            )

            # Mark tokens as awarded
            referral.tokens_awarded = True
            referral.tokens_awarded_at = datetime.now(timezone.utc)

            # Update referral stats for referrer
            self._update_referral_stats(referral.referrer_entity_type, referral.referrer_entity_id)

            return True

        except SQLAlchemyError as e:
            return False

    def _add_token_transaction(
        self,
        registration_type: str,
        entity_id: str,
        transaction_type: TokenTransactionType,
        amount: int,
        reference_id: Optional[str] = None,
        reference_type: Optional[str] = None,
        description: Optional[str] = None
    ) -> bool:
        """
        Add a token transaction and update balance.

        Args:
            entity_type: USER or ORGANIZATION
            entity_id: The entity's ID
            transaction_type: Type of transaction
            amount: Amount of tokens (positive for credit, negative for debit)
            reference_id: Reference ID for the transaction
            reference_type: Type of reference
            description: Description of the transaction

        Returns:
            True if transaction was added successfully
        """
        try:
            # Get current balance
            current_balance = self.get_token_balance(registration_type, entity_id)
            new_balance = current_balance + amount

            # Create transaction record
            transaction = TokenTransaction(
                id=str(uuid4()),
                entity_type=registration_type,
                entity_id=entity_id,
                transaction_type=transaction_type,
                amount=amount,
                balance_after=new_balance,
                reference_id=reference_id,
                reference_type=reference_type,
                description=description
            )

            self.db.add(transaction)
            return True

        except SQLAlchemyError:
            return False

    def get_token_balance(self, registration_type: str, entity_id: str) -> int:
        """
        Get current token balance for an entity.

        Args:
            registration_type: "individual" or "organization"
            entity_id: The entity's ID

        Returns:
            Current token balance
        """
        # Get the latest transaction to get current balance
        latest_transaction = self.db.query(TokenTransaction).filter(
            and_(
                TokenTransaction.entity_type == registration_type,
                TokenTransaction.entity_id == entity_id
            )
        ).order_by(TokenTransaction.created_at.desc()).first()

        return latest_transaction.balance_after if latest_transaction else 0

    def get_referral_stats(self, registration_type: str, entity_id: str) -> Dict[str, Any]:
        """
        Get referral statistics for an entity.

        Args:
            registration_type: "individual" or "organization"
            entity_id: The entity's ID

        Returns:
            Dictionary containing referral statistics
        """
        # Get or create stats record
        stats = self.db.query(ReferralStats).filter(
            and_(
                ReferralStats.entity_type == registration_type,
                ReferralStats.entity_id == entity_id
            )
        ).first()

        if not stats:
            stats = self._initialize_referral_stats(registration_type, entity_id)

        # Get referral code
        referral_code = self.db.query(ReferralCode).filter(
            and_(
                ReferralCode.entity_type == registration_type,
                ReferralCode.entity_id == entity_id,
                ReferralCode.is_active == True
            )
        ).first()

        return {
            "referral_code": referral_code.code if referral_code else None,
            "total_referrals": stats.total_referrals,
            "successful_referrals": stats.successful_referrals,
            "total_tokens_earned": stats.total_tokens_earned,
            "current_token_balance": stats.current_token_balance,
            "last_referral_date": stats.last_referral_date.isoformat() if stats.last_referral_date else None
        }

    def get_referral_history(self, registration_type: str, entity_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get referral history for an entity.

        Args:
            registration_type: "individual" or "organization"
            entity_id: The entity's ID
            limit: Maximum number of records to return

        Returns:
            List of referral history records
        """
        referrals = self.db.query(ReferralRelationship).filter(
            and_(
                ReferralRelationship.referrer_entity_type == registration_type,
                ReferralRelationship.referrer_entity_id == entity_id
            )
        ).order_by(ReferralRelationship.referral_date.desc()).limit(limit).all()

        history = []
        for referral in referrals:
            # Get referee name
            referee_name = "Unknown"
            if referral.referee_entity_type == "individual":
                user = self.db.query(User).filter(User.id == referral.referee_entity_id).first()
                referee_name = user.full_name if user else "Unknown User"
            else:
                org = self.db.query(Organization).filter(Organization.id == referral.referee_entity_id).first()
                referee_name = org.organization_name if org else "Unknown Organization"

            history.append({
                "referee_name": referee_name,
                "referee_type": referral.referee_entity_type.value,
                "referral_date": referral.referral_date.isoformat(),
                "tokens_awarded": referral.tokens_awarded,
                "tokens_awarded_at": referral.tokens_awarded_at.isoformat() if referral.tokens_awarded_at else None
            })

        return history

    def _initialize_referral_stats(self, registration_type: str, entity_id: str) -> ReferralStats:
        """
        Initialize referral stats for an entity.

        Args:
            registration_type: "individual" or "organization"
            entity_id: The entity's ID

        Returns:
            ReferralStats object
        """
        stats = ReferralStats(
            id=str(uuid4()),
            entity_type=registration_type,
            entity_id=entity_id,
            total_referrals=0,
            successful_referrals=0,
            total_tokens_earned=0,
            current_token_balance=0
        )

        self.db.add(stats)
        return stats

    def _update_referral_stats(self, registration_type: str, entity_id: str) -> None:
        """
        Update referral statistics for an entity.

        Args:
            registration_type: "individual" or "organization"
            entity_id: The entity's ID
        """
        try:
            # Get or create stats record
            stats = self.db.query(ReferralStats).filter(
                and_(
                    ReferralStats.entity_type == registration_type,
                    ReferralStats.entity_id == entity_id
                )
            ).first()

            if not stats:
                stats = self._initialize_referral_stats(registration_type, entity_id)

            # Count total referrals
            total_referrals = self.db.query(func.count(ReferralRelationship.id)).filter(
                and_(
                    ReferralRelationship.referrer_entity_type == registration_type,
                    ReferralRelationship.referrer_entity_id == entity_id
                )
            ).scalar() or 0

            # Count successful referrals (those with tokens awarded)
            successful_referrals = self.db.query(func.count(ReferralRelationship.id)).filter(
                and_(
                    ReferralRelationship.referrer_entity_type == registration_type,
                    ReferralRelationship.referrer_entity_id == entity_id,
                    ReferralRelationship.tokens_awarded == True
                )
            ).scalar() or 0

            # Calculate total tokens earned from referrals (both referrer and referee bonuses)
            total_tokens_earned = self.db.query(func.sum(TokenTransaction.amount)).filter(
                and_(
                    TokenTransaction.entity_type == registration_type,
                    TokenTransaction.entity_id == entity_id,
                    or_(
                        TokenTransaction.transaction_type == TokenTransactionType.REFERRAL_BONUS_REFERRER,
                        TokenTransaction.transaction_type == TokenTransactionType.REFERRAL_BONUS_REFEREE
                    )
                )
            ).scalar() or 0

            # Get current token balance
            current_balance = self.get_token_balance(registration_type, entity_id)

            # Get last referral date
            last_referral = self.db.query(ReferralRelationship).filter(
                and_(
                    ReferralRelationship.referrer_entity_type == registration_type,
                    ReferralRelationship.referrer_entity_id == entity_id
                )
            ).order_by(ReferralRelationship.referral_date.desc()).first()

            # Update stats
            stats.total_referrals = total_referrals
            stats.successful_referrals = successful_referrals
            stats.total_tokens_earned = total_tokens_earned
            stats.current_token_balance = current_balance
            stats.last_referral_date = last_referral.referral_date if last_referral else None
            stats.last_updated = datetime.now(timezone.utc)

        except SQLAlchemyError:
            pass  # Silently fail stats update to not break main flow
