from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
import uuid

from app.models.admin import Admin
from app.core.security import get_password_hash, verify_password, create_access_token
from app.utils.exceptions.user_exceptions import (
    UnAuthorizedUserException,
)
from app.utils.exceptions.database_exceptions import (
    DatabaseOperationException,
)
from app.schemas.user_schema import (
    RegistrationType,
)
from app.core.config import settings
from app.models.admin import Admin
from app.services.subscription_service import SubscriptionService

class AdminService:
    def __init__(self, db: Session):
        self.db = db
        self.subscription_service = SubscriptionService(db)
        self.init_default_admin()

    def init_default_admin(self):
        try:
            default_email = settings.ADMIN_EMAIL
            default_password = settings.ADMIN_PASSWORD

            if not default_email or not default_password:
                print("⚠️  No default admin credentials found in environment.")
                return

            query = select(Admin).where(Admin.email == default_email)
            result = self.db.execute(query)
            admin = result.scalar_one_or_none()

            if admin:
                print(f"✅ Default admin already exists: {default_email}")
            else:
                new_admin = Admin(
                    id=str(uuid.uuid4()),
                    email=default_email,
                    hashed_password=get_password_hash(default_password),
                )
                self.db.add(new_admin)
                self.db.commit()
                print(f"✅ Default admin created: {default_email}")

            # Initialize default subscription plan
            self.init_default_subscription_plan()

        except Exception:
            print("❌ Failed to initialize default admin")
            self.db.rollback()

    def init_default_subscription_plan(self):
        """Initialize the default subscription plan if it doesn't exist."""
        try:
            default_plan = self.subscription_service.get_default_subscription_plan()
            if not default_plan:
                default_plan = self.subscription_service.create_default_subscription_plan()
                print(f"✅ Default subscription plan created: {default_plan.name}")
            else:
                print(f"✅ Default subscription plan already exists: {default_plan.name}")
        except Exception as e:
            print(f"⚠️  Failed to initialize default subscription plan: {str(e)}")

    async def login(self, email: str, password: str) -> dict:
        """
        Authenticate an admin using email and password.
        Returns a token and admin details upon successful authentication.
        """
        query = select(Admin).where(Admin.email == email)
        try:
            result = self.db.execute(query)
            admin = result.scalar_one_or_none()
        except SQLAlchemyError:
            raise DatabaseOperationException(operation="fetching admin")

        if not admin or not verify_password(password, admin.hashed_password):
            raise UnAuthorizedUserException(detail="Invalid email or password")

        return self._create_auth_response(admin, "Admin login successful")

    def get_admin_by_id(self, admin_id: str) -> dict:
        """
        Retrieve admin details by ID.
        """
        query = select(Admin).where(Admin.id == admin_id)
        try:
            result = self.db.execute(query)
            admin = result.scalar_one_or_none()
        except SQLAlchemyError:
            raise DatabaseOperationException(operation="fetching admin by ID")

        if not admin:
            raise UnAuthorizedUserException(detail="Admin not found")

        return self._create_info_response(admin, "Admin retrieved successfully")

    def get_admin_by_email(self, email: str) -> dict:
        """
        Retrieve admin details by email.
        """
        query = select(Admin).where(Admin.email == email)
        try:
            result = self.db.execute(query)
            admin = result.scalar_one_or_none()
        except SQLAlchemyError:
            raise DatabaseOperationException(operation="fetching admin")

        if not admin:
            raise UnAuthorizedUserException(detail="Admin not found")

        return self._create_info_response(admin, "Admin retrieved successfully")

    def _create_auth_response(self, admin: Admin, message: str) -> dict:
        """
        Helper method for creating a consistent authentication response for admin login.
        """
        access_token = create_access_token(
            data={
                "email": admin.email,
                "user_id": admin.id,
                "registration_type": RegistrationType.admin,
            }
        )
        return {
            "success": True,
            "message": message,
            "access_token": access_token,
            "token_type": "bearer",
            "admin_id": admin.id,
            "email": admin.email,
            "created_at": admin.created_at.isoformat(),
        }

    def _create_info_response(self, admin: Admin, message: str) -> dict:
        """
        Helper method to create a consistent response object when returning admin info without an auth token.
        """
        return {
            "success": True,
            "message": message,
            "admin_id": admin.id,
            "email": admin.email,
            "created_at": admin.created_at.isoformat(),
        }
