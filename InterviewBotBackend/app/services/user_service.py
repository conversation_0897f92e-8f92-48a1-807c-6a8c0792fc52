# app/services/user_service.py
from typing import Optional, Union, List
from sqlalchemy.orm import Session
from sqlalchemy.future import select
from sqlalchemy import func, inspect
from sqlalchemy.exc import SQLAlchemyError
import os  # Needed for file operations
import uuid
import json
from datetime import datetime,timezone
from uuid import uuid4
import requests
import math
import random

from app.models.user import User
from app.models.organization import Organization
from app.models.interview import Interview
from app.core.security import get_password_hash, verify_password, create_access_token
from app.utils.exceptions.user_exceptions import (
    EmailAlreadyRegisteredException,
    InvalidRegistrationTypeException,
    MissingUpdateFieldsException,
    OrganizationNotFoundException,
    UnAuthorizedUserException,
    GoogleOAuthException,
    MissingParametersException,
    ProfileUpdateException,
    InvalidS3UrlException,
    S3ObjectNotAccessibleException,
)
from app.utils.exceptions.database_exceptions import (
    DatabaseOperationException,
)
from app.services.email_service import EmailService
from app.utils.exceptions.text_extraction_exceptions import PDFTextExtractionException
from app.utils.aws_s3 import download_file_from_s3, parse_s3_url, s3_client, S3_BUCKET_NAME
from app.utils.text_extractor import extract_text_from_pdf
from app.core.config import settings
from app.schemas.user_schema import (
    OrganizationUpdateSkillsJobsSchema,
    RegistrationType,
    UserProfileUpdate,
    UserUpdateJobDescriptionsSchema,
    UserProfilePublic,
    PaginatedUserProfilesResponse,
    ProfilePaginationMetadata,
    OrganizationProfilePublic,
    PaginatedOrganizationsResponse,
    OrganizationPaginationMetadata,
)
from sqlalchemy import inspect
from app.services.subscription_service import SubscriptionService
from app.services.referral_service import ReferralService

class UserService:
    def __init__(self, db: Session):
        self.db = db
        # Import here to avoid circular imports
        self.subscription_service = SubscriptionService(db)
        self.email_service = EmailService(db)
        self.referral_service = ReferralService(db)

    async def register_individual_or_organization(
        self,
        email: str,
        password: str,
        full_name: str,
        registration_type: str,
        referral_code: Optional[str] = None,
    ) -> dict:

        hashed_password = get_password_hash(password)

        if self.get_user_by_email(email) or self.get_org_by_email(email):
            raise EmailAlreadyRegisteredException(email)

        if registration_type == RegistrationType.individual:
            user = User(
                id=str(uuid.uuid4()),
                email=email,
                full_name=full_name,
                hashed_password=hashed_password,
                is_email_verified=False,  # Email verification required
            )

            try:
                self.db.add(user)
                self.db.commit()
                self.db.refresh(user)

                # Send verification email automatically
                try:
                    email_result = await self.email_service.send_verification_otp_on_signup(
                        email=email,
                        user_name=full_name,
                        user_type="candidate"
                    )
                    if not email_result["success"]:
                        print(f"Warning: Failed to send verification email to {email}: {email_result['message']}")
                except Exception as e:
                    print(f"Warning: Failed to send verification email to {email}: {str(e)}")

                # Assign default subscription to individual users
                try:
                    default_plan = self.subscription_service.get_default_subscription_plan()
                    if not default_plan:
                        # Create default plan if it doesn't exist
                        default_plan = self.subscription_service.create_default_subscription_plan()
                    self.subscription_service.assign_subscription_to_user(user.id, default_plan.id)
                except Exception as e:
                    # Log the error but don't fail registration
                    print(f"Warning: Failed to assign subscription to user {user.id}: {str(e)}")

                # Generate referral code for the new user
                try:
                    self.referral_service.get_or_create_referral_code(
                        "individual", user.id, user.full_name
                    )
                except Exception as e:
                    print(f"Warning: Failed to create referral code for user {user.id}: {str(e)}")

                # Process referral if referral code was provided (no tokens awarded yet)
                if referral_code:
                    try:
                        self.referral_service.process_referral_signup(
                            referral_code, "individual", user.id
                        )
                    except Exception as e:
                        print(f"Warning: Failed to process referral for user {user.id}: {str(e)}")
            except SQLAlchemyError:
                self.db.rollback()
                raise DatabaseOperationException("individual user registration")

            return {
                "success": True,
                "message": "User registered successfully. A verification email has been sent to your email address. Please check your email and verify your account before logging in.",
                "email": email,
                "registration_type": RegistrationType.individual,
                "email_verification_required": True
            }

        elif registration_type == RegistrationType.organization:
            organization = Organization(
                id=str(uuid.uuid4()),
                email=email,
                organization_name=full_name,
                hashed_password=hashed_password,
                is_email_verified=False,  # Email verification required
            )

            try:
                self.db.add(organization)
                self.db.commit()
                self.db.refresh(organization)

                # Send verification email automatically
                try:
                    email_result = await self.email_service.send_verification_otp_on_signup(
                        email=email,
                        user_name=full_name,
                        user_type="organization"
                    )
                    if not email_result["success"]:
                        print(f"Warning: Failed to send verification email to {email}: {email_result['message']}")
                except Exception as e:
                    print(f"Warning: Failed to send verification email to {email}: {str(e)}")

                # Assign default subscription to organizations
                try:
                    default_plan = self.subscription_service.get_default_organization_subscription_plan()
                    if not default_plan:
                        # Create default organization plan if it doesn't exist
                        default_plan = self.subscription_service.create_default_organization_subscription_plan()
                    self.subscription_service.assign_subscription_to_organization(organization.id, default_plan.id)
                except Exception as e:
                    # Log the error but don't fail registration
                    print(f"Warning: Failed to assign subscription to organization {organization.id}: {str(e)}")

                # Generate referral code for the new organization
                try:
                    self.referral_service.get_or_create_referral_code(
                        "organization", organization.id, organization.organization_name
                    )
                except Exception as e:
                    print(f"Warning: Failed to create referral code for organization {organization.id}: {str(e)}")

                # Process referral if referral code was provided (no tokens awarded yet)
                if referral_code:
                    try:
                        self.referral_service.process_referral_signup(
                            referral_code, "organization", organization.id
                        )
                    except Exception as e:
                        print(f"Warning: Failed to process referral for organization {organization.id}: {str(e)}")
            except SQLAlchemyError:
                self.db.rollback()
                raise DatabaseOperationException("organization registration")

            return {
                "success": True,
                "message": "Organization registered successfully. A verification email has been sent to your email address. Please check your email and verify your account before logging in.",
                "email": email,
                "registration_type": RegistrationType.organization,
                "email_verification_required": True
            }

        else:
            raise InvalidRegistrationTypeException(registration_type)

    async def login(self, email: str, password: str) -> dict:
        """
        Authenticate user (individual or organization) and return access token.
        """
        # Try individual user first
        user = self.get_user_by_email(email)

        # If not found, try organization
        if not user:
            user = self.get_org_by_email(email)

        if not user or not verify_password(password, user.hashed_password):
            raise UnAuthorizedUserException(detail="Invalid email or password")

        # Check email verification
        if hasattr(user, 'is_email_verified') and not user.is_email_verified:
            raise UnAuthorizedUserException(detail="Email not verified. Please verify your email before logging in.")

        # Identify type
        if isinstance(user, Organization):
            registration_type = RegistrationType.organization
        else:
            registration_type = RegistrationType.individual

        return self._create_auth_response(user, registration_type, "Login successful")

    async def google_oauth_login(
        self, auth_code: str, reg_type: Optional[RegistrationType] = None
    ) -> dict:
        """
        Handles Google OAuth login/registration for individuals or organizations.
        """

        # Step 1: Exchange authorization code for access token
        token_url = "https://oauth2.googleapis.com/token"
        token_data = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "client_secret": settings.GOOGLE_CLIENT_SECRET,
            "code": auth_code,
            "grant_type": "authorization_code",
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
        }

        token_response = requests.post(token_url, data=token_data)
        token_json = token_response.json()

        if "error" in token_json:
            raise GoogleOAuthException()

        # Step 2: Get user info from Google
        user_info = requests.get(
            "https://www.googleapis.com/oauth2/v2/userinfo",
            headers={"Authorization": f"Bearer {token_json['access_token']}"},
        ).json()

        email = user_info.get("email")
        full_name = user_info.get("name", "")
        google_id = user_info.get("id")

        # Check if user exists (either as individual or organization)
        existing_user = self.get_user_by_email(email)
        if existing_user:
            return self._create_auth_response(
                existing_user, RegistrationType.individual, "Login successful"
            )

        existing_org = self.get_org_by_email(email)
        if existing_org:
            return self._create_auth_response(
                existing_org, RegistrationType.organization, "Login successful"
            )

        # Registration flow for new users
        if not reg_type:
            raise MissingParametersException()

        # Create new user based on registration type
        if reg_type == RegistrationType.individual:
            new_entity = User(
                id=str(uuid.uuid4()),
                email=email,
                full_name=full_name,
                hashed_password=None,
                is_oauth_user=True,
                oauth_provider="google",
                google_id=google_id,
                is_email_verified=True,
                email_verified_at=datetime.now(timezone.utc),
            )
            success_message = "User registered and logged in successfully via Google"

        elif reg_type == RegistrationType.organization:
            new_entity = Organization(
                id=str(uuid.uuid4()),
                email=email,
                organization_name=full_name,
                hashed_password=None,
                is_oauth_org=True,
                oauth_provider="google",
                google_id=google_id,
                is_email_verified=True,
                email_verified_at=datetime.now(timezone.utc),
            )
            success_message = "Organization registered and logged in successfully via Google"

        else:
            raise InvalidRegistrationTypeException(reg_type)

        # Save new entity to database
        try:
            self.db.add(new_entity)
            self.db.commit()
            self.db.refresh(new_entity)

            # Assign default subscription to individual users
            if reg_type == RegistrationType.individual:
                try:
                    default_plan = self.subscription_service.get_default_subscription_plan()
                    if not default_plan:
                        # Create default plan if it doesn't exist
                        default_plan = self.subscription_service.create_default_subscription_plan()

                    self.subscription_service.assign_subscription_to_user(new_entity.id, default_plan.id)
                except Exception as e:
                    # Log the error but don't fail registration
                    print(f"Warning: Failed to assign subscription to user {new_entity.id}: {str(e)}")

            # Assign default subscription to organizations
            elif reg_type == RegistrationType.organization:
                try:
                    default_plan = self.subscription_service.get_default_organization_subscription_plan()
                    if not default_plan:
                        # Create default organization plan if it doesn't exist
                        default_plan = self.subscription_service.create_default_organization_subscription_plan()

                    self.subscription_service.assign_subscription_to_organization(new_entity.id, default_plan.id)
                except Exception as e:
                    # Log the error but don't fail registration
                    print(f"Warning: Failed to assign subscription to organization {new_entity.id}: {str(e)}")

        except SQLAlchemyError:
            self.db.rollback()
            entity_type = (
                "individual" if reg_type == RegistrationType.individual else "organization"
            )
            raise DatabaseOperationException(f"{entity_type} OAuth registration")

        return self._create_auth_response(new_entity, reg_type, success_message)

    # Email Verification Methods (moved from EmailService to avoid circular imports)

    async def verify_email_otp(self, email: str, otp_code: str) -> dict:
        """
        Verify email OTP code and update user verification status

        Args:
            email: Email address
            otp_code: OTP code to verify

        Returns:
            dict: Verification result
        """
        try:
            # Verify OTP using email service
            result=self.email_service.verify_email_otp(email, otp_code)
            if result:
                # Update user's email verification status
                user = self.db.query(User).filter(User.email == email).first()
                if user:
                    user.is_email_verified = True
                    user.email_verified_at = datetime.now(timezone.utc)
                    self.db.commit()

                    return self._create_auth_response(user, RegistrationType.individual, "Email verified successfully")
                else:
                    # Check if it's an organization
                    org = self.db.query(Organization).filter(Organization.email == email).first()
                    if org:
                        org.is_email_verified = True
                        org.email_verified_at = datetime.now(timezone.utc)
                        self.db.commit()
                        return self._create_auth_response(
                            org, RegistrationType.organization, "Email verified successfully"
                        )
                    else:
                        raise Exception("User not found")

        except Exception as e:
            raise e

    async def resend_verification_otp(self, email: str) -> dict:
        """
        Resend OTP verification email

        Args:
            email: Email address

        Returns:
            dict: Result of the resend operation
        """
        try:
            # Get user information from database
            user_name = ""
            user_type = ""
            user = self.db.query(User).filter(User.email == email).first()
            if user:
                if user.is_email_verified:
                    raise Exception("Email already verified")
                user_name = user.full_name
                user_type = "candidate"
            else:
                org = self.db.query(Organization).filter(Organization.email == email).first()
                if org:
                    if org.is_email_verified:
                        raise Exception("Email already verified")
                    user_name = org.organization_name
                    user_type = "organization"
                else:
                    raise Exception("User not found")
                
            

            # Resend OTP using email service
            result = await self.email_service.resend_verification_otp(email, user_name, user_type)
            return result

        except Exception as e:
            raise e

    def get_verification_status(self, email: str) -> dict:
        """
        Get email verification status for a given email address

        Args:
            email: Email address to check

        Returns:
            dict: Verification status information
        """
        try:
            result = self.email_service.get_verification_status(email)
            return result

        except Exception as e:
            raise e


    async def update_user_job_descriptions(
        self,
        job_descriptions: UserUpdateJobDescriptionsSchema,
        current_user: dict,
    ) -> dict:
        """
        Update job descriptions for an individual user.

        Args:
            job_descriptions: The job descriptions to update
            current_user: The authenticated user

        Returns:
            A dictionary with the updated job descriptions

        Raises:
            UnAuthorizedUserException: If the user is not found
            MissingUpdateFieldsException: If no job descriptions are provided
            DatabaseOperationException: If there's a database error
        """
        user_id = current_user.get("user_id")

        # Fetch user by ID
        user = self.db.query(User).filter(User.id == user_id).first()

        if not user:
            raise UnAuthorizedUserException(detail="User not found")

        if not job_descriptions.standalone_job_descriptions or len(job_descriptions.standalone_job_descriptions) == 0:
            raise MissingUpdateFieldsException()

        # Create a new dictionary instead of modifying the existing one
        current_jd = user.standalone_job_descriptions or {}
        new_jd = dict(current_jd)  # Make a copy of the current job descriptions

        # Process each job description
        for jd in job_descriptions.standalone_job_descriptions:
            # Generate a unique ID for the job description
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            jd_id = f"jd_{timestamp}_{str(uuid4())[:8]}"

            # Create the job description entry with timestamp
            jd_entry = {
                "name": jd.name,
                "created_at": datetime.now().isoformat()
            }

            # Handle job description based on whether it's a link or text
            if jd.jd_link:
                # If a link is provided, store it and extract text from the file
                jd_entry["link"] = jd.jd_link

                jd_local_path = None
                try:
                    # Parse the S3 URL to get the key
                    jd_s3_key = await parse_s3_url(jd.jd_link)

                    # Download the file from S3
                    jd_local_path = download_file_from_s3(jd_s3_key, file_type="pdf")

                    # Extract text from the PDF
                    with open(jd_local_path, "rb") as file:
                        file_content = file.read()

                    jd_text = extract_text_from_pdf(file_content)

                    # Store the extracted text
                    jd_entry["description"] = jd_text

                except Exception as e:
                    # If there's an error extracting text, we'll raise the error after cleanup
                    error_detail = f"Error extracting text from JD: {str(e)}"
                    print(error_detail)
                    # We'll re-raise the exception after the file is deleted in the finally block
                    raise PDFTextExtractionException(f"Failed to extract text from job description: {str(e)}")
                finally:
                    # Always delete the local file, whether extraction succeeded or failed
                    if jd_local_path and os.path.exists(jd_local_path):
                        try:
                            os.remove(jd_local_path)
                            print(f"Successfully deleted local file: {jd_local_path}")
                        except Exception as e:
                            print(f"Warning: Failed to delete local file {jd_local_path}: {str(e)}")

            elif jd.jd_text:
                # If text is provided directly, store it as the description
                jd_entry["description"] = jd.jd_text
                # No link is stored in this case
                jd_entry["link"] = None

            # Add the job description to the dictionary
            new_jd[jd_id] = jd_entry

        # Update the user's job descriptions
        user.standalone_job_descriptions = new_jd

        try:
            # Explicitly flag the column as modified to force an update
            state = inspect(user)
            if state.attrs.standalone_job_descriptions.history.has_changes():
                print("SQLAlchemy detected changes to job descriptions")
            else:
                print("SQLAlchemy did NOT detect changes - forcing update")
                # Force the update by marking the attribute as modified
                state.attrs.standalone_job_descriptions.history.add_change(None, None, user.standalone_job_descriptions)

            # Add and commit
            self.db.add(user)
            self.db.flush()  # Flush changes to DB to check for errors before commit
            self.db.commit()

            # Verify the changes were saved by reloading the entity
            self.db.refresh(user)

        except Exception as e:
            self.db.rollback()
            raise DatabaseOperationException(f"updating user job descriptions: {str(e)}")

        return {
            "success": True,
            "message": "User job descriptions updated successfully",
            "data": {
                "standalone_job_descriptions": user.standalone_job_descriptions,  # Use the refreshed data from DB
            },
        }

    async def update_skills_and_jobs(
        self,
        skills_and_jobs: OrganizationUpdateSkillsJobsSchema,
        current_instance: dict,
    ) -> dict:
        """
        Update skills, job roles, and job descriptions for an organization.
        """
        org_id = current_instance.get("user_id")

        # Fetch organization by ID
        org = self.db.query(Organization).filter(Organization.id == org_id).first()

        if not org:
            raise OrganizationNotFoundException()

        if skills_and_jobs.skill_sets is None and skills_and_jobs.job_roles is None and skills_and_jobs.standalone_job_descriptions is None:
            raise MissingUpdateFieldsException()

        try:
            # Apply updates if provided
            if skills_and_jobs.skill_sets is not None:
                org.skill_sets = skills_and_jobs.skill_sets

            if skills_and_jobs.job_roles is not None:
                org.job_roles = skills_and_jobs.job_roles

            # Only process standalone job descriptions if explicitly provided
            if skills_and_jobs.standalone_job_descriptions is not None:
                await self._process_standalone_job_descriptions(org, skills_and_jobs.standalone_job_descriptions)

            # Check if setup is complete
            skill_sets_added = org.skill_sets is not None and len(org.skill_sets) > 0
            job_roles_added = org.job_roles is not None and len(org.job_roles) > 0

            # If both are added, set is_setup_complete to True
            if skill_sets_added and job_roles_added:
                org.is_setup_complete = True

            # Commit changes
            self.db.add(org)
            self.db.commit()
            self.db.refresh(org)

        except PDFTextExtractionException:
            # Re-raise PDF extraction exceptions as they are already properly formatted
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            print(f"Database error during organization update: {str(e)}")
            raise DatabaseOperationException("updating organization skills, jobs or job descriptions")

        return {
            "success": True,
            "message": "Organization profile updated successfully",
            "data": {
                "skill_sets": org.skill_sets,
                "job_roles": org.job_roles,
                "standalone_job_descriptions": org.standalone_job_descriptions,
            },
        }

    async def _process_standalone_job_descriptions(
        self,
        org: Organization,
        job_descriptions: List
    ) -> None:
        """
        Process standalone job descriptions for an organization.
        This method handles both PDF links and direct text input.
        """
        if not job_descriptions:
            return

        # Create a new dictionary instead of modifying the existing one
        current_jd = org.standalone_job_descriptions or {}
        new_jd = dict(current_jd)  # Make a copy of the current job descriptions

        # Process each standalone job description
        for jd_item in job_descriptions:
            # Generate a unique ID using timestamp for better organization
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            jd_id = f"{timestamp}_{str(uuid.uuid4())[:8]}"

            # Add the new standalone job description to the new dictionary with basic info
            new_jd[jd_id] = {
                "name": jd_item.name,
                "created_at": datetime.now().isoformat()
            }

            # Handle job description based on whether it's a link or text
            if jd_item.jd_link:
                # If a link is provided, store it and extract text from the file
                new_jd[jd_id]["link"] = jd_item.jd_link
                await self._extract_text_from_pdf_link(jd_item.jd_link, new_jd[jd_id])
            elif jd_item.jd_text:
                # If text is provided directly, store it as the description
                new_jd[jd_id]["description"] = jd_item.jd_text
                new_jd[jd_id]["link"] = None

        # Update the organization's job descriptions
        org.standalone_job_descriptions = new_jd

    async def _extract_text_from_pdf_link(self, pdf_link: str, jd_entry: dict) -> None:
        """
        Extract text from a PDF file stored in S3.
        """
        jd_local_path = None
        try:
            # Parse the S3 URL to get the key
            jd_s3_key = await parse_s3_url(pdf_link)

            # Download the file from S3
            jd_local_path = download_file_from_s3(jd_s3_key, file_type="pdf")

            # Extract text from the PDF
            with open(jd_local_path, "rb") as file:
                file_content = file.read()

            jd_text = extract_text_from_pdf(file_content)

            # Store the extracted text
            jd_entry["description"] = jd_text

        except Exception as e:
            error_detail = f"Error extracting text from JD: {str(e)}"
            print(error_detail)
            raise PDFTextExtractionException(f"Failed to extract text from job description: {str(e)}")
        finally:
            # Always delete the local file, whether extraction succeeded or failed
            if jd_local_path and os.path.exists(jd_local_path):
                try:
                    os.remove(jd_local_path)
                    print(f"Successfully deleted local file: {jd_local_path}")
                except Exception as e:
                    print(f"Warning: Failed to delete local file {jd_local_path}: {str(e)}")

    def get_entity_resources(self, entity_id: str, entity_type: RegistrationType) -> dict:
        """
        Generic method to get all resources for either a user or organization.

        Args:
            entity_id: The user or organization ID
            entity_type: The type of entity (individual or organization)

        Returns:
            A dictionary containing resources (job descriptions, and for organizations: skill sets and job roles)

        Raises:
            OrganizationNotFoundException: If the organization is not found
            UnAuthorizedUserException: If the user is not found
        """
        # Get the entity (user or organization)
        entity = self._get_entity(entity_id, entity_type)

        raw_jds: dict = entity.standalone_job_descriptions or {}

        # Build a list of JDs:
        job_descriptions_list = [{"id": jd_id, **jd_data} for jd_id, jd_data in raw_jds.items()]

        payload = {"job_descriptions": job_descriptions_list}

        # If it’s an organization, tack on skill_sets & job_roles
        if entity_type == RegistrationType.organization:
            payload["skill_sets"] = entity.skill_sets or []
            payload["job_roles"] = entity.job_roles or []

        return {"success": True, "message": "Resources retrieved successfully", "data": payload}

    def get_job_description_by_entity(self, entity_id: str, jd_id: str, entity_type: RegistrationType) -> Optional[dict]:
        """
        Generic method to get a standalone job description by ID for either a user or organization.

        Args:
            entity_id: The user or organization ID
            jd_id: The standalone job description ID
            entity_type: The type of entity (individual or organization)

        Returns:
            A dictionary with the job description or None if not found

        Raises:
            OrganizationNotFoundException: If the organization is not found
            UnAuthorizedUserException: If the user is not found
        """
        # Get the entity (user or organization)
        entity = self._get_entity(entity_id, entity_type)

        # Return None if no job descriptions or the specific job description is not found
        if not entity.standalone_job_descriptions or jd_id not in entity.standalone_job_descriptions:
            return None

        return entity.standalone_job_descriptions[jd_id]

    def _get_entity(self, entity_id: str, entity_type: RegistrationType) -> Union[User, Organization]:
        """
        Helper method to get either a user or organization by ID.

        Args:
            entity_id: The user or organization ID
            entity_type: The type of entity (individual or organization)

        Returns:
            The user or organization entity

        Raises:
            OrganizationNotFoundException: If the organization is not found
            UnAuthorizedUserException: If the user is not found
        """
        if entity_type == RegistrationType.organization:
            entity = self.db.query(Organization).filter(Organization.id == entity_id).first()
            if not entity:
                raise OrganizationNotFoundException()
        else:
            entity = self.db.query(User).filter(User.id == entity_id).first()
            if not entity:
                raise UnAuthorizedUserException(detail="User not found")

        return entity

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get a user by email"""
        query = select(User).where(func.lower(User.email) == email.lower())
        try:
            result = self.db.execute(query)
            return result.scalar_one_or_none()
        except SQLAlchemyError:
            raise DatabaseOperationException(operation="fetching user")

    def get_org_by_email(self, email: str) -> Optional[Organization]:
        """Get a org by email"""
        query = select(Organization).where(func.lower(Organization.email) == email.lower())
        try:
            result = self.db.execute(query)
            return result.scalar_one_or_none()
        except SQLAlchemyError:
            raise DatabaseOperationException(operation="fetching org")

    def get_user_by_id(self, id: str) -> dict:
        """Get a user by their ID"""
        query = select(User).where(User.id == id)
        try:
            user = self.db.execute(query).scalar_one_or_none()
            if not user:
                raise UnAuthorizedUserException(detail="User not found")

            return self._create_info_response(
                user, RegistrationType.individual, "User retrieved successfully"
            )
        except SQLAlchemyError as e:
            print(e)
            raise DatabaseOperationException(operation="fetching user by ID")

    def get_user_profile_by_email(self,email:str)->dict:
        query = select(User).where(func.lower(User.email) == email.lower())
        try:
            result = self.db.execute(query)
            user = result.scalar_one_or_none()
            if not user:
                return []
            return {
                "email": user.email,
                "full_name": user.full_name,
                "position": user.position,
                "location": user.location,
                "phone_number": user.phone_number,
                "bio": user.bio,
                "website": user.website,
                "instagram": user.instagram,
                "tiktok": user.tiktok,
                "youtube": user.youtube,
                "twitter": user.twitter,
                "linkedin": user.linkedin,
                "profile_picture": user.profile_picture,
                "employment_history": user.employment_history,
                "skills": user.skills,
            }
        except SQLAlchemyError:
            raise DatabaseOperationException(operation="fetching user profile")

    def get_org_by_id(self, id: str) -> dict:
        """Get a organization by their ID"""
        query = select(Organization).where(Organization.id == id)
        try:
            organization = self.db.execute(query).scalar_one_or_none()
            if not organization:
                raise OrganizationNotFoundException()

            return self._create_info_response(
                organization, RegistrationType.organization, "Organization retrieved successfully"
            )
        except SQLAlchemyError:
            raise DatabaseOperationException(operation="fetching org by ID")

    def _create_auth_response(self, entity, reg_type: RegistrationType, message: str) -> dict:
        """Helper method to create consistent auth response objects"""
        response = {
            "success": True,
            "message": message,
            "access_token": create_access_token(
                data={"email": entity.email, "user_id": entity.id, "registration_type": reg_type}
            ),
            "token_type": "bearer",
            "registration_type": reg_type,
            "email": entity.email,
            "created_at": entity.created_at.isoformat(),
        }

        # Add type-specific fields
        if reg_type == RegistrationType.individual:
            response.update(
                {
                    "user_id": entity.id,
                    "full_name": entity.full_name,
                }
            )
        else:
            response.update(
                {
                    "organization_id": entity.id,
                    "organization_name": entity.organization_name,
                }
            )

        return response

    async def update_user_profile(self, user_id: str, profile_data: UserProfileUpdate) -> dict:
        """
        Update a user's profile information.

        Args:
            user_id: The ID of the user to update
            profile_data: The profile data to update

        Returns:
            A dictionary with the updated user profile information

        Raises:
            UnAuthorizedUserException: If the user is not found
            ProfileUpdateException: If there's an error updating the profile
            DatabaseOperationException: If there's a database error
        """
        # Fetch user by ID
        query = select(User).where(User.id == user_id)
        try:
            user = self.db.execute(query).scalar_one_or_none()
            if not user:
                raise UnAuthorizedUserException(detail="User not found")

            # Update profile fields if provided
            update_fields = profile_data.model_dump(exclude_unset=True, exclude_none=True)

            if not update_fields:
                raise ProfileUpdateException("No profile fields provided for update")

            # Special handling for resume_link field
            if 'resume_link' in update_fields:
                resume_link = update_fields['resume_link']
                if resume_link:  # Only validate if not None/empty
                    try:
                        # Validate S3 URL format
                        s3_key = await parse_s3_url(resume_link)

                        # Check if S3 object exists and is accessible
                        s3_client.head_object(Bucket=S3_BUCKET_NAME, Key=s3_key)
                    except ValueError as e:
                        raise InvalidS3UrlException(detail=str(e))
                    except Exception as e:
                        error_msg = str(e)
                        if "404" in error_msg or "NoSuchKey" in error_msg:
                            raise S3ObjectNotAccessibleException(detail="Resume file not found in S3")
                        else:
                            raise S3ObjectNotAccessibleException(detail="Resume file is not accessible")

            # Special handling for employment history and skills
            if 'employment_history' in update_fields:
                employment_data = update_fields['employment_history']
                if employment_data is not None:
                    # Process employment history entries
                    employment_entries = []
                    for entry in employment_data:
                        # If entry is a dict (from EmploymentEntry), convert it
                        if isinstance(entry, dict):
                            entry_data = entry
                        else:
                            # If it's an EmploymentEntry object, convert to dict
                            entry_data = entry.model_dump() if hasattr(entry, 'model_dump') else entry.__dict__

                        # Generate unique ID if not provided
                        entry_id = entry_data.get('id') or f"emp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid4())[:8]}"

                        employment_entry = {
                            "id": entry_id,
                            "company": entry_data.get('company'),
                            "position": entry_data.get('position'),
                            "start_date": entry_data.get('start_date'),
                            "end_date": entry_data.get('end_date'),
                            "current": entry_data.get('current', False),
                            "description": entry_data.get('description'),
                            "location": entry_data.get('location'),
                            "created_at": datetime.now().isoformat(),
                            "updated_at": datetime.now().isoformat()
                        }
                        employment_entries.append(employment_entry)

                    # Replace the entire employment history
                    update_fields['employment_history'] = employment_entries

            if 'skills' in update_fields:
                skills_data = update_fields['skills']
                if skills_data is not None:
                    # Clean and deduplicate skills
                    cleaned_skills = list(set([skill.strip() for skill in skills_data if skill.strip()]))
                    # Replace the entire skills list
                    update_fields['skills'] = cleaned_skills

            # Update user object with all fields (including processed employment_history and skills)
            for field, value in update_fields.items():
                setattr(user, field, value)

            # Commit changes to database
            self.db.commit()
            self.db.refresh(user)

            return self._create_profile_response(user, "User profile updated successfully")

        except ProfileUpdateException as e:
            raise e
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException(operation="updating user profile")
        except InvalidS3UrlException as e:
            self.db.rollback()
            raise InvalidS3UrlException(detail=str(e))
        except S3ObjectNotAccessibleException as e:
            self.db.rollback()
            raise S3ObjectNotAccessibleException(detail=str(e))
        except Exception:
            self.db.rollback()
            raise ProfileUpdateException("Unexpected error during profile update")

    async def update_organization_profile(self, org_id: str, profile_data: UserProfileUpdate) -> dict:
        """
        Update an organization's profile information.

        Args:
            org_id: The ID of the organization to update
            profile_data: The profile data to update

        Returns:
            A dictionary with the updated organization profile information

        Raises:
            OrganizationNotFoundException: If the organization is not found
            ProfileUpdateException: If there's an error updating the profile
            DatabaseOperationException: If there's a database error
        """
        # Fetch organization by ID
        query = select(Organization).where(Organization.id == org_id)
        try:
            org = self.db.execute(query).scalar_one_or_none()
            if not org:
                raise OrganizationNotFoundException()

            # Update profile fields if provided
            update_fields = profile_data.model_dump(exclude_unset=True, exclude_none=True)

            if not update_fields:
                raise ProfileUpdateException("No profile fields provided for update")

            # Update organization object with the provided fields
            for field, value in update_fields.items():
                setattr(org, field, value)

            # Commit changes to database
            self.db.commit()
            self.db.refresh(org)

            return self._create_organization_profile_response(org, "Organization profile updated successfully")

        except ProfileUpdateException as e:
            raise e
        except SQLAlchemyError:
            self.db.rollback()
            raise DatabaseOperationException(operation="updating organization profile")
        except Exception:
            self.db.rollback()
            raise ProfileUpdateException("Unexpected error during organization profile update")

    def _create_profile_response(self, user: User, message: str) -> dict:
        """
        Create a response object for user profile operations.

        Args:
            user: The user object
            message: A message to include in the response

        Returns:
            A dictionary with the user's profile information
        """
        # Use actual user skills instead of random generation
        user_skills = user.skills if user.skills else []
        # For backward compatibility, if no skills are set, use empty list
        skill_sets = user_skills
        # Generate rating based on number of skills (more skills = higher rating)
        if len(user_skills) > 0:
            base_rating = 3.0 + (len(user_skills) * 0.5)  # 3.0 base + 0.5 per skill
            rating = min(round(base_rating, 1), 6.5)  # Cap at 6.5
        else:
            rating = 3.0  # Default rating if no skills

        return {
            "success": True,
            "message": message,
            "user_id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "position": user.position,
            "location": user.location,
            "phone_number": user.phone_number,
            "bio": user.bio,
            "website": user.website,
            "instagram": user.instagram,
            "tiktok": user.tiktok,
            "youtube": user.youtube,
            "twitter": user.twitter,
            "linkedin": user.linkedin,
            "profile_picture": user.profile_picture,
            "resume_link": user.resume_link,
            "employment_history": user.employment_history or [],
            "skills": user_skills,
            "rating": rating,
            "created_at": user.created_at.isoformat(),
            "updated_at": user.updated_at.isoformat(),
        }

    def _create_organization_profile_response(self, org: Organization, message: str) -> dict:
        """
        Create a response object for organization profile operations.

        Args:
            org: The organization object
            message: A message to include in the response

        Returns:
            A dictionary with the organization's profile information
        """
        return {
            "success": True,
            "message": message,
            "organization_id": org.id,
            "email": org.email,
            "organization_name": org.organization_name,
            "position": org.position,
            "location": org.location,
            "phone_number": org.phone_number,
            "bio": org.bio,
            "website": org.website,
            "instagram": org.instagram,
            "tiktok": org.tiktok,
            "youtube": org.youtube,
            "twitter": org.twitter,
            "linkedin": org.linkedin,
            "profile_picture": org.profile_picture,
            "skill_sets": org.skill_sets,
            "job_roles": org.job_roles,
            "is_setup_complete": org.is_setup_complete,
            "created_at": org.created_at.isoformat(),
            "updated_at": org.updated_at.isoformat(),
        }

    def _create_info_response(self, entity, reg_type: RegistrationType, message: str) -> dict:
        """Helper method to create consistent info response objects without auth tokens"""
        response = {
            "success": True,
            "message": message,
            "registration_type": reg_type,
            "email": entity.email,
            "created_at": entity.created_at.isoformat(),
            "updated_at": entity.updated_at.isoformat(),
        }

        # Add type-specific fields
        if reg_type == RegistrationType.individual:
            # Use actual user skills instead of random generation
            user_skills = entity.skills if entity.skills else []
            # Generate rating based on number of skills (more skills = higher rating)
            if len(user_skills) > 0:
                base_rating = 3.0 + (len(user_skills) * 0.5)  # 3.0 base + 0.5 per skill
                rating = min(round(base_rating, 1), 6.5)  # Cap at 6.5
            else:
                rating = 3.0  # Default rating if no skills

            user_data = {
                "user_id": entity.id,
                "email": entity.email,
                "full_name": entity.full_name,
                "position": entity.position,
                "location": entity.location,
                "phone_number": entity.phone_number,
                "bio": entity.bio,
                "website": entity.website,
                "instagram": entity.instagram,
                "tiktok": entity.tiktok,
                "youtube": entity.youtube,
                "twitter": entity.twitter,
                "linkedin": entity.linkedin,
                "profile_picture": entity.profile_picture,
                "resume_link": entity.resume_link,
                "employment_history": entity.employment_history,
                "skills": user_skills,
                "rating": rating,  # Use calculated rating based on skills
            }
            response.update(user_data)
        else:
            response.update(
                {
                    "organization_id": entity.id,
                    "organization_name": entity.organization_name,
                    "position": entity.position,
                    "location": entity.location,
                    "phone_number": entity.phone_number,
                    "bio": entity.bio,
                    "website": entity.website,
                    "instagram": entity.instagram,
                    "tiktok": entity.tiktok,
                    "youtube": entity.youtube,
                    "twitter": entity.twitter,
                    "linkedin": entity.linkedin,
                    "profile_picture": entity.profile_picture,
                    "skill_sets": entity.skill_sets,
                    "job_roles": entity.job_roles,
                    "is_setup_complete": entity.is_setup_complete,
                }
            )

        return response

    def get_paginated_user_profiles(
        self,
        skip: int = 0,
        limit: int = 20,
        top_profiles: bool = False,
        skill_filter: Optional[str] = None,
        sort_by: str = 'created_at'
    ) -> PaginatedUserProfilesResponse:
        """
        Get paginated user profiles with optional filtering and sorting.
        
        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return
            top_profiles: Placeholder for top profiles filtering (not implemented)
            skill_filter: Placeholder for skill-based filtering (not implemented)
            sort_by: Placeholder for sorting options (defaults to created_at)
            
        Returns:
            PaginatedUserProfilesResponse with user profiles and pagination metadata
            
        Raises:
            DatabaseOperationException: If there's a database error
        """
        try:
            # Get total count of active users
            total_count_query = select(func.count(User.id)).where(User.is_active == True)
            total_users = self.db.execute(total_count_query).scalar()

            # Calculate pagination metadata
            total_pages = math.ceil(total_users / limit) if total_users > 0 else 0
            current_page = (skip // limit) + 1

            # Get paginated user profiles
            profiles_query = (
                select(User)
                .where(User.is_active == True)
                .order_by(User.created_at.desc())
                .offset(skip)
                .limit(limit)
            )

            users = self.db.execute(profiles_query).scalars().all()

            # Convert users to UserProfilePublic schema
            user_profiles = []
            for user in users:
                # Use actual user skills instead of random generation
                user_skills = user.skills if user.skills else []
                # Generate rating based on number of skills (more skills = higher rating)
                if len(user_skills) > 0:
                    base_rating = 3.0 + (len(user_skills) * 0.5)  # 3.0 base + 0.5 per skill
                    rating = min(round(base_rating, 1), 6.5)  # Cap at 6.5
                else:
                    rating = 3.0  # Default rating if no skills

                profile = UserProfilePublic(
                    id=user.id,
                    email=user.email,
                    full_name=user.full_name,
                    position=user.position,
                    location=user.location,
                    bio=user.bio,
                    website=user.website,
                    profile_picture=user.profile_picture,
                    instagram=user.instagram,
                    tiktok=user.tiktok,
                    youtube=user.youtube,
                    twitter=user.twitter,
                    linkedin=user.linkedin,
                    resume_link=user.resume_link,
                    employment_history=user.employment_history,
                    skills=user_skills,
                    rating=rating,
                    created_at=user.created_at.isoformat(),
                )
                user_profiles.append(profile)

            # Calculate pagination metadata
            current_page_length = len(user_profiles)

            pagination_metadata = ProfilePaginationMetadata(
                total_users=total_users,
                total_pages=total_pages,
                current_page=current_page,
                current_page_length=current_page_length,
                page_size=limit
            )

            # Create response (matching interview endpoints pattern)
            response = PaginatedUserProfilesResponse(
                status="success",
                message="User profiles retrieved successfully",
                data=user_profiles,
                pagination=pagination_metadata
            )

            return response

        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"retrieving paginated user profiles: {str(e)}")
        except Exception as e:
            raise DatabaseOperationException(f"unexpected error retrieving user profiles: {str(e)}")

    def get_paginated_organizations(
        self,
        skip: int = 0,
        limit: int = 20,
        top10: bool = False,
        recommended: bool = False,
        skill_filter: Optional[str] = None,
        location_filter: Optional[str] = None,
        sort_by: str = 'created_at'
    ) -> PaginatedOrganizationsResponse:
        """
        Get paginated organizations with advanced filtering capabilities.
        
        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return
            top10: Filter for top 10 organizations by rating (placeholder)
            recommended: Filter for recommended organizations (placeholder)
            skill_filter: Filter by skill sets (placeholder)
            location_filter: Filter by location (placeholder)
            sort_by: Sorting options (defaults to created_at)
            
        Returns:
            PaginatedOrganizationsResponse with organization profiles and pagination metadata
            
        Raises:
            DatabaseOperationException: If there's a database error
        """
        try:
            # Build base query for active organizations
            base_query = select(Organization).where(Organization.is_active == True)

            # Apply filters (placeholder implementations for now)
            if skill_filter:
                # Placeholder: In future, filter by skill_sets containing the skill
                pass

            if location_filter:
                # Placeholder: In future, filter by location containing the location string
                pass

            # Get total count of filtered organizations
            total_count_query = select(func.count(Organization.id)).where(Organization.is_active == True)
            total_organizations = self.db.execute(total_count_query).scalar()

            # Apply special filters
            if top10:
                # Placeholder: Limit to top 10 by rating (for now, just limit to 10)
                limit = min(limit, 10)

            if recommended:
                # Placeholder: Apply recommendation algorithm (for now, just order by setup completion)
                base_query = base_query.where(Organization.is_setup_complete == True)

            # Calculate pagination metadata
            total_pages = math.ceil(total_organizations / limit) if total_organizations > 0 else 0
            current_page = (skip // limit) + 1

            # Apply sorting and pagination
            if sort_by == 'name':
                base_query = base_query.order_by(Organization.organization_name.asc())
            elif sort_by == 'rating':
                # Placeholder: For now, order by created_at desc (newest first)
                base_query = base_query.order_by(Organization.created_at.desc())
            else:  # default to created_at
                base_query = base_query.order_by(Organization.created_at.desc())

            # Apply pagination
            organizations_query = base_query.offset(skip).limit(limit)
            organizations = self.db.execute(organizations_query).scalars().all()

            # Convert organizations to OrganizationProfilePublic schema
            organization_profiles = []
            for org in organizations:
                # Get actual interview count from database
                interview_count_query = select(func.count(Interview.id)).where(Interview.organization_id == org.id)
                total_interviews = self.db.execute(interview_count_query).scalar() or 0

                profile = OrganizationProfilePublic(
                    id=org.id,
                    email=org.email,
                    organization_name=org.organization_name,
                    position=org.position,
                    location=org.location,
                    bio=org.bio,
                    website=org.website,
                    profile_picture=org.profile_picture,
                    instagram=org.instagram,
                    tiktok=org.tiktok,
                    youtube=org.youtube,
                    twitter=org.twitter,
                    linkedin=org.linkedin,
                    total_interviews=total_interviews,
                    created_at=org.created_at.isoformat(),
                )
                organization_profiles.append(profile)

            # Calculate pagination metadata
            current_page_length = len(organization_profiles)

            pagination_metadata = OrganizationPaginationMetadata(
                total_organizations=total_organizations,
                total_pages=total_pages,
                current_page=current_page,
                current_page_length=current_page_length,
                page_size=limit
            )

            # Create response
            response = PaginatedOrganizationsResponse(
                status="success",
                message="Organizations retrieved successfully",
                data=organization_profiles,
                pagination=pagination_metadata
            )

            return response

        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"retrieving paginated organizations: {str(e)}")
        except Exception as e:
            raise DatabaseOperationException(f"unexpected error retrieving organizations: {str(e)}")

    def get_public_user_profile(self, user_id: str) -> dict:
        """
        Get public profile information for a specific user without authentication.
        
        Args:
            user_id: The ID of the user to retrieve
            
        Returns:
            A dictionary containing the public user profile information
            
        Raises:
            UnAuthorizedUserException: If the user is not found or not active
            DatabaseOperationException: If there's a database error
        """
        try:
            query = select(User).where(User.id == user_id, User.is_active == True)
            user = self.db.execute(query).scalar_one_or_none()

            if not user:
                raise UnAuthorizedUserException("User not found or not active")

            # Use actual user skills instead of random generation
            user_skills = user.skills if user.skills else []
            # Generate rating based on number of skills (more skills = higher rating)
            if len(user_skills) > 0:
                base_rating = 3.0 + (len(user_skills) * 0.5)  # 3.0 base + 0.5 per skill
                rating = min(round(base_rating, 1), 6.5)  # Cap at 6.5
            else:
                rating = 3.0  # Default rating if no skills

            # Fetch past completed interviews for this user (case-insensitive email matching)
            past_interviews_query = select(Interview).where(
                func.lower(Interview.candidate_email) == func.lower(user.email),
                Interview.is_completed == True
            ).order_by(Interview.created_at.desc())

            past_interviews_result = self.db.execute(past_interviews_query).scalars().all()

            # Format past interviews data
            past_interviews = []
            for interview in past_interviews_result:
                # Get organization name if interview was scheduled by organization
                company_name = "Unknown Company"
                if interview.organization_id:
                    org_query = select(Organization).where(Organization.id == interview.organization_id)
                    org = self.db.execute(org_query).scalar_one_or_none()
                    if org:
                        company_name = org.organization_name
                elif interview.scheduled_by_user_id:
                    # # If scheduled by user, try to get user's name or company from their profile
                    # scheduler_query = select(User).where(User.id == interview.scheduled_by_user_id)
                    # scheduler = self.db.execute(scheduler_query).scalar_one_or_none()
                    # if scheduler and scheduler.position:
                    #     company_name = scheduler.position
                    # else:
                    company_name = "Self Interview"

                interview_data = {
                    "id": interview.id,
                    "company": company_name,
                    "position": interview.job_role,
                    "date": interview.created_at.strftime("%Y-%m-%d"),
                    "skills_tested": interview.skill_sets or [],
                    "experience_level": interview.experience_level,
                    "duration_minutes": interview.interview_duration,
                    "status": "completed"
                }

                # Add feedback and scores if available
                if interview.feedback_result:
                    interview_data["feedback"] = interview.feedback_result

                if interview.technical_evaluation_result:
                    interview_data["technical_evaluation_result"] = (
                        interview.technical_evaluation_result
                    );
                    # tech_eval = interview.technical_evaluation_result
                    # if isinstance(tech_eval, dict) and "overall_score" in tech_eval:
                    #     interview_data["score"] = tech_eval["overall_score"]

                if interview.recommendation_result:
                    interview_data["recommendation_result"] = interview.recommendation_result
                # rec_result = interview.recommendation_result
                # if isinstance(rec_result, dict) and "recommendation" in rec_result:
                #     interview_data["result"] = rec_result["recommendation"]

                past_interviews.append(interview_data)

            # Return public profile data (excluding sensitive information)
            return {
                "status": "success",
                "message": "Public user profile retrieved successfully",
                "data": {
                    "id": user.id,
                    "full_name": user.full_name,
                    "position": user.position,
                    "location": user.location,
                    "bio": user.bio,
                    "website": user.website,
                    "profile_picture": user.profile_picture,
                    "instagram": user.instagram,
                    "tiktok": user.tiktok,
                    "youtube": user.youtube,
                    "twitter": user.twitter,
                    "linkedin": user.linkedin,
                    "resume_link": user.resume_link,
                    "employment_history": user.employment_history or [],
                    "skills": user_skills or [],
                    "past_interviews": past_interviews,
                    "rating": rating,
                    "created_at": user.created_at.isoformat(),
                }
            }

        except UnAuthorizedUserException:
            raise
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"retrieving public user profile: {str(e)}")
        except Exception as e:
            raise DatabaseOperationException(f"unexpected error retrieving user profile: {str(e)}")

    def get_public_organization_profile(self, org_id: str) -> dict:
        """
        Get public profile information for a specific organization without authentication.
        
        Args:
            org_id: The ID of the organization to retrieve
            
        Returns:
            A dictionary containing the public organization profile information
            
        Raises:
            OrganizationNotFoundException: If the organization is not found or not active
            DatabaseOperationException: If there's a database error
        """
        try:
            query = select(Organization).where(Organization.id == org_id, Organization.is_active == True)
            org = self.db.execute(query).scalar_one_or_none()

            if not org:
                raise OrganizationNotFoundException("Organization not found or not active")

            # Get actual interview count from database
            interview_count_query = select(func.count(Interview.id)).where(Interview.organization_id == org.id)
            total_interviews = self.db.execute(interview_count_query).scalar() or 0

            # Return public profile data (excluding sensitive information)
            return {
                "status": "success",
                "message": "Public organization profile retrieved successfully",
                "data": {
                    "id": org.id,
                    "organization_name": org.organization_name,
                    "position": org.position,
                    "location": org.location,
                    "bio": org.bio,
                    "website": org.website,
                    "profile_picture": org.profile_picture,
                    "instagram": org.instagram,
                    "tiktok": org.tiktok,
                    "youtube": org.youtube,
                    "twitter": org.twitter,
                    "linkedin": org.linkedin,
                    "total_interviews": total_interviews,
                    "created_at": org.created_at.isoformat(),
                }
            }

        except OrganizationNotFoundException:
            raise
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"retrieving public organization profile: {str(e)}")
        except Exception as e:
            raise DatabaseOperationException(f"unexpected error retrieving organization profile: {str(e)}")
