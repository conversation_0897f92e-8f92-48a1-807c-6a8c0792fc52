from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Boolean,
    Integer,
    JSON,
    Numeric,
    Enum as SQLAlchemyEnum,
)
from sqlalchemy.orm import relationship
from enum import Enum
from app.models.base import Base


class PlanType(str, Enum):
    individual = "individual"
    organization = "organization"


class SubscriptionPlan(Base):
    __tablename__ = "subscription_plans"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    plan_type = Column(SQLAlchemyEnum(PlanType), nullable=False)

    # Common limits for both individual and organization
    interview_limit = Column(Integer, nullable=False)
    jobs_limit = Column(Integer, nullable=False)
    jd_limit = Column(Integer, nullable=False)

    # Individual-specific limits
    resume_limit = Column(Integer, nullable=True)  # Only for individual plans

    # Organization-specific limits
    candidate_suitability_limit = Column(Integer, nullable=True)  # Only for organization plans

    price = Column(Numeric(10, 2), default=0.00)
    is_active = Column(Boolean, default=True)
    features = Column(JSON, nullable=True)  # For future extensibility
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user_subscriptions = relationship(
        "UserSubscription",
        back_populates="subscription_plan",
        cascade="all, delete-orphan"
    )
    
    organization_subscriptions = relationship(
        "OrganizationSubscription",
        back_populates="subscription_plan",
        cascade="all, delete-orphan"
    )

    # Payment orders relationship
    orders = relationship("Order", back_populates="subscription_plan")

    def __repr__(self):
        return f"<SubscriptionPlan {self.name} ({self.plan_type}) - {self.interview_limit} interviews>"