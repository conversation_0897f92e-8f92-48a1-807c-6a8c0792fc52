from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Text,
    ForeignKey,
)
from app.models.base import Base
from sqlalchemy.orm import relationship


class Resume(Base):
    __tablename__ = "resumes"

    id = Column(String, primary_key=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    title = Column(String, nullable=False)  # e.g., "Software Engineer Resume", "Data Scientist Resume"
    content = Column(Text, nullable=False)  # Resume content in JSON format
    template = Column(String, nullable=False, default="professional")  # Template type: professional, modern, academic
    pdf_s3_url = Column(String, nullable=True)  # S3 URL for the generated PDF
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship back to user
    user = relationship("User", back_populates="resumes")

    def __repr__(self):
        return f"<Resume {self.title} for User {self.user_id}>"