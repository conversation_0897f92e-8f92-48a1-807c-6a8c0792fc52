"""
Webhook Event Model

This module defines the WebhookEvent model for tracking Razorpay webhook events.
Webhook events ensure idempotency and provide audit trails for payment processing.
"""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, JSON, Boolean, Text, Enum as SQLEnum
from enum import Enum
from app.models.base import Base


class WebhookEventType(str, Enum):
    """Webhook event type enumeration."""
    PAYMENT_AUTHORIZED = "payment.authorized"
    PAYMENT_CAPTURED = "payment.captured"
    PAYMENT_FAILED = "payment.failed"
    ORDER_PAID = "order.paid"
    REFUND_CREATED = "refund.created"
    REFUND_PROCESSED = "refund.processed"
    SUBSCRIPTION_ACTIVATED = "subscription.activated"
    SUBSCRIPTION_CANCELLED = "subscription.cancelled"


class WebhookEventStatus(str, Enum):
    """Webhook event processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    IGNORED = "ignored"


class WebhookEvent(Base):
    """
    WebhookEvent model for Razorpay webhook events.
    
    Tracks webhook events received from Razorpay to ensure idempotency
    and provide comprehensive audit trails for payment processing.
    """
    __tablename__ = "webhook_events"

    id = Column(String, primary_key=True)
    
    # Webhook identification
    razorpay_event_id = Column(String, unique=True, nullable=False, index=True)
    event_type = Column(SQLEnum(WebhookEventType), nullable=False)
    
    # Event processing
    status = Column(SQLEnum(WebhookEventStatus), default=WebhookEventStatus.PENDING, nullable=False)
    processing_attempts = Column(JSON, nullable=True)  # Track processing attempts
    
    # Event data
    event_payload = Column(JSON, nullable=False)  # Full webhook payload
    signature = Column(String, nullable=True)  # Webhook signature for verification
    
    # Related entities
    entity_type = Column(String, nullable=True)  # payment, order, refund, etc.
    entity_id = Column(String, nullable=True)  # ID of the related entity
    
    # Processing results
    processing_result = Column(JSON, nullable=True)  # Result of processing
    error_message = Column(Text, nullable=True)  # Error message if processing failed
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    processed_at = Column(DateTime, nullable=True)  # When event was successfully processed
    received_at = Column(DateTime, default=datetime.utcnow, nullable=False)  # When webhook was received

    def __repr__(self) -> str:
        return f"<WebhookEvent(id='{self.id}', event_type='{self.event_type}', status='{self.status}')>"

    def to_dict(self) -> dict:
        """Convert webhook event to dictionary representation."""
        return {
            "id": self.id,
            "razorpay_event_id": self.razorpay_event_id,
            "event_type": self.event_type.value if self.event_type else None,
            "status": self.status.value if self.status else None,
            "processing_attempts": self.processing_attempts,
            "event_payload": self.event_payload,
            "signature": self.signature,
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "processing_result": self.processing_result,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "received_at": self.received_at.isoformat() if self.received_at else None,
        }

    def mark_as_processed(self, result: dict = None) -> None:
        """Mark the webhook event as successfully processed."""
        self.status = WebhookEventStatus.PROCESSED
        self.processed_at = datetime.utcnow()
        if result:
            self.processing_result = result

    def mark_as_failed(self, error_message: str) -> None:
        """Mark the webhook event as failed."""
        self.status = WebhookEventStatus.FAILED
        self.error_message = error_message

    def add_processing_attempt(self, attempt_info: dict) -> None:
        """Add a processing attempt to the event."""
        if not self.processing_attempts:
            self.processing_attempts = []
        self.processing_attempts.append({
            **attempt_info,
            "timestamp": datetime.utcnow().isoformat()
        })
