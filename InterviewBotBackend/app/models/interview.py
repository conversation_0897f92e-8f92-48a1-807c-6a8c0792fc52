from datetime import datetime, timezone
from enum import Enum
from sqlalchemy import (
    Column,
    String,
    DateTime,
    ForeignKey,
    Boolean,
    Integer,
    JSON,
    CheckConstraint,
    Enum as SQLAlchemyEnum,
)
from sqlalchemy.orm import relationship
from app.models.base import Base
from sqlalchemy.types import TypeDecorator


class ScheduledBy(Enum):
    ORGANIZATION = "organization"
    USER = "user"


# Custom SQLAlchemy type to ensure timezone awareness
class TZDateTime(TypeDecorator):
    impl = DateTime(timezone=True)
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            if value.tzinfo is None:
                raise ValueError("Timezone naive datetime not allowed")
            # Store all datetimes as UTC in the database
            return value.astimezone(timezone.utc)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            # Return datetime in UTC
            return value.replace(tzinfo=timezone.utc)
        return value


class Interview(Base):
    __tablename__ = "interviews"

    id = Column(String, primary_key=True)
    organization_id = Column(
        String, ForeignKey("organizations.id"), nullable=True
    )
    scheduled_by_user_id = Column(
        String, ForeignKey("users.id"), nullable=True
    )
    job_application_id = Column(
        String, ForeignKey("job_applications.id"), nullable=True
    )
    __table_args__ = (
        CheckConstraint(
            "(organization_id IS NOT NULL AND scheduled_by_user_id IS NULL)"
            " OR "
            "(organization_id IS NULL AND scheduled_by_user_id IS NOT NULL)",
            name="ck_interview_scheduled_by_xor",
        ),
    )
    candidate_name = Column(String, nullable=False)
    candidate_email = Column(String, nullable=False)
    skill_sets = Column(JSON, nullable=False)  # Selected from organization's skill_sets (array)
    job_role = Column(String, nullable=False)  # Selected from organization's job_roles
    experience_level = Column(String, nullable=False)

    # Available time window
    available_from = Column(TZDateTime, nullable=True)  # Start of availability window
    available_until = Column(TZDateTime, nullable=True)  # End of availability window
    interview_duration = Column(Integer, default=30)  # Duration in minutes (default 30)

    resume_link = Column(String, nullable=True)  # Path to stored resume file
    jd_link = Column(String, nullable=True)  # Path to stored JD file
    jd_id = Column(String, nullable=True)  # ID of standalone job description (for organizations only)
    resume_details = Column(String, nullable=True)  # Extracted text from resume
    jd_details = Column(String, nullable=True)  # Extracted text from JD

    is_completed = Column(Boolean, default=False)  # New field to track completion status

    candidate_suitability = Column(String, nullable=True) # Stores the suitability assessment result as text
    agenda = Column(JSON, nullable=True) # Stores the list of agenda items (e.g., ["Intro", "Tech", "Behavioral"])
    questions = Column(JSON, nullable=True) # Stores the structured questions, matching AgendaQuestionList schema (e.g., [{"agenda": "Tech", "questions": ["Q1", "Q2", ...]}, ...])

    interview_transcript = Column(String, nullable=True)  # Path to stored interview transcript file

    violations = Column(String, nullable=True)  # Stores violations detected during the interview

    # New columns to store agent results
    verification_result = Column(JSON, nullable=True)  # Stores VerificationResponse
    feedback_result = Column(JSON, nullable=True)  # Stores FeedbackResponse
    technical_evaluation_result = Column(JSON, nullable=True)  # Stores TechnicalScoreResponse
    recommendation_result = Column(JSON, nullable=True)  # Stores RecommendationResponse
    interview_summary_result = Column(JSON, nullable=True)  # Stores InterviewSummaryResponse
    interview_violation_result=Column(JSON, nullable=True)

    verification_stats = Column(
        JSON, nullable=True
    )  # Stores stats about answered/unanswered questions
    created_at = Column(TZDateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TZDateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )

    scheduled_by_type = Column(
        SQLAlchemyEnum(ScheduledBy),
        nullable=False,
        doc="‘organization’ if org scheduled it, ‘user’ if scheduled by an individual",
    )

    # Relationship to Organization
    # — relationships —
    # if scheduled by an org, link here:
    organization = relationship(
        "Organization",
        back_populates="interviews",
        foreign_keys=[organization_id],
    )
    # if scheduled by a user, link here:
    scheduled_by_user = relationship(
        "User",
        back_populates="scheduled_interviews",
        foreign_keys=[scheduled_by_user_id],
    )
    # if scheduled from a job application, link here:
    job_application = relationship(
        "JobApplication",
        back_populates="interviews",
        foreign_keys=[job_application_id],
    )

    def __repr__(self):
        who = (
            f"Org({self.organization_id})"
            if self.organization_id
            else f"User({self.scheduled_by_user_id})"
        )
        return f"<Interview {self.id} scheduled by {who}>"

    def __repr__(self):
        return f"<Interview for {self.candidate_name} available at {self.available_from}>"

    @property
    def availability_window_hours(self):
        """Calculate the availability window in hours."""
        if self.available_from and self.available_until:
            delta = self.available_until - self.available_from
            return round(delta.total_seconds() / 3600, 1)  # Convert to hours with 1 decimal place
        return None
