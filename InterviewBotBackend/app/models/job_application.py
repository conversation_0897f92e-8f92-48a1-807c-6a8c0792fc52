from datetime import datetime, timezone
from sqlalchemy import (
    Column,
    String,
    DateTime,
    ForeignKey,
    JSON,
    Text,
    Enum as SQLAlchemyEnum,
)
from sqlalchemy.orm import relationship
from app.models.base import Base
from app.models.enums import ApplicationStatus
from sqlalchemy.types import TypeDecorator


# Custom SQLAlchemy type to ensure timezone awareness
class TZDateTime(TypeDecorator):
    impl = DateTime(timezone=True)
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            if value.tzinfo is None:
                raise ValueError("Timezone naive datetime not allowed")
            # Store all datetimes as UTC in the database
            return value.astimezone(timezone.utc)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            # Return datetime in UTC
            return value.replace(tzinfo=timezone.utc)
        return value


class JobApplication(Base):
    __tablename__ = "job_applications"

    id = Column(String, primary_key=True)
    job_posting_id = Column(String, ForeignKey("job_postings.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)

    # Application data
    cover_letter = Column(Text, nullable=True)
    resume_link = Column(String, nullable=True)  # S3 URL
    screening_answers = Column(JSON, nullable=True)  # Answers to screening questions

    # Status tracking
    status = Column(SQLAlchemyEnum(ApplicationStatus), default=ApplicationStatus.PENDING)

    # Timestamps
    applied_at = Column(TZDateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TZDateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )

    # Relationships
    job_posting = relationship("JobPosting", back_populates="applications")
    user = relationship("User", back_populates="job_applications")
    interviews = relationship("Interview", back_populates="job_application")

    def __repr__(self):
        return f"<JobApplication {self.id} for job {self.job_posting_id} by user {self.user_id}>"

    @property
    def applicant_name(self):
        """Get the applicant's full name."""
        return self.user.full_name if self.user else None

    @property
    def applicant_email(self):
        """Get the applicant's email."""
        return self.user.email if self.user else None

    @property
    def job_title(self):
        """Get the job title from the job posting."""
        return self.job_posting.job_title if self.job_posting else None

    @property
    def company_name(self):
        """Get the company name from the job posting."""
        return self.job_posting.company_name if self.job_posting else None