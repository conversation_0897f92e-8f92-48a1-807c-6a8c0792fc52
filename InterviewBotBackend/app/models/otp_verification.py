from datetime import datetime, <PERSON><PERSON><PERSON>
from sqlalchemy import Column, String, DateTime, Boolean, Enum as SQLAlchemyEnum
from enum import Enum
from app.models.base import Base


class OTPType(str, Enum):
    EMAIL_VERIFICATION = "email_verification"
    PASSWORD_RESET = "password_reset"


class OTPVerification(Base):
    __tablename__ = "otp_verifications"

    id = Column(String, primary_key=True)
    email = Column(String, nullable=False, index=True)
    otp_code = Column(String, nullable=False)
    otp_type = Column(SQLAlchemyEnum(OTPType), nullable=False)
    is_used = Column(Boolean, default=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    used_at = Column(DateTime, nullable=True)

    def is_expired(self) -> bool:
        """Check if the OTP has expired"""
        return datetime.utcnow() > self.expires_at

    def is_valid(self) -> bool:
        """Check if the OTP is valid (not used and not expired)"""
        return not self.is_used and not self.is_expired()

    def __repr__(self):
        return f"<OTPVerification {self.email} - {self.otp_type}>"
