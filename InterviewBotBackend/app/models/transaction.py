"""
Transaction Model

This module defines the Transaction model for tracking payment transactions.
Transactions represent actual payment attempts and completions through Razorpay.
"""

from datetime import datetime
from sqlalchemy import Column, String, Numeric, DateTime, ForeignKey, JSON, Enum as SQLEnum, Text
from sqlalchemy.orm import relationship
from enum import Enum
from app.models.base import Base


class TransactionStatus(str, Enum):
    """Transaction status enumeration."""
    CREATED = "created"
    AUTHORIZED = "authorized"
    CAPTURED = "captured"
    REFUNDED = "refunded"
    FAILED = "failed"


class TransactionType(str, Enum):
    """Transaction type enumeration."""
    PAYMENT = "payment"
    REFUND = "refund"
    PARTIAL_REFUND = "partial_refund"


class Transaction(Base):
    """
    Transaction model for payment transactions.
    
    Tracks individual payment transactions processed through Razorpay.
    Links to orders and provides detailed transaction information.
    """
    __tablename__ = "transactions"

    id = Column(String, primary_key=True)
    razorpay_payment_id = Column(String, unique=True, nullable=True, index=True)
    razorpay_refund_id = Column(String, unique=True, nullable=True, index=True)
    
    # Order relationship
    order_id = Column(String, ForeignKey("orders.id"), nullable=False)
    
    # Transaction details
    amount = Column(Numeric(10, 2), nullable=False)  # Transaction amount
    currency = Column(String, default="USD", nullable=False)
    status = Column(SQLEnum(TransactionStatus), default=TransactionStatus.CREATED, nullable=False)
    transaction_type = Column(SQLEnum(TransactionType), default=TransactionType.PAYMENT, nullable=False)
    
    # Payment method information
    payment_method = Column(String, nullable=True)  # card, netbanking, wallet, etc.
    payment_method_details = Column(JSON, nullable=True)  # Detailed payment method info
    
    # Razorpay specific fields
    razorpay_signature = Column(String, nullable=True)  # Payment signature for verification
    razorpay_order_id = Column(String, nullable=True)  # Reference to Razorpay order
    razorpay_fee = Column(Numeric(10, 2), nullable=True)  # Razorpay processing fee
    razorpay_tax = Column(Numeric(10, 2), nullable=True)  # Tax on processing fee
    
    # Transaction metadata
    gateway_response = Column(JSON, nullable=True)  # Full gateway response
    error_code = Column(String, nullable=True)  # Error code if transaction failed
    error_description = Column(Text, nullable=True)  # Error description
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    processed_at = Column(DateTime, nullable=True)  # When transaction was processed

    # Relationships
    order = relationship("Order", back_populates="transactions")

    def __repr__(self) -> str:
        return f"<Transaction(id='{self.id}', razorpay_payment_id='{self.razorpay_payment_id}', status='{self.status}')>"

    def to_dict(self) -> dict:
        """Convert transaction to dictionary representation."""
        return {
            "id": self.id,
            "razorpay_payment_id": self.razorpay_payment_id,
            "razorpay_refund_id": self.razorpay_refund_id,
            "order_id": self.order_id,
            "amount": float(self.amount) if self.amount else 0.0,
            "currency": self.currency,
            "status": self.status.value if self.status else None,
            "transaction_type": self.transaction_type.value if self.transaction_type else None,
            "payment_method": self.payment_method,
            "payment_method_details": self.payment_method_details,
            "razorpay_signature": self.razorpay_signature,
            "razorpay_order_id": self.razorpay_order_id,
            "razorpay_fee": float(self.razorpay_fee) if self.razorpay_fee else None,
            "razorpay_tax": float(self.razorpay_tax) if self.razorpay_tax else None,
            "gateway_response": self.gateway_response,
            "error_code": self.error_code,
            "error_description": self.error_description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
        }
