from datetime import datetime, timezone
from sqlalchemy import (
    Column,
    String,
    DateTime,
    ForeignKey,
    Boolean,
    JSON,
    Text,
    Enum as SQLAlchemyEnum,
)
from sqlalchemy.orm import relationship
from app.models.base import Base
from app.models.enums import WorkplaceType, EmploymentType, JobStatus
from sqlalchemy.types import TypeDecorator


# Custom SQLAlchemy type to ensure timezone awareness
class TZDateTime(TypeDecorator):
    impl = DateTime(timezone=True)
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            if value.tzinfo is None:
                raise ValueError("Timezone naive datetime not allowed")
            # Store all datetimes as UTC in the database
            return value.astimezone(timezone.utc)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            # Return datetime in UTC
            return value.replace(tzinfo=timezone.utc)
        return value


class JobPosting(Base):
    __tablename__ = "job_postings"

    id = Column(String, primary_key=True)
    organization_id = Column(String, ForeignKey("organizations.id"), nullable=False)

    # LinkedIn-style required fields
    job_title = Column(String, nullable=False)
    company_name = Column(String, nullable=False)  # Auto-filled from org
    workplace_type = Column(SQLAlchemyEnum(WorkplaceType), nullable=False)
    job_location = Column(String, nullable=False)
    employment_type = Column(SQLAlchemyEnum(EmploymentType), nullable=False)
    job_role = Column(String, nullable=True)  # Will be made non-nullable after migration
    jd_link = Column(String, nullable=True)  # S3 URL to JD file
    jd_id = Column(String, nullable=True)    # Reference to org's standalone JD
    required_skills = Column(JSON, nullable=False)  # List of skills

    # Status and lifecycle
    status = Column(SQLAlchemyEnum(JobStatus), default=JobStatus.ACTIVE)

    # Screening questions
    screening_questions = Column(JSON, nullable=True)  # Mixed format questions

    # Auto-filled from organization profile
    company_location = Column(String, nullable=True)  # From org.location
    company_website = Column(String, nullable=True)   # From org.website

    # Timestamps
    created_at = Column(TZDateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TZDateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )

    # Relationships
    organization = relationship("Organization", back_populates="job_postings")
    applications = relationship(
        "JobApplication", back_populates="job_posting", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<JobPosting {self.job_title} at {self.company_name}>"

    @property
    def total_applications(self):
        """Get total number of applications for this job posting."""
        return len(self.applications) if self.applications else 0

    @property
    def pending_applications(self):
        """Get number of pending applications."""
        if not self.applications:
            return 0
        from app.models.enums import ApplicationStatus
        return len([app for app in self.applications if app.status == ApplicationStatus.PENDING])

    @property
    def interviews_scheduled(self):
        """Get number of interviews scheduled from applications."""
        if not self.applications:
            return 0
        from app.models.enums import ApplicationStatus
        return len([app for app in self.applications if app.status == ApplicationStatus.INTERVIEW_SCHEDULED])

    @property
    def interviews_completed(self):
        """Get number of completed interviews from applications."""
        if not self.applications:
            return 0
        completed_count = 0
        for application in self.applications:
            if application.interviews:
                completed_count += len([interview for interview in application.interviews if interview.is_completed])
        return completed_count