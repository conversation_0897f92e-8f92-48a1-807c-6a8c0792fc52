from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Boolean,
    Integer,
    JSON,
    Enum as SQLAlchemyEnum,
)
from enum import Enum
from app.models.base import Base
from sqlalchemy import ForeignKey
from sqlalchemy.orm import relationship

class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True)
    email = Column(String, unique=True, nullable=False, index=True)
    full_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=True)  # Nullable for OAuth users
    is_active = Column(Boolean, default=True)
    is_oauth_user = Column(Boolean, default=False)
    oauth_provider = Column(String, nullable=True)
    google_id = Column(String, unique=True, nullable=True)  # New column for Google ID

    # Email verification fields
    is_email_verified = Column(Boolean, default=False)
    email_verified_at = Column(DateTime, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Profile fields
    position = Column(String, nullable=True)  # e.g., "Product Design Lead"
    location = Column(String, nullable=True)  # e.g., "London, UK"
    phone_number = Column(String, nullable=True)  # e.g., "+97-**********"
    bio = Column(String, nullable=True)       # About text
    website = Column(String, nullable=True)   # Personal website

    # Social media accounts
    instagram = Column(String, nullable=True)
    tiktok = Column(String, nullable=True)
    youtube = Column(String, nullable=True)
    twitter = Column(String, nullable=True)
    linkedin = Column(String, nullable=True)

    # Profile picture URL or path
    profile_picture = Column(String, nullable=True)

    # Resume link (S3 URL)
    resume_link = Column(String, nullable=True)

    # Referral code for this user
    referral_code = Column(String, unique=True, nullable=True, index=True)

    # Store job descriptions not tied to skill sets or job roles
    # Format: {"jd_id1": {"name": "JD Name", "description": "JD text...", "link": "S3 URL"}, ...}
    standalone_job_descriptions = Column(JSON, nullable=True)

    # Employment history - LinkedIn-style work experience
    # Format: [{"id": "emp_id", "company": "Company Name", "position": "Job Title",
    #          "start_date": "2023-01", "end_date": "2024-01", "current": false,
    #          "description": "Job description", "location": "City, Country"}]
    employment_history = Column(JSON, nullable=True)

    # Skills - List of user's skills
    # Format: ["Python", "JavaScript", "Project Management", "UI/UX Design"]
    skills = Column(JSON, nullable=True)


    scheduled_interviews = relationship(
        "Interview",
        back_populates="scheduled_by_user",
        foreign_keys="[Interview.scheduled_by_user_id]",
    )

    # Subscription relationship
    subscription = relationship(
        "UserSubscription",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan"
    )

    job_applications = relationship(
        "JobApplication",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    # Resume relationship
    resumes = relationship(
        "Resume",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<User {self.email}>"

