from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    <PERSON><PERSON>an,
    Integer,
    ForeignKey,
)
from sqlalchemy.orm import relationship
from app.models.base import Base


class UserSubscription(Base):
    __tablename__ = "user_subscriptions"

    id = Column(String, primary_key=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False, unique=True)
    subscription_plan_id = Column(String, ForeignKey("subscription_plans.id"), nullable=False)

    # Usage tracking for different limits
    interviews_used = Column(Integer, default=0)
    jobs_used = Column(Integer, default=0)  # Number of jobs applied to
    jd_used = Column(Integer, default=0)    # Number of job descriptions created
    resume_used = Column(Integer, default=0)  # Number of resumes created

    activated_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)  # For future time-based subscriptions
    is_active = Column(<PERSON>olean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="subscription")
    subscription_plan = relationship("SubscriptionPlan", back_populates="user_subscriptions")

    def __repr__(self):
        return f"<UserSubscription {self.user_id} - {self.interviews_used} interviews, {self.jobs_used} jobs, {self.jd_used} JDs, {self.resume_used} resumes used>"

    @property
    def interviews_remaining(self):
        """Calculate remaining interviews based on plan limit and usage."""
        if self.subscription_plan:
            return max(0, self.subscription_plan.interview_limit - self.interviews_used)
        return 0

    @property
    def jobs_remaining(self):
        """Calculate remaining job applications based on plan limit and usage."""
        if self.subscription_plan:
            return max(0, self.subscription_plan.jobs_limit - self.jobs_used)
        return 0

    @property
    def jd_remaining(self):
        """Calculate remaining job descriptions based on plan limit and usage."""
        if self.subscription_plan:
            return max(0, self.subscription_plan.jd_limit - self.jd_used)
        return 0

    @property
    def resume_remaining(self):
        """Calculate remaining resumes based on plan limit and usage."""
        if self.subscription_plan and self.subscription_plan.resume_limit:
            return max(0, self.subscription_plan.resume_limit - self.resume_used)
        return 0

    @property
    def is_interview_limit_reached(self):
        """Check if user has reached their interview limit."""
        return self.interviews_remaining <= 0

    @property
    def is_jobs_limit_reached(self):
        """Check if user has reached their jobs limit."""
        return self.jobs_remaining <= 0

    @property
    def is_jd_limit_reached(self):
        """Check if user has reached their job description limit."""
        return self.jd_remaining <= 0

    @property
    def is_resume_limit_reached(self):
        """Check if user has reached their resume limit."""
        return self.resume_remaining <= 0

    @property
    def is_limit_reached(self):
        """Check if user has reached their interview limit (backward compatibility)."""
        return self.is_interview_limit_reached

    def can_schedule_interview(self):
        """Check if user can schedule another interview."""
        return self.is_active and not self.is_interview_limit_reached

    def can_apply_to_job(self):
        """Check if user can apply to another job."""
        return self.is_active and not self.is_jobs_limit_reached

    def can_create_jd(self):
        """Check if user can create another job description."""
        return self.is_active and not self.is_jd_limit_reached

    def can_create_resume(self):
        """Check if user can create another resume."""
        return self.is_active and not self.is_resume_limit_reached