from enum import Enum


class WorkplaceType(Enum):
    REMOTE = "remote"
    HYBRID = "hybrid"
    ON_SITE = "on_site"


class EmploymentType(Enum):
    FULL_TIME = "full_time"
    PART_TIME = "part_time"
    CONTRACT = "contract"
    INTERNSHIP = "internship"
    TEMPORARY = "temporary"


class JobStatus(Enum):
    ACTIVE = "active"
    CLOSED = "closed"


class ApplicationStatus(Enum):
    PENDING = "pending"
    REVIEWED = "reviewed"
    INTERVIEW_SCHEDULED = "interview_scheduled"
    REJECTED = "rejected"
    HIRED = "hired"


class QuestionType(Enum):
    TEXT = "text"
    MULTIPLE_CHOICE = "multiple_choice"