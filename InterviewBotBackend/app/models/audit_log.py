"""
Audit Log Model

This module defines the AuditLog model for comprehensive activity tracking.
Audit logs provide detailed records of all payment-related operations.
"""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, JSON, Text, Enum as SQLEnum
from enum import Enum
from app.models.base import Base


class AuditAction(str, Enum):
    """Audit action enumeration."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    PAYMENT_INITIATED = "payment_initiated"
    PAYMENT_COMPLETED = "payment_completed"
    PAYMENT_FAILED = "payment_failed"
    REFUND_INITIATED = "refund_initiated"
    REFUND_COMPLETED = "refund_completed"
    WEBHOOK_RECEIVED = "webhook_received"
    WEBHOOK_PROCESSED = "webhook_processed"
    ORDER_CREATED = "order_created"
    ORDER_UPDATED = "order_updated"
    PLAN_CREATED = "plan_created"
    PLAN_UPDATED = "plan_updated"


class AuditEntityType(str, Enum):
    """Audit entity type enumeration."""
    PLAN = "plan"
    ORDER = "order"
    TRANSACTION = "transaction"
    WEBHOOK_EVENT = "webhook_event"
    USER = "user"
    SUBSCRIPTION = "subscription"


class AuditLog(Base):
    """
    AuditLog model for comprehensive activity tracking.
    
    Provides detailed audit trails for all payment-related operations,
    ensuring compliance and enabling detailed analysis of system activities.
    """
    __tablename__ = "audit_logs"

    id = Column(String, primary_key=True)
    
    # Entity information
    entity_type = Column(SQLEnum(AuditEntityType), nullable=False)
    entity_id = Column(String, nullable=False, index=True)
    action = Column(SQLEnum(AuditAction), nullable=False)
    
    # User and context information
    user_id = Column(String, nullable=True, index=True)  # User who performed the action
    user_email = Column(String, nullable=True)  # User email for reference
    ip_address = Column(String, nullable=True)  # IP address of the request
    user_agent = Column(String, nullable=True)  # User agent string
    
    # Change tracking
    old_values = Column(JSON, nullable=True)  # Previous values before change
    new_values = Column(JSON, nullable=True)  # New values after change
    changes = Column(JSON, nullable=True)  # Specific fields that changed
    
    # Additional context
    audit_metadata = Column(JSON, nullable=True)  # Additional metadata
    description = Column(Text, nullable=True)  # Human-readable description
    
    # Request information
    request_id = Column(String, nullable=True)  # Request ID for correlation
    session_id = Column(String, nullable=True)  # Session ID if available
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    def __repr__(self) -> str:
        return f"<AuditLog(id='{self.id}', entity_type='{self.entity_type}', action='{self.action}')>"

    def to_dict(self) -> dict:
        """Convert audit log to dictionary representation."""
        return {
            "id": self.id,
            "entity_type": self.entity_type.value if self.entity_type else None,
            "entity_id": self.entity_id,
            "action": self.action.value if self.action else None,
            "user_id": self.user_id,
            "user_email": self.user_email,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "old_values": self.old_values,
            "new_values": self.new_values,
            "changes": self.changes,
            "audit_metadata": self.audit_metadata,
            "description": self.description,
            "request_id": self.request_id,
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    @classmethod
    def create_log(
        cls,
        entity_type: AuditEntityType,
        entity_id: str,
        action: AuditAction,
        user_id: str = None,
        user_email: str = None,
        old_values: dict = None,
        new_values: dict = None,
        audit_metadata: dict = None,
        description: str = None,
        ip_address: str = None,
        user_agent: str = None,
        request_id: str = None,
        session_id: str = None,
    ) -> "AuditLog":
        """Create a new audit log entry."""
        import uuid
        
        # Calculate changes if both old and new values are provided
        changes = None
        if old_values and new_values:
            changes = {}
            for key, new_value in new_values.items():
                old_value = old_values.get(key)
                if old_value != new_value:
                    changes[key] = {
                        "old": old_value,
                        "new": new_value
                    }
        
        return cls(
            id=str(uuid.uuid4()),
            entity_type=entity_type,
            entity_id=entity_id,
            action=action,
            user_id=user_id,
            user_email=user_email,
            old_values=old_values,
            new_values=new_values,
            changes=changes,
            audit_metadata=audit_metadata,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            request_id=request_id,
            session_id=session_id,
        )
