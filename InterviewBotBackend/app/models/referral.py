"""
Referral System Models

This module defines the database models for the referral system including
referral codes, referral relationships, and token transactions.
"""

from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Boolean,
    Integer,
    ForeignKey,
    Numeric,
    Enum as SQLAlchemyEnum,
    Index,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship
from enum import Enum
from app.models.base import Base



class TokenTransactionType(str, Enum):
    """Token transaction type enumeration."""
    REFERRAL_BONUS_REFERRER = "referral_bonus_referrer"  # 300 tokens for referrer when referee buys paid plan
    REFERRAL_BONUS_REFEREE = "referral_bonus_referee"    # 100 tokens for referee when they buy paid plan
    BONUS_AWARD = "bonus_award"                          # Future: admin bonus awards


class ReferralCode(Base):
    """
    Model for storing referral codes for users and organizations.
    Each user/organization has one unique referral code.
    """
    __tablename__ = "referral_codes"

    id = Column(String, primary_key=True)
    code = Column(String, unique=True, nullable=False, index=True)
    
    # Entity information (individual or organization)
    entity_type = Column(String, nullable=False)  # "individual" or "organization"
    entity_id = Column(String, nullable=False)  # user_id or organization_id
    
    # Metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    referrals_made = relationship(
        "ReferralRelationship",
        back_populates="referrer_code",
        foreign_keys="[ReferralRelationship.referrer_code_id]"
    )
    
    # Ensure one referral code per entity
    __table_args__ = (
        UniqueConstraint('entity_type', 'entity_id', name='uq_entity_referral_code'),
        Index('idx_referral_codes_entity', 'entity_type', 'entity_id'),
    )

    def __repr__(self):
        return f"<ReferralCode {self.code} - {self.entity_type}:{self.entity_id}>"


class ReferralRelationship(Base):
    """
    Model for tracking referral relationships.
    Records who referred whom and when.
    """
    __tablename__ = "referral_relationships"

    id = Column(String, primary_key=True)
    
    # Referrer information
    referrer_code_id = Column(String, ForeignKey("referral_codes.id"), nullable=False)
    referrer_entity_type = Column(String, nullable=False)  # "individual" or "organization"
    referrer_entity_id = Column(String, nullable=False)

    # Referee information (the new user/organization who signed up)
    referee_entity_type = Column(String, nullable=False)  # "individual" or "organization"
    referee_entity_id = Column(String, nullable=False)
    
    # Referral details
    referral_date = Column(DateTime, default=datetime.utcnow)
    referee_purchased_paid_plan = Column(Boolean, default=False)  # Track if referee bought paid plan
    paid_plan_purchase_date = Column(DateTime, nullable=True)     # When referee bought paid plan
    tokens_awarded = Column(Boolean, default=False)              # Track if tokens were awarded
    tokens_awarded_at = Column(DateTime, nullable=True)          # When tokens were awarded
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    referrer_code = relationship(
        "ReferralCode",
        back_populates="referrals_made",
        foreign_keys=[referrer_code_id]
    )
    
    # Ensure one referral relationship per referee
    __table_args__ = (
        UniqueConstraint('referee_entity_type', 'referee_entity_id', name='uq_referee_referral'),
        Index('idx_referral_relationships_referrer', 'referrer_entity_type', 'referrer_entity_id'),
        Index('idx_referral_relationships_referee', 'referee_entity_type', 'referee_entity_id'),
    )

    def __repr__(self):
        return f"<ReferralRelationship {self.referrer_entity_type}:{self.referrer_entity_id} -> {self.referee_entity_type}:{self.referee_entity_id}>"


class TokenTransaction(Base):
    """
    Model for tracking token transactions.
    Records all token earnings, spending, and transfers.
    """
    __tablename__ = "token_transactions"

    id = Column(String, primary_key=True)
    
    # Entity information
    entity_type = Column(String, nullable=False)  # "individual" or "organization"
    entity_id = Column(String, nullable=False)
    
    # Transaction details
    transaction_type = Column(SQLAlchemyEnum(TokenTransactionType), nullable=False)
    amount = Column(Integer, nullable=False)  # Positive for credits, negative for debits
    balance_after = Column(Integer, nullable=False)  # Running balance after this transaction
    
    # Reference information
    reference_id = Column(String, nullable=True)  # referral_relationship_id, order_id, etc.
    reference_type = Column(String, nullable=True)  # "referral", "purchase", "bonus", etc.
    
    # Description and metadata
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_token_transactions_entity', 'entity_type', 'entity_id'),
        Index('idx_token_transactions_type', 'transaction_type'),
        Index('idx_token_transactions_created', 'created_at'),
        Index('idx_token_transactions_reference', 'reference_type', 'reference_id'),
    )

    def __repr__(self):
        return f"<TokenTransaction {self.entity_type}:{self.entity_id} {self.amount} tokens - {self.transaction_type}>"


class ReferralStats(Base):
    """
    Model for caching referral statistics for performance.
    Stores aggregated data to avoid complex queries.
    """
    __tablename__ = "referral_stats"

    id = Column(String, primary_key=True)
    
    # Entity information
    entity_type = Column(String, nullable=False)  # "individual" or "organization"
    entity_id = Column(String, nullable=False)
    
    # Statistics
    total_referrals = Column(Integer, default=0)
    successful_referrals = Column(Integer, default=0)  # Referrals that completed signup
    total_tokens_earned = Column(Integer, default=0)
    current_token_balance = Column(Integer, default=0)
    
    # Timestamps
    last_referral_date = Column(DateTime, nullable=True)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Ensure one stats record per entity
    __table_args__ = (
        UniqueConstraint('entity_type', 'entity_id', name='uq_entity_referral_stats'),
        Index('idx_referral_stats_entity', 'entity_type', 'entity_id'),
    )

    def __repr__(self):
        return f"<ReferralStats {self.entity_type}:{self.entity_id} - {self.total_referrals} referrals, {self.current_token_balance} tokens>"
