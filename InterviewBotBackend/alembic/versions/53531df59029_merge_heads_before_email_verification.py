"""merge_heads_before_email_verification

Revision ID: 53531df59029
Revises: add_profile_fields_to_organization, b25eaf66d92c, remove_job_descriptions
Create Date: 2025-07-09 22:35:06.195497

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '53531df59029'
down_revision: Union[str, None] = ('add_profile_fields_to_organization', 'b25eaf66d92c', 'remove_job_descriptions')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
