"""added violation field

Revision ID: 48fb9e91dc7d
Revises: 42ab1f75e01a
Create Date: 2025-04-21 17:28:15.028063

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '48fb9e91dc7d'
down_revision: Union[str, None] = '42ab1f75e01a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('interviews', sa.Column('violations', postgresql.JSON(astext_type=sa.Text()), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("interviews", "violations")
    # ### end Alembic commands ###
