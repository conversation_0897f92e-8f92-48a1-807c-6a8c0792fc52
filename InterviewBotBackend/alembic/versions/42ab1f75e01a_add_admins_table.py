"""add admins table

# Ensure these identifiers match your actual migration file
Revision ID: 42ab1f75e01a # Or ideally, a NEW ID from `alembic revision`
Revises: 53d8b20b82f7
Create Date: 2025-04-16 17:46:15.524061 # Or the new creation date/time

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# from sqlalchemy.dialects import postgresql # Not needed for these standard types

# revision identifiers, used by Alembic.
revision: str = "42ab1f75e01a"  # <<< MAKE SURE this is the correct ID for THIS file
down_revision: Union[str, None] = "53d8b20b82f7"  # <<< Should point to the previous migration
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create the admins table based on the Admin model."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "admins",
        # Based on: id = Column(String, primary_key=True)
        sa.Column("id", sa.String(), nullable=False),
        # Based on: email = Column(String, unique=True, nullable=False, index=True)
        sa.Column("email", sa.String(), nullable=False),
        # Based on: hashed_password = Column(String, nullable=False)
        sa.Column("hashed_password", sa.String(), nullable=False),
        # Based on: created_at = Column(DateTime, default=datetime.utcnow)
        # Using server_default for DB-level default timestamp
        sa.Column("created_at", sa.DateTime(), server_default=sa.func.now(), nullable=False),
        # Based on: updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        # Using server_default and onupdate for DB-level timestamps
        # Note: onupdate=sa.func.now() might require database-specific triggers for full effect (e.g., in PostgreSQL)
        sa.Column(
            "updated_at",
            sa.DateTime(),
            server_default=sa.func.now(),
            onupdate=sa.func.now(),
            nullable=False,
        ),
        # Define primary key constraint
        sa.PrimaryKeyConstraint("id", name=op.f("pk_admins")),
        # Define unique constraint for email (handles unique=True)
        sa.UniqueConstraint("email", name=op.f("uq_admins_email")),
    )
    # Create index separately for email (handles index=True)
    # The unique=True here is slightly redundant given the UniqueConstraint,
    # but explicitly defines the index's unique property.
    op.create_index(op.f("ix_admins_email"), "admins", ["email"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Drop the admins table."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop the index first
    op.drop_index(op.f("ix_admins_email"), table_name="admins")
    # Drop the table
    op.drop_table("admins")
    # ### end Alembic commands ###
