"""updated interview table

Revision ID: 6dca61aaad41
Revises: 8dc1ab7e4f9d
Create Date: 2025-04-11 10:33:28.841019

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "6dca61aaad41"
down_revision: Union[str, None] = "8dc1ab7e4f9d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema by adding new columns to the interviews table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("interviews", sa.Column("interview_transcript", sa.String(), nullable=True))
    op.add_column(
        "interviews",
        sa.Column("verification_result", postgresql.JSON(astext_type=sa.Text()), nullable=True),
    )
    op.add_column(
        "interviews",
        sa.Column("feedback_result", postgresql.JSON(astext_type=sa.Text()), nullable=True),
    )
    op.add_column(
        "interviews",
        sa.Column(
            "technical_evaluation_result", postgresql.JSON(astext_type=sa.Text()), nullable=True
        ),
    )
    op.add_column(
        "interviews",
        sa.Column("recommendation_result", postgresql.JSON(astext_type=sa.Text()), nullable=True),
    )
    op.add_column(
        "interviews",
        sa.Column(
            "interview_summary_result", postgresql.JSON(astext_type=sa.Text()), nullable=True
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema by removing the added columns."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("interviews", "interview_summary_result")
    op.drop_column("interviews", "recommendation_result")
    op.drop_column("interviews", "technical_evaluation_result")
    op.drop_column("interviews", "feedback_result")
    op.drop_column("interviews", "verification_result")
    op.drop_column("interviews", "interview_transcript")
    # ### end Alembic commands ###
