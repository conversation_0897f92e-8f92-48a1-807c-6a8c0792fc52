"""Add suitability, agenda, and questions to Interview model

Revision ID: 8dc1ab7e4f9d
Revises: aaa65eef1258
Create Date: 2025-04-08 10:56:18.656064

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8dc1ab7e4f9d'
down_revision: Union[str, None] = 'aaa65eef1258'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("interviews", sa.Column("resume_details", sa.String(), nullable=True))
    op.add_column("interviews", sa.Column("jd_details", sa.String(), nullable=True))
    op.add_column("interviews", sa.Column("candidate_suitability", sa.String(), nullable=True))
    op.add_column("interviews", sa.Column("agenda", sa.<PERSON>(), nullable=True))
    op.add_column("interviews", sa.<PERSON>umn("questions", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("interviews", "resume_details")
    op.drop_column("interviews", "jd_details")
    op.drop_column("interviews", "questions")
    op.drop_column("interviews", "agenda")
    op.drop_column("interviews", "candidate_suitability")
    # ### end Alembic commands ###
