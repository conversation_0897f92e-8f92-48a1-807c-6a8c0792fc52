"""updated violation field

Revision ID: e898abaf1715
Revises: 48fb9e91dc7d
Create Date: 2025-04-21 18:05:07.744727

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e898abaf1715'
down_revision: Union[str, None] = '48fb9e91dc7d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # First drop the existing JSON column
    op.drop_column('interviews', 'violations')
    # Then add the new String column
    op.add_column('interviews', sa.Column('violations', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # First drop the String column
    op.drop_column('interviews', 'violations')
    # Then recreate the JSON column
    op.add_column('interviews', 
        sa.Column('violations', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True)
    )
    # ### end Alembic commands ###
