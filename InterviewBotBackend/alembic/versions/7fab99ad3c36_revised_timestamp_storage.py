"""revised timestamp storage

Revision ID: 7fab99ad3c36
Revises: dffe6ab0c85c
Create Date: 2025-04-02 10:36:45.917589

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '7fab99ad3c36'
down_revision: Union[str, None] = 'dffe6ab0c85c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    """Upgrade schema: change datetime columns to timezone-aware (timestamptz)."""
    op.alter_column(
        "interviews",
        "interview_timestamp",
        type_=postgresql.TIMESTAMP(timezone=True),
        existing_type=sa.DateTime(),
        existing_nullable=False,
    )
    op.alter_column(
        "interviews",
        "created_at",
        type_=postgresql.TIMESTAMP(timezone=True),
        existing_type=sa.DateTime(),
        existing_nullable=True,
    )
    op.alter_column(
        "interviews",
        "updated_at",
        type_=postgresql.TIMESTAMP(timezone=True),
        existing_type=sa.DateTime(),
        existing_nullable=True,
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "interviews",
        sa.Column("id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("organization_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("candidate_name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("candidate_email", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("skill_set", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("job_role", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("experience_level", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "interview_timestamp",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("resume_link", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("jd_link", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("is_completed", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column(
            "created_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.Column(
            "updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"], ["organizations.id"], name="interviews_organization_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="interviews_pkey"),
    )
    # ### end Alembic commands ###
