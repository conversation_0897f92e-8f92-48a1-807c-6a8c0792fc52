"""add_email_verification_fields_simple

Revision ID: 1368f94b49a7
Revises: f6a22399580b
Create Date: 2025-07-09 22:59:50.130003

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1368f94b49a7'
down_revision: Union[str, None] = 'f6a22399580b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add email verification fields to users and organizations tables."""
    # Add email verification fields to users table
    op.add_column('users', sa.Column('is_email_verified', sa.<PERSON>an(), nullable=True, default=False))
    op.add_column('users', sa.Column('email_verified_at', sa.DateTime(), nullable=True))

    # Add email verification fields to organizations table
    op.add_column('organizations', sa.Column('is_email_verified', sa.<PERSON>olean(), nullable=True, default=False))
    op.add_column('organizations', sa.Column('email_verified_at', sa.DateTime(), nullable=True))

    # Create OTP verifications table
    op.create_table('otp_verifications',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('otp_code', sa.String(), nullable=False),
        sa.Column('otp_type', sa.Enum('EMAIL_VERIFICATION', 'PASSWORD_RESET', name='otptype'), nullable=False),
        sa.Column('is_used', sa.Boolean(), nullable=True, default=False),
        sa.Column('expires_at', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('used_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_otp_verifications_email'), 'otp_verifications', ['email'], unique=False)


def downgrade() -> None:
    """Remove email verification fields and OTP table."""
    # Drop OTP verifications table
    op.drop_index(op.f('ix_otp_verifications_email'), table_name='otp_verifications')
    op.drop_table('otp_verifications')

    # Remove email verification fields from organizations table
    op.drop_column('organizations', 'email_verified_at')
    op.drop_column('organizations', 'is_email_verified')

    # Remove email verification fields from users table
    op.drop_column('users', 'email_verified_at')
    op.drop_column('users', 'is_email_verified')
