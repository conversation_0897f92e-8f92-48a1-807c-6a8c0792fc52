"""updated interview(added start time and end time)

Revision ID: 55da2805bf49
Revises: 77e7cc2d8684
Create Date: 2025-04-06 09:40:04.678726

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '55da2805bf49'
down_revision: Union[str, None] = '77e7cc2d8684'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('interviews')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('interviews',
    sa.Column('id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('organization_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('candidate_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('candidate_email', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('skill_set', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('job_role', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('experience_level', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('interview_timestamp', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('resume_link', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('jd_link', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_completed', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], name='interviews_organization_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='interviews_pkey')
    )
    # ### end Alembic commands ###
