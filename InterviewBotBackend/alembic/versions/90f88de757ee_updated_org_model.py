"""updated org model

Revision ID: 90f88de757ee
Revises: e8f490632528
Create Date: 2025-03-31 14:49:44.405801

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '90f88de757ee'
down_revision: Union[str, None] = 'e8f490632528'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('email', sa.String(), nullable=False))
    op.add_column('organizations', sa.Column('organization_name', sa.String(), nullable=False))
    op.add_column('organizations', sa.Column('hashed_password', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('is_active', sa.<PERSON>(), nullable=True))
    op.add_column('organizations', sa.Column('is_oauth_org', sa.<PERSON>(), nullable=True))
    op.add_column('organizations', sa.Column('oauth_provider', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('google_id', sa.String(), nullable=True))
    op.drop_constraint('organizations_name_key', 'organizations', type_='unique')
    op.drop_constraint('organizations_owner_id_key', 'organizations', type_='unique')
    op.create_index(op.f('ix_organizations_email'), 'organizations', ['email'], unique=True)
    op.create_unique_constraint(None, 'organizations', ['google_id'])
    op.create_unique_constraint(None, 'organizations', ['organization_name'])
    op.drop_column('organizations', 'name')
    op.drop_column('organizations', 'owner_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('owner_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('organizations', sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'organizations', type_='unique')
    op.drop_constraint(None, 'organizations', type_='unique')
    op.drop_index(op.f('ix_organizations_email'), table_name='organizations')
    op.create_unique_constraint('organizations_owner_id_key', 'organizations', ['owner_id'])
    op.create_unique_constraint('organizations_name_key', 'organizations', ['name'])
    op.drop_column('organizations', 'google_id')
    op.drop_column('organizations', 'oauth_provider')
    op.drop_column('organizations', 'is_oauth_org')
    op.drop_column('organizations', 'is_active')
    op.drop_column('organizations', 'hashed_password')
    op.drop_column('organizations', 'organization_name')
    op.drop_column('organizations', 'email')
    # ### end Alembic commands ###
