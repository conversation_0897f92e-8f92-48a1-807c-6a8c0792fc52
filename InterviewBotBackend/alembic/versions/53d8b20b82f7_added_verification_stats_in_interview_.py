"""added verification_stats in interview table

Revision ID: 53d8b20b82f7
Revises: 6dca61aaad41
Create Date: 2025-04-11 19:59:17.805410

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "53d8b20b82f7"
down_revision: Union[str, None] = "6dca61aaad41"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add verification_stats column to interviews table
    op.add_column(
        "interviews",
        sa.Column("verification_stats", postgresql.JSON(astext_type=sa.Text()), nullable=True),
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Remove verification_stats column if rolling back
    op.drop_column("interviews", "verification_stats")
