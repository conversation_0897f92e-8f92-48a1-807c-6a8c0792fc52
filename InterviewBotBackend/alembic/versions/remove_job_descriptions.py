"""remove job descriptions column

Revision ID: remove_job_descriptions
Revises: add_standalone_job_descriptions
Create Date: 2025-04-03 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'remove_job_descriptions'
down_revision: Union[str, None] = 'add_standalone_job_descriptions'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Remove job_descriptions column from organizations table
    op.drop_column('organizations', 'job_descriptions')


def downgrade() -> None:
    """Downgrade schema."""
    # Add job_descriptions column back to organizations table
    op.add_column('organizations', sa.Column('job_descriptions', sa.JSON(), nullable=True))
