"""updated connection between user and org

Revision ID: e8f490632528
Revises: ae8b0ec85a86
Create Date: 2025-03-31 14:47:06.430238

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e8f490632528'
down_revision: Union[str, None] = 'ae8b0ec85a86'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('users_organization_id_fkey', 'users', type_='foreignkey')
    op.drop_column('users', 'is_organization_owner')
    op.drop_column('users', 'organization_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('organization_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('is_organization_owner', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.create_foreign_key('users_organization_id_fkey', 'users', 'organizations', ['organization_id'], ['id'])
    # ### end Alembic commands ###
