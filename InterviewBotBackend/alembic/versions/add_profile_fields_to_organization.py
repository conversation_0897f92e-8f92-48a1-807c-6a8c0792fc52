"""add profile fields to organization

Revision ID: add_profile_fields_to_organization
Revises: 48fb9e91dc7d
Create Date: 2025-04-25 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'add_profile_fields_to_organization'
down_revision: Union[str, None] = '48fb9e91dc7d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Add profile fields to organizations table
    op.add_column('organizations', sa.Column('position', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('location', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('phone_number', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('bio', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('website', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('instagram', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('tiktok', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('youtube', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('twitter', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('linkedin', sa.String(), nullable=True))
    op.add_column('organizations', sa.Column('profile_picture', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop all the profile fields added to organizations table
    op.drop_column('organizations', 'profile_picture')
    op.drop_column('organizations', 'linkedin')
    op.drop_column('organizations', 'twitter')
    op.drop_column('organizations', 'youtube')
    op.drop_column('organizations', 'tiktok')
    op.drop_column('organizations', 'instagram')
    op.drop_column('organizations', 'website')
    op.drop_column('organizations', 'bio')
    op.drop_column('organizations', 'phone_number')
    op.drop_column('organizations', 'location')
    op.drop_column('organizations', 'position')
    # ### end Alembic commands ###
