"""updated user model

Revision ID: effc3df0a9fb
Revises: e898abaf1715
Create Date: 2025-04-22 12:56:28.742335

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'effc3df0a9fb'
down_revision: Union[str, None] = 'e898abaf1715'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Add new profile fields to users table
    op.add_column('users', sa.Column('position', sa.String(), nullable=True))
    op.add_column('users', sa.Column('location', sa.String(), nullable=True))
    op.add_column('users', sa.Column('phone_number', sa.String(), nullable=True))
    op.add_column('users', sa.Column('bio', sa.String(), nullable=True))
    op.add_column('users', sa.Column('website', sa.String(), nullable=True))
    op.add_column('users', sa.Column('instagram', sa.String(), nullable=True))
    op.add_column('users', sa.Column('tiktok', sa.String(), nullable=True))
    op.add_column('users', sa.Column('youtube', sa.String(), nullable=True))
    op.add_column('users', sa.Column('twitter', sa.String(), nullable=True))
    op.add_column('users', sa.Column('linkedin', sa.String(), nullable=True))
    op.add_column('users', sa.Column('profile_picture', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop all the profile fields added to users table
    op.drop_column('users', 'profile_picture')
    op.drop_column('users', 'custom_links')
    op.drop_column('users', 'linkedin')
    op.drop_column('users', 'twitter')
    op.drop_column('users', 'youtube')
    op.drop_column('users', 'tiktok')
    op.drop_column('users', 'instagram')
    op.drop_column('users', 'website')
    op.drop_column('users', 'bio')
    op.drop_column('users', 'phone_number')
    op.drop_column('users', 'location')
    op.drop_column('users', 'position')
    # ### end Alembic commands ###
