"""updated org model

Revision ID: db0adb923f39
Revises: 401f0a0ffa54
Create Date: 2025-03-31 16:04:15.627516

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'db0adb923f39'
down_revision: Union[str, None] = '401f0a0ffa54'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('organizations_organization_name_key', 'organizations', type_='unique')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('organizations_organization_name_key', 'organizations', ['organization_name'])
    # ### end Alembic commands ###
