"""Remove EntityType enum and replace with string columns

Revision ID: remove_entity_type_enum
Revises: add_referral_system
Create Date: 2025-01-08 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'remove_entity_type_enum'
down_revision = 'add_referral_system'
branch_labels = None
depends_on = None


def upgrade():
    """Replace EntityType enum columns with string columns."""
    
    # Update referral_codes table
    op.alter_column('referral_codes', 'entity_type',
                   existing_type=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   type_=sa.String(),
                   existing_nullable=False)
    
    # Update referral_relationships table
    op.alter_column('referral_relationships', 'referrer_entity_type',
                   existing_type=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   type_=sa.String(),
                   existing_nullable=False)
    
    op.alter_column('referral_relationships', 'referee_entity_type',
                   existing_type=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   type_=sa.String(),
                   existing_nullable=False)
    
    # Update token_transactions table
    op.alter_column('token_transactions', 'entity_type',
                   existing_type=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   type_=sa.String(),
                   existing_nullable=False)
    
    # Update referral_stats table
    op.alter_column('referral_stats', 'entity_type',
                   existing_type=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   type_=sa.String(),
                   existing_nullable=False)
    
    # Update existing data to use new string values
    # Convert 'USER' to 'individual' and 'ORGANIZATION' to 'organization'
    op.execute("UPDATE referral_codes SET entity_type = 'individual' WHERE entity_type = 'USER'")
    op.execute("UPDATE referral_codes SET entity_type = 'organization' WHERE entity_type = 'ORGANIZATION'")
    
    op.execute("UPDATE referral_relationships SET referrer_entity_type = 'individual' WHERE referrer_entity_type = 'USER'")
    op.execute("UPDATE referral_relationships SET referrer_entity_type = 'organization' WHERE referrer_entity_type = 'ORGANIZATION'")
    op.execute("UPDATE referral_relationships SET referee_entity_type = 'individual' WHERE referee_entity_type = 'USER'")
    op.execute("UPDATE referral_relationships SET referee_entity_type = 'organization' WHERE referee_entity_type = 'ORGANIZATION'")
    
    op.execute("UPDATE token_transactions SET entity_type = 'individual' WHERE entity_type = 'USER'")
    op.execute("UPDATE token_transactions SET entity_type = 'organization' WHERE entity_type = 'ORGANIZATION'")
    
    op.execute("UPDATE referral_stats SET entity_type = 'individual' WHERE entity_type = 'USER'")
    op.execute("UPDATE referral_stats SET entity_type = 'organization' WHERE entity_type = 'ORGANIZATION'")
    
    # Drop the enum type (this will only work if no other tables are using it)
    try:
        op.execute("DROP TYPE entitytype")
    except Exception:
        # If the enum is still being used elsewhere, this will fail
        # In that case, we'll leave it for manual cleanup
        pass


def downgrade():
    """Revert string columns back to EntityType enum."""
    
    # Recreate the enum type
    entitytype_enum = postgresql.ENUM('USER', 'ORGANIZATION', name='entitytype')
    entitytype_enum.create(op.get_bind())
    
    # Update existing data back to enum values
    op.execute("UPDATE referral_codes SET entity_type = 'USER' WHERE entity_type = 'individual'")
    op.execute("UPDATE referral_codes SET entity_type = 'ORGANIZATION' WHERE entity_type = 'organization'")
    
    op.execute("UPDATE referral_relationships SET referrer_entity_type = 'USER' WHERE referrer_entity_type = 'individual'")
    op.execute("UPDATE referral_relationships SET referrer_entity_type = 'ORGANIZATION' WHERE referrer_entity_type = 'organization'")
    op.execute("UPDATE referral_relationships SET referee_entity_type = 'USER' WHERE referee_entity_type = 'individual'")
    op.execute("UPDATE referral_relationships SET referee_entity_type = 'ORGANIZATION' WHERE referee_entity_type = 'organization'")
    
    op.execute("UPDATE token_transactions SET entity_type = 'USER' WHERE entity_type = 'individual'")
    op.execute("UPDATE token_transactions SET entity_type = 'ORGANIZATION' WHERE entity_type = 'organization'")
    
    op.execute("UPDATE referral_stats SET entity_type = 'USER' WHERE entity_type = 'individual'")
    op.execute("UPDATE referral_stats SET entity_type = 'ORGANIZATION' WHERE entity_type = 'organization'")
    
    # Convert columns back to enum
    op.alter_column('referral_codes', 'entity_type',
                   existing_type=sa.String(),
                   type_=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   existing_nullable=False)
    
    op.alter_column('referral_relationships', 'referrer_entity_type',
                   existing_type=sa.String(),
                   type_=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   existing_nullable=False)
    
    op.alter_column('referral_relationships', 'referee_entity_type',
                   existing_type=sa.String(),
                   type_=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   existing_nullable=False)
    
    op.alter_column('token_transactions', 'entity_type',
                   existing_type=sa.String(),
                   type_=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   existing_nullable=False)
    
    op.alter_column('referral_stats', 'entity_type',
                   existing_type=sa.String(),
                   type_=sa.Enum('USER', 'ORGANIZATION', name='entitytype'),
                   existing_nullable=False)
