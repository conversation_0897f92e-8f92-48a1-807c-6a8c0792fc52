"""add subscription tables and limits

Revision ID: add_subscription_tables_and_limits
Revises: 8dc1ab7e4f9d
Create Date: 2025-07-30 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'add_subscription_tables_and_limits'
down_revision: Union[str, None] = '8dc1ab7e4f9d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create subscription tables with new limit structure."""
    
    # Create enum for plan types
    plan_type_enum = postgresql.ENUM('individual', 'organization', name='plantype')
    plan_type_enum.create(op.get_bind())
    
    # Create subscription_plans table
    op.create_table(
        'subscription_plans',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('plan_type', plan_type_enum, nullable=False),
        
        # Common limits for both individual and organization
        sa.Column('interview_limit', sa.Integer(), nullable=False),
        sa.Column('jobs_limit', sa.Integer(), nullable=False),
        sa.Column('jd_limit', sa.Integer(), nullable=False),
        
        # Individual-specific limits
        sa.Column('resume_limit', sa.Integer(), nullable=True),
        
        # Organization-specific limits
        sa.Column('candidate_suitability_limit', sa.Integer(), nullable=True),
        
        sa.Column('price', sa.Numeric(10, 2), default=0.00),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('features', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create user_subscriptions table
    op.create_table(
        'user_subscriptions',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('subscription_plan_id', sa.String(), nullable=False),
        
        # Usage tracking for different limits
        sa.Column('interviews_used', sa.Integer(), default=0),
        sa.Column('jobs_used', sa.Integer(), default=0),
        sa.Column('jd_used', sa.Integer(), default=0),
        sa.Column('resume_used', sa.Integer(), default=0),
        
        sa.Column('activated_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.ForeignKeyConstraint(['subscription_plan_id'], ['subscription_plans.id']),
        sa.UniqueConstraint('user_id')
    )
    
    # Create organization_subscriptions table
    op.create_table(
        'organization_subscriptions',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('organization_id', sa.String(), nullable=False),
        sa.Column('subscription_plan_id', sa.String(), nullable=False),
        
        # Usage tracking for different limits
        sa.Column('interviews_used', sa.Integer(), default=0),
        sa.Column('jobs_used', sa.Integer(), default=0),
        sa.Column('jd_used', sa.Integer(), default=0),
        sa.Column('candidate_suitability_used', sa.Integer(), default=0),
        
        sa.Column('activated_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id']),
        sa.ForeignKeyConstraint(['subscription_plan_id'], ['subscription_plans.id']),
        sa.UniqueConstraint('organization_id')
    )


def downgrade() -> None:
    """Drop subscription tables."""
    op.drop_table('organization_subscriptions')
    op.drop_table('user_subscriptions')
    op.drop_table('subscription_plans')
    
    # Drop enum
    plan_type_enum = postgresql.ENUM('individual', 'organization', name='plantype')
    plan_type_enum.drop(op.get_bind())
