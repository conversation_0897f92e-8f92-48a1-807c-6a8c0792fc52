"""updated interview table

Revision ID: e195369ad707
Revises: b2d3bc45e881
Create Date: 2025-04-01 17:46:29.001450

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e195369ad707'
down_revision: Union[str, None] = 'b2d3bc45e881'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('interviews')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('interviews',
    sa.Column('id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('organization_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('candidate_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('candidate_email', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('skill_set', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('job_role', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('experience_level', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('interview_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('interview_time', postgresql.TIME(), autoincrement=False, nullable=False),
    sa.Column('resume_link', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('jd_link', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_completed', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], name='interviews_organization_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='interviews_pkey')
    )
    # ### end Alembic commands ###
