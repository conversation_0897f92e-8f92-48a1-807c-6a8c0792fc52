"""create users and organizations tables

Revision ID: ae8b0ec85a86
Revises: 
Create Date: 2025-03-31 13:11:56.403434

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ae8b0ec85a86'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('organizations',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('owner_id', sa.String(), nullable=False),
    sa.Column('skill_sets', sa.JSON(), nullable=True),
    sa.Column('job_roles', sa.JSON(), nullable=True),
    sa.Column('is_setup_complete', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    sa.UniqueConstraint('owner_id')
    )
    op.add_column('users', sa.Column('is_organization_owner', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('organization_id', sa.String(), nullable=True))
    op.create_foreign_key(None, 'users', 'organizations', ['organization_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_column('users', 'organization_id')
    op.drop_column('users', 'is_organization_owner')
    op.drop_table('organizations')
    # ### end Alembic commands ###
