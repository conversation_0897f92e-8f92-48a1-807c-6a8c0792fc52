"""add_email_verification_fields

Revision ID: f6a22399580b
Revises: 53531df59029
Create Date: 2025-07-09 22:37:08.566873

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f6a22399580b'
down_revision: Union[str, None] = '53531df59029'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('job_postings', 'jd_link',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.add_column('organizations', sa.Column('is_email_verified', sa.Boolean(), nullable=True))
    op.add_column('organizations', sa.Column('email_verified_at', sa.DateTime(), nullable=True))
    op.add_column('otp_verifications', sa.Column('used_at', sa.DateTime(), nullable=True))
    op.drop_column('otp_verifications', 'organization_id')
    op.drop_column('otp_verifications', 'user_id')
    op.drop_index(op.f('idx_resumes_created_at'), table_name='resumes')
    op.drop_index(op.f('idx_resumes_user_id'), table_name='resumes')
    op.add_column('users', sa.Column('is_email_verified', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('email_verified_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'email_verified_at')
    op.drop_column('users', 'is_email_verified')
    op.create_index(op.f('idx_resumes_user_id'), 'resumes', ['user_id'], unique=False)
    op.create_index(op.f('idx_resumes_created_at'), 'resumes', ['created_at'], unique=False)
    op.add_column('otp_verifications', sa.Column('user_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('otp_verifications', sa.Column('organization_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('otp_verifications', 'used_at')
    op.drop_column('organizations', 'email_verified_at')
    op.drop_column('organizations', 'is_email_verified')
    op.alter_column('job_postings', 'jd_link',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    # ### end Alembic commands ###
