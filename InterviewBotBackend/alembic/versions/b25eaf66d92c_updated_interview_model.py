"""updated interview model

Revision ID: b25eaf66d92c
Revises: effc3df0a9fb
Create Date: 2025-04-22 15:50:39.632938
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "b25eaf66d92c"
down_revision = "effc3df0a9fb"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 1) create the ENUM type for scheduled_by_type
    scheduledby = postgresql.ENUM("organization", "user", name="scheduledby", create_type=True)
    scheduledby.create(op.get_bind(), checkfirst=True)

    # 2) add scheduled_by_type with a server default so old rows get 'organization'
    op.add_column(
        "interviews",
        sa.Column(
            "scheduled_by_type",
            sa.Enum("organization", "user", name="scheduledby"),
            nullable=False,
            server_default="organization",
        ),
    )

    # 3) add scheduled_by_user_id FK to users.id
    op.add_column("interviews", sa.Column("scheduled_by_user_id", sa.String(), nullable=True))
    op.create_foreign_key(
        "fk_interviews_scheduled_by_user", "interviews", "users", ["scheduled_by_user_id"], ["id"]
    )

    # 4) enforce XOR: if type=user, user_id NOT NULL; if org, user_id IS NULL
    op.create_check_constraint(
        "ck_interview_scheduled_by_xor",
        "interviews",
        "(scheduled_by_type='organization' AND scheduled_by_user_id IS NULL) "
        "OR (scheduled_by_type='user' AND scheduled_by_user_id IS NOT NULL)",
    )

    # 5) now that new rows will set it explicitly, drop the default if you like:
    op.alter_column("interviews", "scheduled_by_type", server_default=None)


def downgrade() -> None:
    # remove the CHECK
    op.drop_constraint("ck_interview_scheduled_by_xor", "interviews", type_="check")

    # drop the FK and column
    op.drop_constraint("fk_interviews_scheduled_by_user", "interviews", type_="foreignkey")
    op.drop_column("interviews", "scheduled_by_user_id")

    # drop the enum column
    op.drop_column("interviews", "scheduled_by_type")

    # drop the ENUM type
    scheduledby = postgresql.ENUM("organization", "user", name="scheduledby")
    scheduledby.drop(op.get_bind(), checkfirst=True)
