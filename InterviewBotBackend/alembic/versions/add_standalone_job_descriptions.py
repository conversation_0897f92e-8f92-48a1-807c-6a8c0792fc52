"""add standalone job descriptions

Revision ID: add_standalone_job_descriptions
Revises: add_job_descriptions_to_organization
Create Date: 2025-04-02 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_standalone_job_descriptions'
down_revision: Union[str, None] = 'add_job_descriptions_to_organization'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add standalone_job_descriptions column to organizations table
    op.add_column('organizations', sa.Column('standalone_job_descriptions', sa.JSON(), nullable=True))
    
    # Add jd_id column to interviews table
    op.add_column('interviews', sa.Column('jd_id', sa.String(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove standalone_job_descriptions column from organizations table
    op.drop_column('organizations', 'standalone_job_descriptions')
    
    # Remove jd_id column from interviews table
    op.drop_column('interviews', 'jd_id')
