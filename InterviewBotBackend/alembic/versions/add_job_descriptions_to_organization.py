"""add job descriptions to organization

Revision ID: add_job_descriptions_to_organization
Revises: db0adb923f39
Create Date: 2025-04-01 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_job_descriptions_to_organization'
down_revision: Union[str, None] = 'db0adb923f39'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('job_descriptions', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organizations', 'job_descriptions')
    # ### end Alembic commands ###
